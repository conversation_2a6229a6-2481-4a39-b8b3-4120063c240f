def branch = "${env.BRANCH_NAME}"
def tag = env.BRANCH_NAME.toUpperCase()
tag = "${tag}-${env.BUILD_NUMBER}"

def service = "ladder"

def imageDeploy = "test-mcr.mobio.vn/laddergroup/${service}"
def tagToDeploy = "${imageDeploy}:${tag}"
def tagToDeployLatest = "${imageDeploy}:latest"

def imageDeployRelease = "mcr.mobio.vn/laddergroup/${service}"
def tagToDeployRelease = "${imageDeployRelease}:${tag}"

def agentLabel
if (env.BRANCH_NAME.toUpperCase() == "DEVELOP") {
  agentLabel = "dev"
} else if (env.BRANCH_NAME.toUpperCase() == "HOTFIX" || env.BRANCH_NAME.toUpperCase() == "MASTER") {
  agentLabel = "uat-agent"
}

pipeline {
  agent { label "${agentLabel}" }
  stages {
    stage('Apply\nImage BASE') {
      steps {
        script {
          def filenameDockerfile = "Dockerfile"
          if (env.BRANCH_NAME.toUpperCase() == "MASTER" || env.BRANCH_NAME.toUpperCase() == "HOTFIX") {
            filenameDockerfile = "Dockerfile-release"
          }

          sh ("sed -i 's#BE_BASE_COMPILE_IMAGE#${env.BE_BASE_COMPILE_IMAGE}#g' ${filenameDockerfile}")
          def isReplaceCompileImageSuccess = sh(returnStdout: true, script: "grep -c '${env.BE_BASE_COMPILE_IMAGE}' ${filenameDockerfile}").trim()
          if (isReplaceCompileImageSuccess == 0)
            error ("Chua thay the duoc image BASE(compile)")

          sh ("sed -i 's#BE_BASE_RUN_IMAGE#${env.BE_BASE_RUN_IMAGE}#g' ${filenameDockerfile}")
          def isReplaceRunImageSuccess = sh(returnStdout: true, script: "grep -c '${env.BE_BASE_RUN_IMAGE}' ${filenameDockerfile}").trim()
          if (isReplaceRunImageSuccess == 0)
            error ("Chua thay the duoc image BASE(run)")
        }
      }
    }

    stage('Build image') {
      steps {
        script {
          if (env.BRANCH_NAME.toUpperCase() == "DEVELOP") {
            sh ("docker build -t ${tagToDeploy} -t ${tagToDeployLatest} .")
          } else if (env.BRANCH_NAME.toUpperCase() == "HOTFIX") {
            // get git branch name
            def git_branch_name = GIT_BRANCH.split('/').last().toUpperCase()
            tag = "${git_branch_name}-${env.BUILD_NUMBER}"
            tagToDeployRelease = "${imageDeployRelease}:${tag}"

            sh ("docker build -t ${tagToDeployRelease} -f Dockerfile-release .")

          } else if (env.BRANCH_NAME.toUpperCase() == "MASTER") {
              // build release
              sh ("docker build -t ${tagToDeployRelease} -f Dockerfile-release .")
          }
        }
      }
    }

    stage('Scan security') {
      steps {
        script {
          def tagName = "${tagToDeploy}"
          if (env.BRANCH_NAME.toUpperCase() == "MASTER" || env.BRANCH_NAME.toUpperCase() == "HOTFIX")
            tagName = "${tagToDeployRelease}"

          sh ("trivy -q image --exit-code 1 --format template --template \"@/contrib/html.tpl\" --output trivy_report.html --timeout 30m --ignore-unfixed --security-checks vuln --skip-update ${tagName}")
        }
      }
    }

    stage('Push image') {
      steps {
        script {
          if (env.BRANCH_NAME.toUpperCase() == "DEVELOP") {
            sh ("docker push ${tagToDeploy}")
            sh ("docker push ${tagToDeployLatest}")
          } else if (env.BRANCH_NAME.toUpperCase() == "MASTER" || env.BRANCH_NAME.toUpperCase() == "HOTFIX") {
            sh ("docker push ${tagToDeployRelease}")

            // commit tag
            sh("git tag ${tag}")
            sh("git push --tags")
          }
        }
      }
    }

    stage ('Run') {
      steps {
        script {
          if (env.BRANCH_NAME.toUpperCase() == "DEVELOP") {
            sh ("sh /jenkins/workspace/DevOps_script.sh")
            // apply các file yaml
            sh ("find ./yaml -name '*.yaml' -type f -exec sed -i 's#{image}#${tagToDeploy}#g' {} ';' -exec kubectl -n ladder apply -f {} ';'")
            def is_used = true
            def unused_pods = [
            ]
            if (is_used == false) {
              echo "Khong su dung"
              sh ("find ./yaml -name '*.yaml' -type f -exec kubectl scale --replicas=0 -f {} -n ladder ';'")
            }
            else {
              for(file in unused_pods) {
                sh ("find ./yaml -name 'file' -type f -exec kubectl scale --replicas=0 -f {} -n ladder ';'")
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      archiveArtifacts artifacts: "trivy_report.html", fingerprint: true

      publishHTML (target: [
        allowMissing: true,
        alwaysLinkToLastBuild: true,
        keepAll: true,
        reportDir: '.',
        reportFiles: 'trivy_report.html',
        reportName: 'Trivy Scan',
        ])
    }

    success {
        script {
            echo "Pipeline succeeded."
            def commitHash = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
            def commitMessage = sh(script: "git log -1 --pretty=%B ${commitHash}", returnStdout: true).trim()
            def commitAuthorName = sh(script: "git log -1 --pretty=format:'%an' ${commitHash}", returnStdout: true).trim()
            def commitAuthorEmail = sh(script: "git log -1 --pretty=format:'%ae' ${commitHash}", returnStdout: true).trim()
            def postUrl = 'https://open.larksuite.com/open-apis/bot/v2/hook/b153aeee-9753-4d38-a3ee-db6f7da39c5d'
            def postBody = """
            {
                "msg_type": "interactive",
                "card": {
                    "config": {
                        "wide_screen_mode": true
                    },
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": "Build done 🥳🥳🥳🥳\\nCommit: ${commitHash}\\nMessage: ${commitMessage}\\nAuthor: ${commitAuthorName} <${commitAuthorEmail}>",
                                "tag": "plain_text"
                            }
                        }
                    ],
                    "header": {
                        "template": "blue",
                        "title": {
                            "content": "Jenkins - Ladder",
                            "tag": "plain_text"
                        }
                    }
                }
            }
            """
            sh "curl -X POST -H 'Content-Type: application/json' -d '${postBody}' '${postUrl}'"
        }
    }

    failure {
        script {
            echo "Pipeline failed."
            def commitHash = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
            def commitMessage = sh(script: "git log -1 --pretty=%B ${commitHash}", returnStdout: true).trim()
            def commitAuthorName = sh(script: "git log -1 --pretty=format:'%an' ${commitHash}", returnStdout: true).trim()
            def commitAuthorEmail = sh(script: "git log -1 --pretty=format:'%ae' ${commitHash}", returnStdout: true).trim()
            def postUrl = 'https://open.larksuite.com/open-apis/bot/v2/hook/b153aeee-9753-4d38-a3ee-db6f7da39c5d'
            def postBody = """
            {
                "msg_type": "interactive",
                "card": {
                    "config": {
                        "wide_screen_mode": true
                    },
                    "elements": [
                        {
                            "tag": "div",
                            "text": {
                                "content": "Build failed 🤒🤒🤒🤒\\nCommit: ${commitHash}\\nMessage: ${commitMessage}\\nAuthor: ${commitAuthorName} <${commitAuthorEmail}>",
                                "tag": "plain_text"
                            }
                        }
                    ],
                    "header": {
                        "template": "blue",
                        "title": {
                            "content": "Jenkins - Ladder",
                            "tag": "plain_text"
                        }
                    }
                }
            }
            """
            sh "curl -X POST -H 'Content-Type: application/json' -d '${postBody}' '${postUrl}'"
        }
    }
  }
}