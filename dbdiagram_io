// Copy đoạn mã dưới đây vào trang: https://dbdiagram.io/

Table user {
  user_id uuid [pk]
  employee_code string [pk, note: '<PERSON><PERSON> nhân viên']
  first_name varchar(50) [note: '']
  middle_name varchar(50) [note: '']
  last_name varchar(50) [note: '']
  primary_email varchar(50) [note: '<PERSON><PERSON> chính sử dụng ở công ty']
  personal_email varchar(50) [note: 'Email cá nhân']
  username string [note:'Username đăng nhập']
  password string  [note: 'Mật khẩu']
  gender int [note: 'Kiểu giới tính: 1 = Nam 2 = Nữ 0 = Không xác định']
  marital_status int [
    note: 'Tình trạng hôn nhân: 1 = Đ<PERSON><PERSON> thân 2 = Đã kết hôn 3 = Ly hôn 4 = Khác']
  education_level string [
    note: 'Trình độ học vấn']
  lark_user_id string [note: "Id của nhân viên bên lark"]
  open_user_id string [note: "Open id của nhân viên bên lark"]
  leader_user_id string [note: "Id leader của nhân viên"]
  job_title_id string [ref: > job_title.job_title_id, note: "Id chức danh của nhân viên"]
  job_title_level_id string [ref: > job_title_level.job_title_level_id, note: "Id của level chức danh nhân viên"]
  employment_type_id int [ref: > employment_type.employment_type_id, note: 'Id loại hình làm việc']
  thumb_avatar_link string [note: "Link ảnh đại diện"]
  icon_avatar_link string [note: "Ảnh đại diện cỡ nhỏ"]
  home_town string [note: 'Quê quán']
  date_of_birth datetime [note: 'Ngày tháng năm sinh']
  current_address string [note: 'Địa chỉ hiện tại']
  phone_number string [note: 'Số điện thoại']
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']
  last_time_login datetime [note: 'Thời gian đăng nhập gần nhất']
  salary_amount float [note: 'Mức thu nhập tính theo tháng']
  start_salary datetime [note: 'Ngày bắt đầu mức lương']
  start_onboard_at datetime [note: 'Ngày bắt đầu onboard']
  contract_id string [ref: > contract.contract_id, note: 'Mã hợp đồng']
  status int [
    note: '1 = "hoạt động" -1 = "không hoạt động"']

  Note: '''
    Bảng quản lý thông tin user
  '''
}
//-----------------

Table user_department {
  user_department_id uuid [pk]
  user_id string [ref: > user.user_id, note: 'Id của nhân viên']
  department_id string [ref: > department.department_id, note: 'Id của phòng ban']
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
    Bảng lưu mối quan hệ của user và phòng ban
  '''
}
//-----------------

Table employment_type {
    employment_type_id uuid [pk]
    vi_name string [note: 'Tên tiếng việt của loại hình làm việc']
    en_name string [note: 'Tên tiếng anh của loại hình làm việc']
    description string [note: 'Mô tả loại hình làm việc']
    status int [note: "Trạng thái hoạt động"]
    created_by string [note: 'Người tạo']
    updated_by string [note: 'Người cập nhật']
    created_time datetime [note: 'Thời gian tạo']
    updated_time datetime [note: 'Thời gian cập nhật']
    
    Note: '''
        Bảng lưu thông tin loại hình làm việc
    '''
}
//-----------------


Table department {
    department_id uuid [pk]
    lark_department_id string [note: "Id dữ liệu phòng ban bên lark"]
    open_department_id string [note: "Open id của dữ liệu phòng ban bên lark"]
    display int [note: "Trạng thái hiển thị của phòng ban [1-được hiển thị, 0-không được hiển thị"]
    name string [note: 'Tên phòng ban']
    lower_case_name string [note: "Tên viết thường của phòng ban"]
    owners array [note: "Danh sách id người quản lí phòng ban"]

    //realation n-n  
    users User [note: "Danh sách nhân viên trong trong phòng ban"]

    created_by string [note: 'Người tạo']
    description string [note: 'Ghi chú']
    status int [
    note: '1 = "hoạt động" -1 = "không hoạt động"']
    updated_by string [note: 'Người cập nhật']
    created_time datetime [note: 'Thời gian tạo']
    updated_time datetime [note: 'Thời gian cập nhật']

    Note: '''
        Bảng lưu thông tin phòng ban
        Tương ứng với các team lớn trong công ty
    '''
}
//-----------------

Table role {
  role_id uuid [pk]
  name string [note: "Tên vai trò"]
  lower_case_name string [note: "Tên viết thường của vai trò"]
  description string [note: "Mô tả vai trò"]
  created_by string [note: 'Người tạo']
  status int [
  note: '1 = "hoạt động" -1 = "không hoạt động"']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
        Bảng lưu thông tin vai trò của nhân viên trong ladder
    '''
} 
//-----------------

Table role_permission {
  role_permission_id uuid [pk]
  role_id string [ref: > role.role_id, note: "Id của vai trò"]
  permission_id string [ref: > permission.permission_id, note: "Id của quyền hạn"]
  created_by string [note: 'Người tạo']
  status int [
  note: '1 = "hoạt động" -1 = "không hoạt động"']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
        Bảng lưu thông tin quan hệ giữ vai trò và quyền han
    '''
}
//-----------------

Table permission {
  permission_id uuid [pk]
  description string [note: "Mô tả quyền hạn"]
  action string [note: "Hành động được thao tác"]
  scope string [note: "Phạm vi thao tác, value hiện tại: all, organizational_chart, title_level, competency_dictionary, capacity_framework, evaluate, evaluate_result"]
  created_by string [note: 'Người tạo']
  status int [
  note: '1 = "hoạt động" -1 = "không hoạt động"']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
        Bảng lưu thông tin quyền hạn trong từng màn
    '''
}
//-----------------

Table job_title {
  job_title_id uuid [pk]
  department_id string [ref: > department.department_id, note: "Id phòng ban"]
  name string [note: "Tên chức danh"]
  lower_case_name string [note: "Tên viết thường của chức danh"]
  description string [note: "Mô tả chức danh"]
  created_by string [note: 'Người tạo']
  status int [
  note: '1 = "hoạt động" -1 = "không hoạt động"']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
        Bảng lưu thông tin chức danh của nhân viên
    '''
}
//-----------------

Table job_title_level {
  job_title_level_id uuid [pk]
  job_title_id string [ref: > job_title.job_title_id, note: "Id chức danh"]
  name string [note: "Tên level của chức danh"]
  level int [note: "Level"]
  lower_case_name string [note: "Tên viết thường của level chức danh"]
  created_by string [note: 'Người tạo']
  status int [
  note: '1 = "hoạt động" -1 = "không hoạt động"']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
        Bảng lưu thông tin level chức danh của nhân viên
    '''
}

////////////////Những bảng sau trong cấu trúc sql được define trước chưa được dùng /////////////////////

Table sub_department {
    sub_department_id uuid [pk]
    department_id string [ref: > department.department_id, note: 'Id của phòng ban chính']
    name string [note: 'Tên sub phòng ban']
    description string [note: 'Mô tả cho sub phòng ban']
    status int [
    note: '1 = "hoạt động" -1 = "không hoạt động"']
    created_by string [note: 'Người tạo']
    updated_by string [note: 'Người cập nhật']
    created_time datetime [note: 'Thời gian tạo']
    updated_time datetime [note: 'Thời gian cập nhật']

    Note: ''' 
        Bảng lưu thông tin của các phòng ban phụ
        Ví dụ: team BE Sale, BE Ticket, ...
    '''
}
//-----------------


Table user_history {
    history_id uuid [pk]
    user_id string [ref: > user.user_id, note: 'Mã nhân viên']
    before_data json [note: 'Dữ liệu trước khi thay đổi']
    after_data json [note: 'Dữ liệu sau khi thay đổi']
    created_by string [note: 'Người tạo']
    updated_by string [note: 'Người cập nhật']
    created_time datetime [note: 'Thời gian tạo']
    updated_time datetime [note: 'Thời gian cập nhật']
    
    Note: '''
        Lịch sử thao tác dữ liệu của User
    '''
}
//-----------------


Table user_event {
    user_event_id uuid [pk]
    user_id string [ref: > user.user_id, note: 'Mã nhân viên']
    body_event json [note: 'Dữ liệu event']
    event_type string [note: 'Loại event']
    created_by string [note: 'Người tạo']
    updated_by string [note: 'Người cập nhật']
    created_time datetime [note: 'Thời gian tạo']
    updated_time datetime [note: 'Thời gian cập nhật']

    Note: '''
        Danh sách các event của User
    '''

}
//-----------------

Table contract {
    contract_id uuid [pk]
    contract_number string [note: 'Số hợp đồng']
    status int [
    note: ' Trạng thái của hợp đồng: 1 = "có hiệu lực", -1 = "kết thúc"']
    contract_file_scan_link string [note: 'Link file hợp đồng scan']
    created_by string [note: 'Người tạo']
    updated_by string [note: 'Người cập nhật']
    created_time datetime [note: 'Thời gian tạo']
    updated_time datetime [note: 'Thời gian cập nhật'] 
}
//-----------------

Table user_identification {
    user_identification_id uuid [pk]
    user_id string [ref: > user.user_id, note: "Mã nhân viên"]
    type string [note: ' Loại giới tờ định danh: identification_card = "Căn cước công dân", passport = "Hộ chiếu"']
    identification_number string [note: 'Số giấy tờ định danh']
    issue_date datetime [note: 'Thời gian cấp giấy tờ ']
    issue_place string [
        note: "Nơi cấp giấy tờ định danh"]
    front_image string [
        note: "Hình ảnh mặt trước của giấy tờ định danh"]
    back_image string [
        note: "Hình ảnh mặt sau của giấy tờ định danh"]
    status int [
    note: '1 = "hoạt động" -1 = "không hoạt động"']
    created_by string [note: 'Người tạo']
    updated_by string [note: 'Người cập nhật']
    created_time datetime [note: 'Thời gian tạo']
    updated_time datetime [note: 'Thời gian cập nhật']

    Note: '''
        Bảng lưu thông tin giấy tờ định danh của Nhân viên
    '''
}
//-----------------

Table contact_info {
    contact_id uuid [pk]
    user_id string [ref: > user.user_id, note: 'Mã nhân viên']
    full_name string [note: 'Họ tên người liên hệ']
    email string [note: 'Email người liên hệ']
    phone string [note: 'Số điện thoại người liên hệ']
    address string [note: 'Địa chỉ người liên hệ']
    relationship_type string [
        note: 'Mối quan hệ Chồng/ Vợ Bố/ Mẹmft Người thân Bạn bè']
    status int [
        note: '1 = "hoạt động" -1 = "không hoạt động"']
    Note: '''
        Bảng lưu thông tin người liên hệ
    '''
}
//-----------------




/////////////////////////////Phần phía dưới sử dụng mongodb//////////////////////////////////
Table capacity_group {
  capacity_group_id uuid [pk]
  name string [note: "Tên nhóm năng lực"]
  description string [note: "Mô tả nhóm năng lực"]
  lower_case_name string [note: "Tên viết thường của nhóm năng lực"]
  is_default bool [note: "Nhóm năng lực mặc định hay người dùng tạo"]
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
        Bảng lưu thông tin của nhóm năng lực
    '''
}
//-----------------


Table capacity {
  capacity_id uuid [pk]
  name string [note: "Tên năng lực"]
  description string [note: "Mô tả năng lực"]
  lower_case_name string [note: "Tên viết thường của năng lực"]
  capacity_group_id int [note: "Mã id nhóm năng lực"]
  behaviors_expression array [note: "Danh sách biểu hiện hành vi"]
  job_title_id string [note: "Id chức danh"]
  job_title_level_id string [note: "Id level của chức danh"] 
  is_default bool [note: "Năng lực mặc định hay người dùng tạo"]
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
        Bảng lưu thông tin của năng lực
    '''
}

Table capacity__behaviors_expression {
  name string [note: "Tên cấp độ"]
  description string [note: "Mô tả biểu hiện hành vi theo từng cấp độ của năng lực"]
  level int [note: "Cấp độ của Hành vi [1-5]"]
  status int [note: "Trạng thái sử dụng"]
}
//-----------------


Table capacity_framework {
  capacity_framework_id uuid [pk]
  name string [note: "Tên khung năng lực"]
  department_id string [note: "Id của phòng ban áp dụng"]
  job_title_ids list [note: "Danh sách chức danh áp dụng cho khung năng lực này"]
  start_time datetime [note: "Ngày bắt đầu áp dụng khung năng lực"]
  end_time datetime [note: "Ngày kết thúc khung năng lực"]
  status int [note: "Trạng thái của khung năng lực 1-activate, 2-deactivate, 3-draft"]
  capacity_groups list [note: "Danh sách thông tin nhóm năng lực áp dụng"]
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']
}

Table capacity_framework__capacity_groups {
  id string [note: "Id nhóm năng lực"]
  name string [note: "Tên nhóm nămg lực"]
  lst_capacity string [note: "Danh sách thông tin năng lực trong nhóm"]
}

Table capacity_framework__capacity_groups__lst_capacity {
  id string [note: "Id của năng lực"]
  name stirng [note: "Tên năng lực"]
  weight int [note: "trọng số của năng lực"]
  behaviors_expressions array [note: "Danh sách biểu hiện hành vi và điểm"]
  job_title_levels  array [note: "danh sách điểm tương ứng với level"]
}

Table capacity_framework__lst_capacity__behaviors_expression {
  description string [note: "Mô tả biểu hiện hành vi theo từng cấp độ của năng lực"]
  level int [note: "level của năng lực, luôn cùng level với biểu hiện hành vi cha"]
  is_activate bool [note: "Có đang được sử dụng hay không"]
  point_min int [note: "Điểm tối thiểu"]
  point_max int [note: "Điểm tối đa"]
  point_mid int [note: "Điểm chuẩn"]
}

Table capacity_framework__lst_capacity__job_title_levels {
  job_title_level_id string [note: "Id của level"]
  behavior_expression_level int [note: "Level của biểu hiện hành vi trong danh sách behaviors_expression"]
}
//-----------------


Table evaluate_period {
  evaluate_period_id string [note: "Id của kì đánh giá"]
  start_time datetime [note: "Ngày bắt đầu của kì đánh giá"]
  end_time datetime [note: "Ngày kết thúc của kì đánh giá"]
  company_id string [note: "Id của công ty"]
  status int [note: "Trạng thái của kì đánh giá 1-activate, 0-deactivate"]
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
    Bảng lưu thông tin kì đánh giá
  '''
}
//----------------------

Table evaluate_time {
  evaluate_time_id string [note: "Id của thời điểm đánh giá"]
  company_id string [note: "Id của công ty"]
  evaluate_period_id string [note: "Id của kì đánh giá"]
  department_id string [note: 'Id phòng ban']
  times array [note: "Time đánh giá"]
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
    Bảng lưu thông tin thời gian đánh giá trong kì đánh giá của từng phòng ban
  '''
}

Table evaluate_time__times {
  start_time datetime [note: "Ngày bắt đánh giá"]
  end_time datetime [note: "Ngày kết thúc đánh giá"]
}
//-----------------------

Table evaluate {
  evaluate_id string [note: "Id của phiếu đánh giá"]
  evaluate_period_id string [note: "Id của kì đánh giá"]
  user_id string [note: "Id cúa user"]
  company_id string [note: "Id của công ty"]
  department_id string [note: "Id phòng ban"]
  before_job_title_level_id string [note: "Level chức danh trước đánh giá"]
  after_job_title_level_id string [note: "Level chức danh sau đánh giá"]
  start_time datetime [note: "Ngày bắt đầu của bả đánh giá"]
  end_time datetime [note: "Ngày kết thúc của đánh giá"]
  time_eval_of_users list [note: "Time quy định đánh giá của từng nhân viên"]
  capacity_framework_id string [note: "Id của khung năng lực"]
  capacity_groups list [note: "danh sách nhóm năng lực áp dụng"]
  amount_of_work_note string [note: "Khối lượng công việc"]
  work_quality_note string [note: "Chất lượng công việc"]
  user_note string [note: "Lưu ý của nhân viên"]
  hr_review string [note: "Đánh giá, đề xuất của hr"]
  leader_review string [note: "Đánh giá, đề xuất của quản lí"]
  work_process_note string [note: "Tiến độ công việc"]
  status int [note: "Trạng thái của bản đánh giá 1-waiting, 2-draft, 3-completed"]
  created_by string [note: 'Người tạo']
  updated_by string [note: 'Người cập nhật']
  created_time datetime [note: 'Thời gian tạo']
  updated_time datetime [note: 'Thời gian cập nhật']

  Note: '''
    Bảng lưu thông tin phiếu đánh giá của một cá nhân
  '''
}

Table evaluate__capacity_groups {
  capacity_group_id string [note: "Id nhóm năng lực"]
  name string [note: "Tên nhóm năng lực"]
  lst_capacity list [note: "Danh sách năng lực sử dụng"] 
}

Table evaluate__capacity_groups__lst_capacity {
  capacity_id string [note: "Id năng lực"]
  name string [note: "Tên năng lực"]
  points list [note: "Danh sách điểm đánh giá của nhân sự"]
}

Table evalute__time_eval_of_users {
  user_id string [note: "Id của nhân viên"]
  user_type int [note: "loại nhân viên 1-owner, 2-leader, 3-hr, 4-assign, 5-other"]
  start_time datetime [note: "Giờ bắt đầu của nhân sự được đánh giá"]
  end_time datetime [note: "Giờ kết thúc của nhân sự được đánh giá"]
  submit_time datetime [note: "Giờ nhấn gửi đi của nhân sự"]
}

Table evaluate__lst_capacity__points {
  user_id string [note: "Id của nhân viên"]
  user_type int [note: "loại nhân viên 1-owner, 2-ledder, 3-hr, 4-assign, 5-other"]
  point int [note: "Điểm đánh giá"]
}






