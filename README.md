# Ladder - <PERSON><PERSON> thống quản lý nhân sự

Hệ thống quản lý nhân sự toàn diện được xây dựng bằng FastAPI cho Mobio. Hệ thống cung cấp đánh giá hiệu suất nh<PERSON> viên, qu<PERSON><PERSON> lý khung năng lực, qu<PERSON><PERSON> lý cấu trúc tổ chức và tích hợp vớ<PERSON> (Feishu) để tự động hóa quy trình làm việc.

## Thông tin dự án

* API v1.0: http://localhost:8000/api/v1.0
* Swagger: http://localhost:8000/docs
* Alternative API docs: http://127.0.0.1:8000/redoc
* pgAdmin: http://localhost:9000/

## Kiến trúc hệ thống

### Tổng quan
Ladder được xây dựng theo kiến trúc microservices với các thành phần chính:
- **API Layer**: FastAPI với RESTful APIs
- **Business Logic**: Controllers và Services
- **Data Access**: Repository Pattern với PostgreSQL và MongoDB
- **Caching**: Redis cho cache và pub/sub messaging
- **Background Jobs**: Celery workers và cronjobs
- **Authentication**: JWT với hệ thống phân quyền AWS-style

### Luồng xử lý request (Request Flow)

Hệ thống Ladder tuân theo pattern layered architecture với luồng xử lý rõ ràng từ API layer xuống data layer:

```
HTTP Request → Router → Controller → Repository → Model → Database
            ↓          ↓           ↓            ↓       ↓
           API     Business    Data Access   Entity  Storage
          Layer     Logic       Layer        Layer   Layer
```

#### 1. **Routers** (API Layer)
- **Chức năng**: Định nghĩa API endpoints, xử lý HTTP requests/responses
- **Vị trí**: `src/routers/v1_0/`
- **Nhiệm vụ**:
  - Định nghĩa routes và HTTP methods
  - Validate request parameters
  - Authentication và authorization checks
  - Delegate business logic cho Controllers

```python
# Ví dụ: src/routers/v1_0/user_routers.py
@routers.post(
    path=URI.USER.USERS,
    dependencies=[Depends(check_authentication), Depends(can_list_users)],
    response_model=BaseResponsePagingNumberSchema,
)
async def get_users(request: Request, filters: UserFiltersRequestSchema, session: Session = Depends(get_db)):
    return await UserController().get_users(request, filters, session)
```

#### 2. **Controllers** (Business Logic Layer)
- **Chức năng**: Xử lý business logic, orchestrate các operations
- **Vị trí**: `src/controllers/`
- **Nhiệm vụ**:
  - Thực hiện business rules và validation
  - Coordinate giữa multiple repositories
  - Transform data giữa layers
  - Handle complex business workflows

```python
# Ví dụ: src/controllers/user_controller.py
class UserController(BaseController):
    async def get_users(self, request: Request, filters: UserFiltersRequestSchema, session: Session):
        # Business logic processing
        user_repository = UserRepository(session)
        department_repository = DepartmentRepository(session)
        
        # Coordinate multiple repositories
        users = await user_repository.get_users_with_filters(filters)
        departments = await department_repository.get_departments_by_user_ids([u.id for u in users])
        
        # Transform and return data
        return self.build_response(users, departments)
```

#### 3. **Repositories** (Data Access Layer)
- **Chức năng**: Trừu tượng hóa database operations, implement data access patterns
- **Vị trí**: `src/repositories/`
- **Nhiệm vụ**:
  - CRUD operations với database
  - Query optimization và filtering
  - Data mapping và transformation
  - Transaction management

```python
# Ví dụ: src/repositories/users_repository.py
class UserRepository(BaseRepository):
    async def get_one_by_primary_email(self, primary_email) -> Type[UserModel] | None:
        data = await self.get_one(UserModel, primary_email=primary_email)
        return data
    
    def add_user(self, data: UserInsertDataSchema) -> UserModel:
        data.employee_code = UserModel().generate_employee_code(self.db)
        data_insert = self.insert_one(UserModel, data_insert=data)
        return data_insert.to_dict(_hide=[UserModel.password.key])
```

#### 4. **Models** (Entity Layer)
- **Chức năng**: Định nghĩa data structure và database schema
- **Vị trí**: `src/models/postgres/`, `src/models/mongo/`
- **Nhiệm vụ**:
  - Define database tables/collections
  - Entity relationships và constraints
  - Data validation rules
  - Business entity behaviors

```python
# Ví dụ: src/models/postgres/user_model.py
class UserModel(BaseModel):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, default=generate_uuid)
    primary_email = Column(String, unique=True, nullable=False)
    full_name = Column(String, nullable=False)
    employee_code = Column(String, unique=True)
    
    # Relationships
    departments = relationship("UserDepartmentModel", back_populates="user")
```

#### 5. **Database Layer**
- **PostgreSQL**: Structured data (users, departments, roles, policies)
- **MongoDB**: Semi-structured data (competency frameworks, evaluations)
- **Redis**: Caching và session management

### Ưu điểm của pattern này:
- **Separation of Concerns**: Mỗi layer có trách nhiệm rõ ràng
- **Maintainability**: Dễ dàng maintain và extend
- **Testability**: Có thể test từng layer độc lập
- **Scalability**: Có thể scale từng component riêng biệt
- **Reusability**: Repository và Model có thể reuse cho nhiều controllers

### Công nghệ sử dụng
- **Backend Framework**: FastAPI (Python 3.11)
- **Databases**: 
  - PostgreSQL (chính) với SQLAlchemy 2.0.23
  - MongoDB (cho dữ liệu phi cấu trúc)
  - Redis (caching và messaging)
- **Authentication**: JWT (pyjwt) với Argon2 password hashing
- **Task Queue**: Celery 5.3.6
- **Data Processing**: Pandas, openpyxl
- **Migration**: Alembic
- **Container**: Docker và Kubernetes
- **External Integration**: Lark (Feishu) SDK

## Cấu trúc dự án

```
ladder/
├── configs/                              # Cấu hình ứng dụng
│   ├── config.py                         # Cấu hình chính
│   └── database.py                       # Cấu hình database
├── migrates/                             # Alembic database migrations
│   └── versions/                         # Các file migration
├── scripts/                              # Scripts tiện ích
│   ├── script_init_*.py                  # Scripts khởi tạo dữ liệu
│   └── script_update_*.py                # Scripts cập nhật dữ liệu
├── src/                                  # Mã nguồn chính
│   ├── app.py                            # Entry point chính
│   ├── auth/                             # Xác thực và phân quyền
│   │   ├── authentication.py             # Logic xác thực
│   │   ├── permissions.py                # Hệ thống phân quyền
│   │   └── encrypt.py                    # Mã hóa và JWT
│   ├── controllers/                      # Business logic controllers
│   │   ├── auth_controller.py            # Xác thực
│   │   ├── user_controller.py            # Quản lý người dùng
│   │   ├── company_controller.py         # Quản lý công ty
│   │   ├── department_controller.py      # Quản lý phòng ban
│   │   ├── competency_*.py               # Quản lý năng lực
│   │   ├── evaluate_*.py                 # Đánh giá hiệu suất
│   │   └── task_performance_*.py         # Quản lý task performance
│   ├── models/                           # Data models
│   │   ├── postgres/                     # PostgreSQL models (SQLAlchemy)
│   │   ├── mongo/                        # MongoDB models (PyMongo)
│   │   └── redis/                        # Redis models
│   ├── repositories/                     # Data access layer
│   │   ├── base_repository.py            # Base repository
│   │   └── *_repository.py               # Specific repositories
│   ├── routers/                          # API routes
│   │   ├── v1_0/                         # API v1.0 endpoints
│   │   └── login/                        # SSO routes
│   ├── schemas/                          # Pydantic schemas
│   │   └── pydantic/                     # Request/Response schemas
│   ├── cronjobs/                         # Background jobs
│   │   ├── handler_sync_*.py             # Sync jobs với Lark
│   │   └── handler_system_*.py           # System jobs
│   ├── redis/                            # Redis services
│   │   ├── caching/                      # Caching layer
│   │   └── pubsub/                       # Pub/sub messaging
│   ├── libs/                             # External libraries
│   │   └── lark_docs_sdk/                # Lark integration SDK
│   ├── middleware/                       # Custom middleware
│   │   ├── authorization.py              # Authorization middleware
│   │   └── handle_response.py            # Response handler
│   ├── common/                           # Common utilities
│   │   ├── constants.py                  # Constants
│   │   ├── choices.py                    # Enum choices
│   │   └── custom_exception.py           # Custom exceptions
│   └── utils/                            # Utility functions
│       ├── logging.py                    # Logging utilities
│       └── time_helper.py                # Time utilities
├── start_subscribers/                    # Redis pub/sub subscribers
├── start_cronjobs.py                     # Cronjobs entry point
├── static/                               # Static files (admin UI)
├── yaml/                                 # Kubernetes deployment configs
└── app_web.py                            # FastAPI application entry point
```

## Các tính năng chính

### 1. Quản lý người dùng và phân quyền
- Xác thực qua JWT và Lark SSO
- Hệ thống phân quyền AWS-style với Policy Evaluator
- Quản lý vai trò (Role) và chính sách (Policy)
- Middleware authorization tự động

### 2. Quản lý cấu trúc tổ chức
- Quản lý công ty, phòng ban, chức danh
- Đồng bộ dữ liệu với Lark (Feishu)
- Cấu trúc tổ chức phân cấp

### 3. Quản lý năng lực (Competency Framework)
- Khung năng lực tổ chức
- Nhóm năng lực (Competency Groups)
- Đánh giá năng lực cá nhân

### 4. Đánh giá hiệu suất
- Chu kỳ đánh giá (Evaluate Periods)
- Quản lý quá trình đánh giá
- Báo cáo và thống kê

### 5. Tích hợp Lark
- Đồng bộ dữ liệu người dùng và phòng ban
- Gửi thông báo qua Lark Bot
- Quản lý tài liệu và template

## Hệ thống phân quyền

Ladder sử dụng hệ thống phân quyền theo mô hình AWS với các thành phần:

```
User ──┐
       ├── UserPolicy ──┐
       └── UserRole ────┤
                        ├── Policy (JSON Document)
           RolePolicy ──┘
```

### Các thành phần chính:
- **Policy Evaluator**: Đánh giá quyền truy cập
- **Authorization Middleware**: Kiểm tra quyền cho mỗi request
- **Permission Decorators**: Khai báo quyền cần thiết cho API endpoint

## Databases

### PostgreSQL (Database chính)
- Lưu trữ dữ liệu có cấu trúc: users, departments, roles, policies
- Sử dụng SQLAlchemy ORM với Repository Pattern
- Migration với Alembic

### MongoDB 
- Lưu trữ dữ liệu phi cấu trúc: competency frameworks, evaluations
- Tích hợp với PyMongo
- Flexible schema cho các module mở rộng

### Redis
- Caching với LRU cache
- Pub/Sub messaging cho background tasks
- Session storage

## Background Jobs

### Cronjobs
- Đồng bộ dữ liệu từ Lark
- Tự động tạo đánh giá theo chu kỳ
- Gửi thông báo định kỳ
- Cập nhật trạng thái hệ thống

### Redis Pub/Sub
- Xử lý gửi tin nhắn Lark Bot
- Đồng bộ dữ liệu realtime
- Background task processing

## Hướng dẫn chạy dự án

### 1. Chạy với Docker (Khuyến nghị)
```bash
# Copy file cấu hình
cp docker-compose.example.yml docker-compose.yml

# Chạy PostgreSQL và pgAdmin
docker compose up -d --build

# Chạy ứng dụng
python app_web.py
```

### 2. Cấu hình môi trường
```bash
# Copy file environment
cp tungdd.env .env

# Cài đặt dependencies
pip install -r requirements.txt
```

### 3. Database Migration
```bash
# Tạo migration mới
PYTHONPATH=./ python3.11 alembic_cmd.py revision --autogenerate -m "migration_message"

# Áp dụng migration
PYTHONPATH=./ python3.11 alembic_cmd.py upgrade head

# Rollback migration
PYTHONPATH=./ python3.11 alembic_cmd.py downgrade -1
```

### 4. Khởi tạo dữ liệu
```bash
# Khởi tạo dữ liệu cơ bản
python scripts/script_init_role_and_permission.py
python scripts/script_init_data_from_lark.py

# Khởi tạo settings
python scripts/script_init_setting.py
```

### 5. Chạy Background Services
```bash
# Chạy cronjobs
python start_cronjobs.py

# Chạy Redis subscribers
python start_subscribers/start_send_bot_message_subscriber.py
python start_subscribers/start_sync_bitable_competency_framework_subscriber.py
```

## API Documentation

Khi ứng dụng đang chạy, truy cập:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **API Base**: http://localhost:8000/api/v1.0

## Triển khai Production

Sử dụng Kubernetes với các file cấu hình trong thư mục `yaml/`:
- `deploy_ladder_api.yaml`: Main API deployment
- `cronjobs/`: Cronjob deployments
- `subscribers/`: Redis subscriber deployments

## Monitoring và Logging

- Sử dụng Mobio logging framework
- Log files trong `monitor_logs/`
- Health check endpoints
- Performance monitoring với Redis metrics