#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" 
    Author: tungdd
    Date created: 05/01/2024
"""

from alembic.config import CommandLine, Config


class CustomCommandLine(CommandLine):
    def run(self, argv=None):
        options = self.parser.parse_args(argv)
        if not hasattr(options, "cmd"):
            self.parser.error("too few arguments")
        else:
            cfg = Config(
                file_="alembic.ini",
                ini_section=options.name,
                cmd_opts=options,
                config_args={},
            )
            self.run_cmd(cfg, options)


if __name__ == "__main__":
    CustomCommandLine().run()
