import openpyxl
from openpyxl.utils.cell import get_column_letter


def analyze_excel_file(file_path):
    wb = openpyxl.load_workbook(file_path)
    ws = wb.active

    print("Sheet name:", ws.title)
    print("Max row:", ws.max_row)
    print("Max column:", ws.max_column)

    # Print content of first 10 rows
    for row in range(1, min(10, ws.max_row + 1)):
        row_data = []
        for col in range(1, min(10, ws.max_column + 1)):
            cell = ws.cell(row=row, column=col)
            row_data.append(str(cell.value))
        print(f"Row {row}:", row_data)

    print("\nCell styles and formats:")
    for row in range(1, min(10, ws.max_row + 1)):
        for col in range(1, min(10, ws.max_column + 1)):
            cell = ws.cell(row=row, column=col)
            if cell.fill and cell.fill.start_color and cell.fill.start_color.index != "00000000":
                print(
                    f"Cell {get_column_letter(col)}{row} fill color: {cell.fill.start_color.rgb} (type: {cell.fill.fill_type})"
                )
            if cell.font and cell.font.bold:
                print(f"Cell {get_column_letter(col)}{row} has bold font")
            if cell.alignment and (cell.alignment.horizontal or cell.alignment.vertical):
                print(
                    f"Cell {get_column_letter(col)}{row} alignment: horizontal={cell.alignment.horizontal}, vertical={cell.alignment.vertical}"
                )


if __name__ == "__main__":
    analyze_excel_file("/Users/<USER>/workspace/mobio/ladder/x1.xlsx")
