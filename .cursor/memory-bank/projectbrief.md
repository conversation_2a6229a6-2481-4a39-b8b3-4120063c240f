# Ladder - Dự án quản lý nhân sự

## Tổng quan dự án
Ladder là hệ thống quản lý nhân sự của Mobio. Dự án cung cấp các API để quản lý thông tin nhân sự và các chức năng liên quan.

## Mục tiêu chính
- Xây dựng hệ thống quản lý nhân sự toàn diện
- Cung cấp API dễ sử dụng và hiệu quả
- Tích hợp với các hệ thống khác của Mobio

## Phạm vi dự án
- Quản lý thông tin nhân viên
- Quản lý cấu trúc tổ chức
- Quản lý quyền và phân quyền

## Yêu cầu kỹ thuật
- Hệ thống phát triển dựa trên FastAPI
- Sử dụng PostgreSQL làm cơ sở dữ liệu
- Quản lý migration với Alembic
- <PERSON><PERSON> thể triển khai bằng Docker

## Endpoint
- API v1.0: http://localhost:8000/api/v1.0
- Swagger: http://localhost:8000/docs
- Alternative API docs: http://127.0.0.1:8000/redoc
- pgAdmin: http://localhost:9000/ 