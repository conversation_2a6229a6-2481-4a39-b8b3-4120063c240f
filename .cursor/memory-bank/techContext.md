# Bối cảnh kỹ thuật Ladder

## Công nghệ sử dụng
- **Backend**: FastAPI (Python)
- **Database**: PostgreSQL
- **ORM**: SQLAlchemy, SQLModel
- **Migration**: Alembic
- **Authentication**: JWT (pyjwt)
- **Password Hashing**: Argon2 (argon2-cffi)
- **Task Queue**: Celery
- **Caching**: Redis
- **Data Processing**: Pandas, openpyxl
- **Container**: Docker
- **CI/CD**: Jenkins (k8s-Jenkinsfile)

## Môi trường phát triển
- Python 3.11
- Docker và Docker Compose cho môi trường local
- Cần cấu hình .env (từ .env.example)

## Cấu trúc dự án
```
Ladder/
├── configs/                  # C<PERSON>u hình (database, ...)
├── migrates/                 # Alembic script_location
├── src/                      # Mã nguồn chính
│   ├── app.py                # Entry point
│   ├── controllers/          # Các controller
│   ├── cronjobs/             # <PERSON><PERSON>c công việc định kỳ
│   ├── auth/                 # <PERSON><PERSON><PERSON> thực
│   ├── common/               # Hàm và lớp dùng chung
│   ├── database/             # Kết nối và cấu hình database
│   ├── helpers/              # Hàm trợ giúp
│   ├── libs/                 # Thư viện
│   ├── middleware/           # Middleware
│   ├── models/               # Mô hình dữ liệu
│   ├── repositories/         # Repository pattern
│   ├── routers/              # API routes
│   ├── schemas/              # Pydantic schemas
│   ├── utils/                # Công cụ
│   ├── redis/                # Redis client
│   └── celery/               # Celery workers
└── static/                   # Tài nguyên tĩnh
```

## Các ràng buộc kỹ thuật
- Tương thích với phiên bản pydantic < 2.0
- Sử dụng SQLAlchemy 2.0.23
- Cần cấu hình PostgreSQL và Redis

## Dependencies chính
- FastAPI cho API framework
- SQLAlchemy và SQLModel cho ORM
- Pydantic cho validation
- Alembic cho database migration
- Celery cho task queue
- Redis cho caching
- JWT cho authentication
- Các thư viện Mobio internal (m-singleton, m-schedule, m-formatter-logging, m-caching) 