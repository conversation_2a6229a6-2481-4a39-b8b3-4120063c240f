# Mô hình hệ thống Ladder

## Kiến trúc hệ thống
- Ứng dụng được xây dựng theo mô hình API-first với FastAPI
- Sử dụng kiến trúc layer cho các thành phần backend
- PostgreSQL làm cơ sở dữ liệu chính
- Redis cho caching và messaging
- Celery cho task queue và background jobs

## Quyết định kỹ thuật chính
- Sử dụng Repository Pattern để tách biệt logic truy cập dữ liệu
- Dependency Injection thông qua FastAPI dependencies
- Chuẩn hóa lỗi với middleware và exception handler
- Quản lý migration tự động với Alembic
- Containerization với Docker cho dễ dàng triển khai

## Mô hình phân quyền
Hệ thống sử dụng mô hình phân quyền phong cách AWS với các thành phần:

- **Policy Evaluator**: <PERSON><PERSON><PERSON> giá nếu người dùng có quyền thực hiện action trên resource
- **Authorization Middleware**: Chặn và xác thực quyền của request
- **Permission Decorators**: Decorator cho API endpoint để xác định quyền cần thiết

Mô hình dữ liệu phân quyền:
- User có nhiều UserPolicy và UserRole
- Role có nhiều RolePolicy 
- Policy lưu trữ dưới dạng JSON document

## Mối quan hệ giữa các thành phần

- **Controllers**: Xử lý logic nghiệp vụ và điều phối các thành phần khác
- **Repositories**: Truy cập và thao tác với dữ liệu
- **Models**: Định nghĩa cấu trúc dữ liệu trong database
- **Schemas**: Định nghĩa cấu trúc dữ liệu cho API (Pydantic)
- **Routers**: Định nghĩa các endpoint API
- **Middleware**: Xử lý request/response trước và sau khi xử lý chính
- **Cronjobs**: Các tác vụ định kỳ

## Luồng xử lý chính
1. Client gửi request đến API
2. Middleware xác thực và kiểm tra quyền
3. Router điều hướng đến controller phù hợp
4. Controller xử lý business logic
5. Repository thực hiện truy vấn/cập nhật database
6. Controller trả về kết quả
7. Middleware định dạng response
8. Response trả về client 