# Dự án Ladder - <PERSON>uy tắc và thông tin dự án

## Quy tắc chung
- Luôn sử dụng pnpm và pnpx thay vì npm và npx
- Tạo file đúng vị trí, không tạo trong thư mục không liên quan
- <PERSON><PERSON><PERSON><PERSON> tạo comments trong mã nguồn nếu không cần thiết
- Tách biệt dữ liệu giả và mã nguồn
- G<PERSON><PERSON> mã nguồn sạch, loại bỏ mã dư thừa
- Sử dụng giải pháp tốt nhất và best practices
- Tổng kết những việc đã làm và chưa làm khi kết thúc nhiệm vụ

## Cấu trúc dự án
Dự án Ladder là hệ thống quản lý nhân sự của <PERSON>, sử dụng FastAPI (Python), PostgreSQL và các công nghệ hiện đạ<PERSON> kh<PERSON>. Cấu trúc chính gồm:
- `src/`: <PERSON>ã nguồn chính
  - <PERSON><PERSON><PERSON> thư mục theo chức năng: controllers, models, repositories, ...
- `configs/`: Cấu hình
- `migrates/`: Alembic migrations
- `static/`: Tài nguyên tĩnh

## Quy tắc phát triển
- Sử dụng Repository Pattern cho truy cập dữ liệu
- Áp dụng Dependency Injection qua FastAPI dependencies
- Tuân thủ mô hình phân quyền dựa trên AWS style
- Sử dụng Alembic cho migrations
- Luôn đảm bảo có tests cho chức năng mới

## Ghi chú quan trọng
- Phiên bản pydantic phải < 2.0
- SQLAlchemy 2.0.23 là phiên bản được sử dụng
- Cần cấu hình .env từ .env.example
- Có thể dùng Docker Compose để chạy môi trường local 