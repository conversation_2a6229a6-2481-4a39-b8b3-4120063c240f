import asyncio
from datetime import datetime

from mobio.libs.logging import <PERSON><PERSON>Logging
from sqlalchemy.orm import Session

from configs.database import get_db
from lark.api.company_api import CompanyApi
from src.common.common import CommonKey, EvaluatePeriodKey, EvaluateTimeKey
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.evaluate_time_model import EvaluateTimeModel
from src.repositories.company_repository import CompanyRepository
from src.repositories.department_repository import DepartmentRepository
from src.utils.time_helper import get_time_now

evaluate_periods = [
    {
        EvaluatePeriodKey.START_TIME: datetime(2024, 1, 1, 0, 0, 0),
        EvaluatePeriodKey.END_TIME: datetime(2024, 9, 30, 23, 59, 59),
        EvaluatePeriodKey.NAME: "<PERSON><PERSON> đ<PERSON>h giá tháng 6",
        EvaluateTimeKey.TIMES: [
            {
                EvaluateTimeKey.START_TIME: datetime(2024, 6, 1, 0, 0, 0),
                EvaluateTimeKey.END_TIME: datetime(2024, 6, 30, 23, 59, 59),
            },
            {
                EvaluateTimeKey.START_TIME: datetime(2024, 7, 1, 0, 0, 0),
                EvaluateTimeKey.END_TIME: datetime(2024, 7, 31, 23, 59, 59),
            },
            {
                EvaluateTimeKey.START_TIME: datetime(2024, 8, 1, 0, 0, 0),
                EvaluateTimeKey.END_TIME: datetime(2024, 8, 31, 23, 59, 59),
            },
            {
                EvaluateTimeKey.START_TIME: datetime(2024, 9, 1, 0, 0, 0),
                EvaluateTimeKey.END_TIME: datetime(2024, 9, 30, 23, 59, 59),
            },
            {
                EvaluateTimeKey.START_TIME: datetime(2024, 10, 1, 0, 0, 0),
                EvaluateTimeKey.END_TIME: datetime(2024, 10, 31, 23, 59, 59),
            },
        ],
    },
    {
        EvaluatePeriodKey.START_TIME: datetime(2024, 11, 1, 0, 0, 0),
        EvaluatePeriodKey.END_TIME: datetime(2025, 3, 31, 23, 59, 59),
        EvaluatePeriodKey.NAME: "Kì đánh giá tháng 12",
        EvaluateTimeKey.TIMES: [
            {
                EvaluateTimeKey.START_TIME: datetime(2024, 12, 1, 0, 0, 0),
                EvaluateTimeKey.END_TIME: datetime(2024, 12, 31, 23, 59, 59),
            }
        ],
    },
]


async def init_evaluate_period_and_evaluate_time():
    session_db: Session = next(get_db())

    company_api: CompanyApi = CompanyApi()
    evaluate_period_model = EvaluatePeriodModel()
    evaluate_time_model = EvaluateTimeModel()
    company_repo: CompanyRepository = CompanyRepository(session_db)
    department_repo: DepartmentRepository = DepartmentRepository(session_db)

    company_info_of_lark = await company_api.get_company_information()
    company = await company_repo.get_company(tenant_key=company_info_of_lark.tenant_key)
    MobioLogging().info(f"init_evaluate_period_and_evaluate_time :: company :: {company.name}")

    time_now = get_time_now()
    created_by = "admin"

    departments, _ = await department_repo.get_departments(company_id=company.company_id, page=-1)

    for evaluate_period_data in evaluate_periods:
        evaluate_period_id = ""
        times = evaluate_period_data.get(EvaluateTimeKey.TIMES)

        evaluate_period = await evaluate_period_model.get_evaluate_period_by_time(
            company.company_id,
            evaluate_period_data[EvaluatePeriodKey.START_TIME],
            evaluate_period_data[EvaluatePeriodKey.END_TIME],
        )
        if evaluate_period:
            evaluate_period_id = str(evaluate_period.get(EvaluatePeriodKey.ID))

            MobioLogging().debug(
                f"init_evaluate_period_and_evaluate_time :: evaluate_period :: {evaluate_period} :: already_exists"
            )

        else:
            start_time = evaluate_period_data.get(EvaluatePeriodKey.START_TIME)
            end_time = evaluate_period_data.get(EvaluatePeriodKey.END_TIME)
            name = evaluate_period_data.get(EvaluatePeriodKey.NAME)

            evaluate_period_data_insert = {
                EvaluatePeriodKey.START_TIME: start_time,
                EvaluatePeriodKey.END_TIME: end_time,
                EvaluatePeriodKey.NAME: name,
                CommonKey.COMPANY_ID: company.company_id,
                CommonKey.CREATED_TIME: time_now,
                CommonKey.UPDATED_TIME: time_now,
                CommonKey.CREATED_BY: created_by,
                CommonKey.UPDATED_BY: created_by,
                EvaluatePeriodKey.STATUS: 1,
            }
            evaluate_period_id = await evaluate_period_model.insert_evaluate_period(evaluate_period_data_insert)
            MobioLogging().debug(
                f"init_evaluate_period_and_evaluate_time :: evaluate_period :: {evaluate_period_data} :: created"
            )

        for department in departments:
            evaluate_time_for_department = await evaluate_time_model.get_evaluate_time_by_department_id(
                company.company_id, department.department_id, time_now
            )

            if evaluate_time_for_department:
                evaluate_time_id = evaluate_time_for_department.get(EvaluateTimeKey.ID)

                await evaluate_time_model.update_by_set(
                    {
                        EvaluateTimeKey.ID: evaluate_time_id,
                    },
                    {
                        EvaluateTimeKey.TIMES: times,
                    },
                )
                MobioLogging().debug(
                    f"init_evaluate_period_and_evaluate_time :: evaluate_time :: {evaluate_time_for_department} :: updated"
                )
            else:
                evaluate_time_data = {
                    EvaluateTimeKey.EVALUATE_PERIOD_ID: evaluate_period_id,
                    EvaluateTimeKey.DEPARTMENT_ID: department.department_id,
                    CommonKey.CREATED_TIME: time_now,
                    CommonKey.UPDATED_TIME: time_now,
                    CommonKey.CREATED_BY: created_by,
                    CommonKey.UPDATED_BY: created_by,
                    EvaluatePeriodKey.STATUS: 1,
                    EvaluateTimeKey.TIMES: times,
                    CommonKey.COMPANY_ID: company.company_id,
                }
                await evaluate_time_model.insert(evaluate_time_data)
                MobioLogging().debug(
                    f"init_evaluate_period_and_evaluate_time :: evaluate_time :: {evaluate_time_data} :: created"
                )


if __name__ == "__main__":
    asyncio.run(init_evaluate_period_and_evaluate_time(), debug=True)
