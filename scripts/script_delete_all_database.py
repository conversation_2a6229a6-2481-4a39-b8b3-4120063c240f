import psycopg2
from psycopg2 import sql

# Database connection parameters
DB_NAME = "your_database_name"
DB_USER = "your_database_user"
DB_PASSWORD = "your_password"
DB_HOST = "localhost"  # or your host
DB_PORT = "5432"       # default PostgreSQL port

def terminate_blocking_sessions():
    """Terminate any session that is blocking table operations."""
    try:
        conn = psycopg2.connect(
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT
        )
        conn.autocommit = True
        cursor = conn.cursor()

        # Query to find and terminate blocking transactions
        cursor.execute("""
            SELECT pg_terminate_backend(pg_stat_activity.pid)
            FROM pg_stat_activity
            WHERE pg_stat_activity.datname = %s
            AND pid <> pg_backend_pid()  -- do not kill your own session
            AND state IN ('idle in transaction', 'active');  -- target idle or active transactions
        """, (DB_NAME,))

        print(f"Terminated blocking sessions for database {DB_NAME}.")

    except Exception as e:
        print(f"Error terminating sessions: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def drop_all_tables():
    try:
        conn = psycopg2.connect(
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT
        )
        conn.autocommit = True

        cursor = conn.cursor()

        # Get the list of all tables in the public schema
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public';
        """)

        tables = cursor.fetchall()

        if not tables:
            print("No tables found to drop.")
        else:
            for table in tables:
                table_name = table[0]
                print(f"Dropping table: {table_name}")

                # Safely drop the table using SQL identifier
                drop_table_query = sql.SQL("DROP TABLE IF EXISTS {} CASCADE").format(sql.Identifier(table_name))
                cursor.execute(drop_table_query)

            print("All tables dropped successfully.")

    except Exception as e:
        print(f"Error: {e}")

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    # First, terminate any blocking processes
    terminate_blocking_sessions()

    # Then, attempt to drop all tables
    drop_all_tables()