#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 16/09/2024
"""

import asyncio

from bson import ObjectId

from src.models.mongo.competency_framework_model import CompetencyFrameworkModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.evaluate_time_model import EvaluateTimeModel


async def update_status_to_draft():
    competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
    await competency_framework_model.update_by_set(
        {"_id": {"$in": [ObjectId("66e676ae2cb400eb1a5a180e"), ObjectId("66e7d8891658fa7c12ed1b83")]}}, {"status": 3}
    )


async def delete_evaluate_period():
    evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
    await evaluate_period_model.delete_many(
        {
            "competency_framework_id": {
                "$in": [ObjectId("66e127d2cade282c06591193"), ObjectId("66e127d1cade282c06591181")]
            }
        }
    )

    evaluate_time_model: EvaluateTimeModel = EvaluateTimeModel()
    await evaluate_time_model.delete_many(
        {"evaluate_period_id": {"$in": [ObjectId("66e127d2cade282c06591193"), ObjectId("66e127d1cade282c06591181")]}}
    )


if __name__ == "__main__":
    asyncio.run(main=delete_evaluate_period(), debug=True)
