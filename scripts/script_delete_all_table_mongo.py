#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/09/2024
"""

import asyncio

from src.models.mongo.competency_framework_model import CompetencyFrameworkModel
from src.models.mongo.competency_group_model import CompetencyGroupModel
from src.models.mongo.competency_model import CompetencyModel
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.evaluate_time_model import EvaluateTimeModel
from src.models.mongo.folder_model import FolderModel
from src.models.mongo.lark_docs_template_model import LarkDocsTemplateModel
from src.models.mongo.sync_lark_bitable_mapping import SyncLarkBitableMapping


async def delete():
    await CompetencyFrameworkModel().delete_many({})
    await CompetencyGroupModel().delete_many({})
    await CompetencyModel().delete_many({})
    await EvaluateModel().delete_many({})
    await EvaluatePeriodModel().delete_many({})
    await EvaluateTimeModel().delete_many({})
    await FolderModel().delete_many({})
    await LarkDocsTemplateModel().delete_many({})
    await SyncLarkBitableMapping().delete_many({})


if __name__ == "__main__":
    asyncio.run(delete(), debug=True)
