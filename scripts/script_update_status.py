#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/11/2024
"""

import asyncio

from src.common.choices import EvaluateStatusChoice
from src.common.common import EvaluateKey
from src.models.mongo.evaluate_model import EvaluateModel


async def update_status():
    evaluate_model: EvaluateModel = EvaluateModel()
    for evaluate_id in ["66fcf9b56453183e3e22d935", "66fcf9b56453183e3e22d934"]:
        data_update = {EvaluateKey.STATUS: EvaluateStatusChoice.COMPLETED.value, EvaluateKey.DISABLE_EDIT: True}
        await evaluate_model.update_evaluate(evaluate_id, data_update)


if __name__ == "__main__":
    asyncio.run(update_status(), debug=True)
