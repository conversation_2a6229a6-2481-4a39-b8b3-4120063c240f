#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 30/05/2025
"""

import asyncio

from configs.database import get_db
from src.common.choices import (
    EvaluateStatusFilterChoice,
    StatusChoice,
    UserEvaluateTypeChoice,
)
from src.common.common import <PERSON><PERSON><PERSON><PERSON><PERSON>, JobLevelStatusKey
from src.models.mongo.evaluate_model import EvaluateModel
from src.repositories.users_repository import UserRepository


async def calculate_point_after_job_level_id(competency_groups, before_job_title_level_id):

    job_title_level_user_evaluate = None
    job_title_level_leader_evaluate = None
    job_title_level_status = None

    mapping_job_title_point = {}
    user_points = 0
    leader_points = 0
    points_job_title_level_current = 0
    for competency_group in competency_groups:
        for competency in competency_group.get("lst_competency"):
            for job_title_level in competency.get("job_title_levels"):
                job_title_level_id = job_title_level.get("job_title_level_id")
                behavior_expression_level = job_title_level.get("behavior_expression_level")
                if behavior_expression_level == -1:
                    continue
                if mapping_job_title_point.get(job_title_level_id):
                    mapping_job_title_point[job_title_level_id] += behavior_expression_level
                else:
                    mapping_job_title_point[job_title_level_id] = behavior_expression_level

            # tính điểm của nhân viên
            points = competency.get("points", [])
            for point in points:
                user_type = point.get("user_type")
                point_value = point.get("point", 0)
                if user_type == UserEvaluateTypeChoice.OWNER.value:
                    user_points += point_value
                elif user_type == UserEvaluateTypeChoice.LEADER.value:
                    leader_points += point_value

    # Sort mapping_job_title_point theo value giảm dần
    mapping_job_title_point = dict(sorted(mapping_job_title_point.items(), key=lambda item: item[1], reverse=False))

    for job_title_level_id, point_value in mapping_job_title_point.items():
        if job_title_level_id == before_job_title_level_id:
            points_job_title_level_current = point_value
        if user_points >= point_value:
            job_title_level_user_evaluate = job_title_level_id
        if leader_points >= point_value:
            job_title_level_leader_evaluate = job_title_level_id
    if leader_points > points_job_title_level_current:
        job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_INCREASE
    elif leader_points < points_job_title_level_current:
        job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_DECREASE
    else:
        job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_SAME

    return job_title_level_user_evaluate, job_title_level_leader_evaluate, job_title_level_status


async def main():
    evaluate_model = EvaluateModel()
    evaluates = await evaluate_model.find({})
    for evaluate in evaluates:
        evaluate_id = evaluate.get("_id")
        if str(evaluate_id) != "6840d5e9a6a5a1f2239e7a9d":
            continue
        before_job_title_level_id = evaluate.get("before_job_title_level_id")
        competency_groups = evaluate.get("competency_groups")

        data_update = {}
        user_owner_id = None
        time_eval_of_users = evaluate.get("time_eval_of_users")
        for time_eval_of_user in time_eval_of_users:
            user_type = time_eval_of_user.get("user_type")
            submit_time = time_eval_of_user.get("submit_time")
            if user_type == UserEvaluateTypeChoice.OWNER.value:
                user_owner_id = time_eval_of_user.get("user_id")
        if evaluate.get("status") == StatusChoice.SUCCESS.value:
            data_update.update({"status_filter": EvaluateStatusFilterChoice.COMPLETED.value})
        else:
            status_filter = EvaluateStatusFilterChoice.WAITING_USER.value
            time_eval_of_users = evaluate.get("time_eval_of_users")
            if user_type == UserEvaluateTypeChoice.OWNER.value and submit_time:
                status_filter = EvaluateStatusFilterChoice.WAITING_LEADER.value
            data_update.update({"status_filter": status_filter})
        if evaluate.get("status") == StatusChoice.SUCCESS.value:
            user_evaluate_job_title_level_id, leader_evaluate_job_title_level_id, job_title_level_status = (
                await calculate_point_after_job_level_id(competency_groups, before_job_title_level_id)
            )
            if leader_evaluate_job_title_level_id == before_job_title_level_id:
                job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_SAME
            data_update.update(
                {
                    EvaluateKey.USER_EVALUATE_JOB_TITLE_LEVEL_ID: user_evaluate_job_title_level_id,
                    EvaluateKey.LEADER_EVALUATE_JOB_TITLE_LEVEL_ID: leader_evaluate_job_title_level_id,
                    EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID: leader_evaluate_job_title_level_id,
                    EvaluateKey.JOB_TITLE_LEVEL_STATUS: job_title_level_status,
                }
            )

            if user_owner_id:
                # Update job_title_level_id trong bảng user
                user_repo = UserRepository(next(get_db()))
                await user_repo.update_user_job_title_level_id(user_owner_id, leader_evaluate_job_title_level_id)

        await evaluate_model.update_by_set({"_id": evaluate.get("_id")}, data_update)
    print("Done")


if __name__ == "__main__":
    asyncio.run(main())
