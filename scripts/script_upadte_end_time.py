#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 01/11/2024
"""

import asyncio

from bson import ObjectId

from src.common.choices import EvaluateStatusChoice
from src.common.common import Evaluate<PERSON>ey
from src.models.mongo.evaluate_model import EvaluateModel


async def update_status_done():
    evaluate_model: EvaluateModel = EvaluateModel()
    for evaluate_id in ["66fcf9b56453183e3e22d936", "66fcf9b56453183e3e22d937", "66fcf9b56453183e3e22d937"]:
        data_update = {EvaluateKey.STATUS: EvaluateStatusChoice.COMPLETED.value, EvaluateKey.DISABLE_EDIT: True}
        await evaluate_model.update_evaluate(evaluate_id, data_update)


async def update_status_end_time():
    evaluate_model: EvaluateModel = EvaluateModel()

    evaluates = await evaluate_model.find(
        {
            "_id": {
                "$in": [
                    ObjectId("66e790c96c4d41c680322245"),
                    ObjectId("66fcf9b56453183e3e22d936"),
                    ObjectId("66fcf9b56453183e3e22d937"),
                    ObjectId("66fcf9b56453183e3e22d938"),
                ]
            }
        }
    )
    for evaluate in evaluates:
        time_eval_of_users = evaluate.get("time_eval_of_users")
        root_end_time = evaluate.get("end_time")
        for time_eval_of_user in time_eval_of_users:
            time_eval_of_user_type = time_eval_of_user.get("user_type")
            end_time = time_eval_of_user.get("end_time")
            if time_eval_of_user_type == 2:
                end_time = end_time.replace(day=4, month=11)
                time_eval_of_user["end_time"] = end_time
                root_end_time = end_time
        await evaluate_model.update_one_query(
            {"_id": ObjectId(evaluate["_id"])}, {"time_eval_of_users": time_eval_of_users, "end_time": root_end_time}
        )


async def x():
    evaluate_model: EvaluateModel = EvaluateModel()
    z = await evaluate_model.get_evaluate_current_for_user("66e790c96c4d41c680322245")
    print(z)


if __name__ == "__main__":
    asyncio.run(update_status_end_time(), debug=True)
    # asyncio.run(x(), debug=True)
