import asyncio

from mobio.libs.logging import Mo<PERSON>Logging
from sqlalchemy.orm import Session
from configs.database import get_db
from lark.api.company_api import CompanyApi
from src.common.common import CompanyKey, PermissionKey, Role<PERSON>ey
from src.models.postgres.base_model import CompanyModel, PermissionModel
from src.models.postgres.base_model import RoleModel

role_init = [
    {
        RoleKey.NAME: "admin",
        RoleKey.DESCRIPTION: "Role Admin",
    },
    {
        RoleKey.NAME: "leader",
        RoleK<PERSON>.DESCRIPTION: "Role Leader",
    },
    {
        RoleKey.NAME: "user",
        RoleKey.DESCRIPTION: "Role User",
    },
]

role_permission_init = [
    # admin
    {
        RoleKey.NAME: "admin",
        "permissions": [
            {
                PermissionKey.ACTION: "read",
                PermissionKey.SCOPE: "all",
            },
            {
                PermissionKey.ACTION: "create",
                PermissionKey.SCOPE: "all",
            },
            {
                PermissionKey.ACTION: "update",
                PermissionKey.SCOPE: "all",
            },
            {
                PermissionKey.ACTION: "delete",
                PermissionKey.SCOPE: "all",
            },
        ],
    },
    {
        RoleKey.NAME: "leader",
        "permissions": [
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "organizational_chart"},
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "title_level"},
            {PermissionKey.ACTION: "create", PermissionKey.SCOPE: "title_level"},
            {PermissionKey.ACTION: "update", PermissionKey.SCOPE: "title_level"},
            {PermissionKey.ACTION: "delete", PermissionKey.SCOPE: "title_level"},
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "competency_dictionary"},
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "competency_framework"},
            {
                PermissionKey.ACTION: "update",
                PermissionKey.SCOPE: "competency_framework",
            },
            {
                PermissionKey.ACTION: "delete",
                PermissionKey.SCOPE: "competency_framework",
            },
            {
                PermissionKey.ACTION: "create",
                PermissionKey.SCOPE: "competency_framework",
            },
            {
                PermissionKey.ACTION: "read",
                PermissionKey.SCOPE: "evaluate",
            },
            {
                PermissionKey.ACTION: "update",
                PermissionKey.SCOPE: "evaluate",
            },
            {
                PermissionKey.ACTION: "delete",
                PermissionKey.SCOPE: "evaluate",
            },
            {
                PermissionKey.ACTION: "create",
                PermissionKey.SCOPE: "evaluate",
            },
            {
                PermissionKey.ACTION: "read",
                PermissionKey.SCOPE: "evaluate_result",
            },
            {
                PermissionKey.ACTION: "update",
                PermissionKey.SCOPE: "evaluate_result",
            },
            {
                PermissionKey.ACTION: "delete",
                PermissionKey.SCOPE: "evaluate_result",
            },
            {
                PermissionKey.ACTION: "create",
                PermissionKey.SCOPE: "evaluate_result",
            },
        ],
    },
    {
        RoleKey.NAME: "user",
        "permissions": [
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "organizational_chart"},
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "evaluate"},
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "evaluate_result"},
            {PermissionKey.ACTION: "read", PermissionKey.SCOPE: "competency_framework"},
            {
                PermissionKey.ACTION: "update",
                PermissionKey.SCOPE: "evaluate",
            },
        ],
    },
]

permission_init = [
    {
        PermissionKey.DESCRIPTION: "Permission create for all scope",
        PermissionKey.ACTION: "create",
        PermissionKey.SCOPE: "all",
    },
    {
        PermissionKey.DESCRIPTION: "Permission update for all scope",
        PermissionKey.ACTION: "update",
        PermissionKey.SCOPE: "all",
    },
    {
        PermissionKey.DESCRIPTION: "Permission delete for all scope",
        PermissionKey.ACTION: "delete",
        PermissionKey.SCOPE: "all",
    },
    {
        PermissionKey.DESCRIPTION: "Permission read for all scope",
        PermissionKey.ACTION: "read",
        PermissionKey.SCOPE: "all",
    },
    {
        PermissionKey.DESCRIPTION: "Permission read organizational chart",
        PermissionKey.ACTION: "read",
        PermissionKey.SCOPE: "organizational_chart",
    },
    {
        PermissionKey.DESCRIPTION: "Permission create organizational chart",
        PermissionKey.ACTION: "create",
        PermissionKey.SCOPE: "organizational_chart",
    },
    {
        PermissionKey.DESCRIPTION: "Permission update organizational chart",
        PermissionKey.ACTION: "update",
        PermissionKey.SCOPE: "organizational_chart",
    },
    {
        PermissionKey.DESCRIPTION: "Permission delete organizational chart",
        PermissionKey.ACTION: "delete",
        PermissionKey.SCOPE: "organizational_chart",
    },
    {
        PermissionKey.DESCRIPTION: "Permission read title level",
        PermissionKey.ACTION: "read",
        PermissionKey.SCOPE: "title_level",
    },
    {
        PermissionKey.DESCRIPTION: "Permission create title level",
        PermissionKey.ACTION: "create",
        PermissionKey.SCOPE: "title_level",
    },
    {
        PermissionKey.DESCRIPTION: "Permission update title level",
        PermissionKey.ACTION: "update",
        PermissionKey.SCOPE: "title_level",
    },
    {
        PermissionKey.DESCRIPTION: "Permission delete title level",
        PermissionKey.ACTION: "delete",
        PermissionKey.SCOPE: "title_level",
    },
    {
        PermissionKey.DESCRIPTION: "Permission read competency dictionary",
        PermissionKey.ACTION: "read",
        PermissionKey.SCOPE: "competency_dictionary",
    },
    {
        PermissionKey.DESCRIPTION: "Permission create competency dictionary",
        PermissionKey.ACTION: "create",
        PermissionKey.SCOPE: "competency_dictionary",
    },
    {
        PermissionKey.DESCRIPTION: "Permission update proficiency dictionary",
        PermissionKey.ACTION: "update",
        PermissionKey.SCOPE: "competency_dictionary",
    },
    {
        PermissionKey.DESCRIPTION: "Permission delete proficiency dictionary",
        PermissionKey.ACTION: "delete",
        PermissionKey.SCOPE: "competency_dictionary",
    },
    {
        PermissionKey.DESCRIPTION: "Permission read competency framework",
        PermissionKey.ACTION: "read",
        PermissionKey.SCOPE: "competency_framework",
    },
    {
        PermissionKey.DESCRIPTION: "Permission create competency framework",
        PermissionKey.ACTION: "create",
        PermissionKey.SCOPE: "competency_framework",
    },
    {
        PermissionKey.DESCRIPTION: "Permission update competency framework",
        PermissionKey.ACTION: "update",
        PermissionKey.SCOPE: "competency_framework",
    },
    {
        PermissionKey.DESCRIPTION: "Permission delete competency framework",
        PermissionKey.ACTION: "delete",
        PermissionKey.SCOPE: "competency_framework",
    },
    {
        PermissionKey.DESCRIPTION: "Permission read evaluate",
        PermissionKey.ACTION: "read",
        PermissionKey.SCOPE: "evaluate",
    },
    {
        PermissionKey.DESCRIPTION: "Permission create evaluate",
        PermissionKey.ACTION: "create",
        PermissionKey.SCOPE: "evaluate",
    },
    {
        PermissionKey.DESCRIPTION: "Permission update evaluate",
        PermissionKey.ACTION: "update",
        PermissionKey.SCOPE: "evaluate",
    },
    {
        PermissionKey.DESCRIPTION: "Permission delete evaluate",
        PermissionKey.ACTION: "delete",
        PermissionKey.SCOPE: "evaluate",
    },
    {
        PermissionKey.DESCRIPTION: "Permission read result of evaluation",
        PermissionKey.ACTION: "read",
        PermissionKey.SCOPE: "evaluate_result",
    },
    {
        PermissionKey.DESCRIPTION: "Permission update result of evaluation",
        PermissionKey.ACTION: "update",
        PermissionKey.SCOPE: "evaluate_result",
    },
    {
        PermissionKey.DESCRIPTION: "Permission delete result of evaluation",
        PermissionKey.ACTION: "delete",
        PermissionKey.SCOPE: "evaluate_result",
    },
    {
        PermissionKey.DESCRIPTION: "Permission create result of evaluation",
        PermissionKey.ACTION: "create",
        PermissionKey.SCOPE: "evaluate_result",
    },
]


async def init_role_and_permission():
    session_db: Session = next(get_db())

    MobioLogging().info(
        f"Script_init_role_and_permission :: Start init role and permission :: data_role_init :: {role_init} :: data_permission_init :: {permission_init}"
    )

    company_api: CompanyApi = CompanyApi()

    company_info_of_lark = await company_api.get_company_information()
    query_of_company = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark.tenant_key)
    if query_of_company.first():
        query_of_company.update(
            {
                CompanyKey.NAME: company_info_of_lark.name,
                CompanyKey.AVATAR: company_info_of_lark.avatar.avatar_origin,
            }
        )
    else:
        session_db.add(
            CompanyModel(
                tenant_key=company_info_of_lark.tenant_key,
                name=company_info_of_lark.name,
                avatar=company_info_of_lark.avatar.avatar_origin,
            )
        )
    session_db.commit()
    company_info = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark.tenant_key).first()

    for role in role_init:
        name = role.get(RoleKey.NAME)
        role_current = session_db.query(RoleModel).filter_by(name=name).update(role)
        if not role_current:
            session_db.add(RoleModel(**role, company_id=company_info.company_id))
        session_db.commit()

    for permission in permission_init:
        action = permission.get(PermissionKey.ACTION)
        scope = permission.get(PermissionKey.SCOPE)
        permission_current = session_db.query(PermissionModel).filter_by(action=action, scope=scope).update(permission)
        if not permission_current:
            session_db.add(PermissionModel(**permission, company_id=company_info.company_id))
        session_db.commit()

    for role_permission in role_permission_init:
        role = role_permission.get(RoleKey.NAME)
        role_permissions = role_permission.get("permissions")

        role = session_db.query(RoleModel).filter_by(name=role).first()

        for role_permission in role_permissions:
            permission_append = (
                session_db.query(PermissionModel)
                .filter_by(
                    action=role_permission.get(PermissionKey.ACTION), scope=role_permission.get(PermissionKey.SCOPE)
                )
                .first()
            )
            if permission_append in role.permissions:
                continue
            role.permissions.append(permission_append)
        session_db.commit()

    MobioLogging().debug("Script_init_role_and_permission :: End init role and permission")


if __name__ == "__main__":
    asyncio.run(init_role_and_permission(), debug=True)
