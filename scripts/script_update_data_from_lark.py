#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/10/2024
"""
import asyncio

import unidecode
from mobio.libs.logging import MobioLogging
from sqlalchemy.orm import Session

from configs.database import get_db
from lark.api.company_api import CompanyApi
from lark.api.department_api import DepartmentApi
from lark.api.user_api import UserApi
from src.common.choices import DepartmentChoice, RoleChoice
from src.common.common import CompanyKey, EmploymentTypeKey, JobTitleKey
from src.models.postgres.base_model import (
    CompanyModel,
    DepartmentModel,
    EmploymentTypeModel,
    JobTitleModel,
    RoleModel,
    UserModel,
)
from src.utils.time_helper import to_time_at

employee_types_init = [
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 1,
        EmploymentTypeKey.VI_NAME: "Chính thức",
        EmploymentTypeKey.EN_NAME: "Regular",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 2,
        EmploymentTypeKey.VI_NAME: "Thực tập sinh",
        EmploymentTypeKey.EN_NAME: "Intern",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 5,
        EmploymentTypeKey.VI_NAME: "Tư vấn",
        EmploymentTypeKey.EN_NAME: " Consultant",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 6,
        EmploymentTypeKey.VI_NAME: "Thử việc",
        EmploymentTypeKey.EN_NAME: "Probation",
    },
]


async def update_data_organization_from_lark():

    session_db: Session = next(get_db())

    user_api: UserApi = UserApi()
    company_api: CompanyApi = CompanyApi()
    department_api: DepartmentApi = DepartmentApi()

    company_info_of_lark = await company_api.get_company_information()
    query_of_company = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark.tenant_key)
    if query_of_company.first():
        query_of_company.update(
            values={
                CompanyKey.NAME: company_info_of_lark.name,
                CompanyKey.AVATAR: company_info_of_lark.avatar.avatar_origin,
            }
        )
    else:
        session_db.add(
            CompanyModel(
                tenant_key=company_info_of_lark.tenant_key,
                name=company_info_of_lark.name,
                avatar=company_info_of_lark.avatar.avatar_origin,
            )
        )
    session_db.commit()

    company_info = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark.tenant_key).first()

    for employee_type in employee_types_init:
        employee_type_current = (
            session_db.query(EmploymentTypeModel)
            .filter_by(employment_type_id=employee_type[EmploymentTypeKey.EMPLOYMENT_TYPE_ID])
            .update(
                {
                    EmploymentTypeKey.VI_NAME: employee_type[EmploymentTypeKey.VI_NAME],
                    EmploymentTypeKey.EN_NAME: employee_type[EmploymentTypeKey.EN_NAME],
                }
            )
        )
        if not employee_type_current:
            session_db.add(
                EmploymentTypeModel(
                    company_id=company_info.company_id,
                    employment_type_id=employee_type[EmploymentTypeKey.EMPLOYMENT_TYPE_ID],
                    vi_name=employee_type[EmploymentTypeKey.VI_NAME],
                    en_name=employee_type[EmploymentTypeKey.EN_NAME],
                )
            )
        session_db.commit()

    department_ids = await department_api.get_department_ids_in_permission_scope()
    departments = await department_api.get_bulk_department_information(department_ids)
    MobioLogging().info(f" init_data_organization_from_lark :: department_ids :: {department_ids}")

    child_departments = list()

    users_owner_departments = {}

    department_order = 0
    # insert information department
    for department_information in departments:
        department_order += 1

        if int(department_information.parent_department_id):
            child_departments.append(department_information)
            continue

        display = 1
        if department_information.name.lower() in [DepartmentChoice.BOD.value]:
            display = 0

        status = 1 if not department_information.status.is_deleted else 0
        lower_case_name = department_information.name.lower()
        department_current = (
            session_db.query(DepartmentModel).filter_by(lark_department_id=department_information.department_id).first()
        )
        if not department_current:
            department_current = DepartmentModel(
                lark_department_id=department_information.department_id,
                company_id=company_info.company_id,
                open_department_id=department_information.open_department_id,
                name=department_information.name,
                lower_case_name=lower_case_name,
                status=status,
                display=display,
                order=department_order,
                owners=[],
            )
            session_db.add(department_current)
        else:
            department_current.status = status
            department_current.display = display
            department_current.lower_case_name = lower_case_name
            department_current.name = department_information.name
        session_db.commit()

        users = await user_api.get_bulk_user_information_by_department_id(
            department_id=department_current.open_department_id
        )

        if not users:
            continue
        role_user = session_db.query(RoleModel).filter_by(name=RoleChoice.USER.value).first()
        for user_information in users:
            MobioLogging().info(f" init_data_organization_from_lark :: user :: email :: {user_information.email}")
            job_title = (
                session_db.query(JobTitleModel)
                .filter_by(name=user_information.job_title, department_id=department_current.department_id)
                .first()
            )
            if not job_title and user_information.job_title:
                job_title = JobTitleModel(
                    company_id=company_info.company_id,
                    name=user_information.job_title,
                    lower_case_name=user_information.job_title.lower(),
                    department_id=department_current.department_id,
                )
                session_db.add(job_title)
                session_db.commit()
            if not job_title:
                continue

            user_current = session_db.query(UserModel).filter_by(primary_email=user_information.email).first()
            if not user_current:
                user_current = UserModel(
                    primary_email=user_information.email,
                    company_id=company_info.company_id,
                    employee_code=UserModel().generate_employee_code(session_db),
                    name=user_information.name,
                    phone_number=user_information.mobile,
                    gender=user_information.gender,
                    lark_user_id=user_information.user_id,
                    open_user_id=user_information.open_id,
                    thumb_avatar_link=user_information.avatar.avatar_origin,
                    status=1 if user_information.status.is_activated else 0,
                    start_onboard_at=to_time_at(user_information.join_time),
                    employment_type_id=user_information.employee_type,
                    leader_user_id=user_information.leader_user_id,
                    departments=[department_current],
                    job_title_id=job_title.job_title_id,
                    roles=[role_user],
                    order=1,
                )
                session_db.add(user_current)
            else:
                if not department_current in user_current.departments:
                    user_current.departments.append(department_current)
                user_current.status = 1 if user_information.status.is_activated else 0
                user_current.thumb_avatar_link = user_information.avatar.avatar_origin
                user_current.employment_type_id = user_information.employee_type
                user_current.leader_user_id = user_information.leader_user_id
                user_current.gender = user_information.gender
                user_current.phone_number = user_information.mobile
                user_current.name = user_information.name
                user_current.job_title_id = job_title.job_title_id
                user_current.unsigned_name = unidecode.unidecode(user_information.name).lower()
            session_db.commit()

            if user_information.custom_attrs:
                for custom_attribute in user_information.custom_attrs:
                    if custom_attribute.id in ["C-7407297354980229152", "C-7389984131214688288"]:
                        MobioLogging().info("user_information.custom_attrs :: {}".format(custom_attribute.value.text))
                        owner_department_ids = custom_attribute.value.text.replace(" ", "").split(",")
                        users_owner_departments.update({user_current.user_id: owner_department_ids})

    MobioLogging().info("users_owner_departments :: {}".format(users_owner_departments))

    # replace leader_user_id of user
    users_in_system = session_db.query(UserModel).all()
    bod_department = session_db.query(DepartmentModel).filter_by(lower_case_name="bod").first()
    hr_department = session_db.query(DepartmentModel).filter_by(lower_case_name="hr").first()

    for user in users_in_system:
        role_choice = RoleChoice.USER.value
        if bod_department in user.departments:
            role_choice = RoleChoice.ADMIN.value
        role_user = session_db.query(RoleModel).filter_by(name=role_choice).first()
        user.roles = [role_user]

        if user.leader_user_id:
            leader = session_db.query(UserModel).filter_by(open_user_id=user.leader_user_id).first()
            user.leader_user_id = leader.user_id if leader else None
        if bod_department in user.departments:
            user.leader_user_id = None
        session_db.commit()

    # Update owners in department
    leader_role = session_db.query(RoleModel).filter_by(lower_case_name="leader").first()
    departments_in_system = session_db.query(DepartmentModel).all()
    for department_system in departments_in_system:
        department_in_lark = await department_api.get_single_department_information(
            department_system.open_department_id
        )

        leaders = department_in_lark.get("leaders", [])
        if leaders:
            for leader in leaders:
                lark_user_id = leader.get("leaderID")
                user = session_db.query(UserModel).filter_by(lark_user_id=lark_user_id).first()
                user.roles = [leader_role]

        owners = []
        # MobioLogging().info("department_system.lark_department_id :: {}, ")
        for owner_id, dp_ids in users_owner_departments.items():
            MobioLogging().info(
                "department_system.department_id :: {}, dp_ids :: {}".format(department_system.department_id, dp_ids)
            )
            if department_system.lark_department_id[:8] in dp_ids:
                owners.append(owner_id)
        MobioLogging().info("owners: {}".format(owners))
        department_system.owners = owners
        session_db.commit()

    # Update job title in department BOD
    bod_department = session_db.query(DepartmentModel).filter_by(lower_case_name="bod").first()
    users_in_bod = await user_api.get_bulk_user_information_by_department_id(bod_department.open_department_id)
    for user_bod in users_in_bod:
        job_title = (
            session_db.query(JobTitleModel)
            .filter_by(name=user_bod.job_title)
            .update({JobTitleKey.DEPARTMENT_ID: bod_department.department_id})
        )
        session_db.commit()

    MobioLogging().debug(f"update_data_organization_from_lark :: done")


if __name__ == "__main__":
    asyncio.run(update_data_organization_from_lark(), debug=True)
