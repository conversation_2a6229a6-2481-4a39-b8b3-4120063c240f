import asyncio
import sys
import pandas
from configs import ApplicationConfig
from configs.database import get_db
from datetime import datetime
import numpy as np

from src.common.common import CommonKey, CompetencyFrameworkKey
from src.models.mongo.competency_framework_model import Competency<PERSON>rameworkModel
from src.repositories.job_title_repository import JobTitleRepository
from src.utils.attrs import generate_uuid_without_dash
from src.utils.time_helper import get_time_now
from mobio.libs.logging import MobioLogging


async def init_po_competency_framework():

    session_db = next(get_db())

    job_title_repo: JobTitleRepository = JobTitleRepository(session_db)
    competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()

    df = pandas.ExcelFile(f"{ApplicationConfig.LADDER_HOME}/resources/imports/po_competency_framework.xlsx")
    df_competency_framework = pandas.read_excel(df, sheet_name="Khung năng lực team PO")
    df_competency_framework = df_competency_framework.replace(np.nan, "", regex=True)

    company_id = sys.argv[1]
    department_id = sys.argv[2]

    job_titles = await job_title_repo.get_job_title_by_department_id(department_id)
    job_title_levels = await job_title_repo.get_job_title_levels(job_titles[0].job_title_id)

    job_title_level_mapping = {}
    for job_title_level in job_title_levels:
        job_title_level_mapping[job_title_level.name] = job_title_level.job_title_level_id
    MobioLogging().info(f"init_po_competency_framework :: job_title_level_mapping :: {job_title_level_mapping}")

    competency_groups = []
    for competency_group in df_competency_framework["Nhóm năng lực"].unique():

        lst_competency = []
        for idx, row in df_competency_framework.iterrows():
            if row["Nhóm năng lực"] == competency_group:
                behavior_expressions = []
                for idx_behavior_expression in range(1, 6):
                    if row[f"Biểu hiện hành vi cấp độ {idx_behavior_expression}"]:
                        behavior_expressions.append(
                            {
                                CompetencyFrameworkKey.DESCRIPTION: row[
                                    f"Biểu hiện hành vi cấp độ {idx_behavior_expression}"
                                ],
                                CompetencyFrameworkKey.LEVEL: idx_behavior_expression,
                                CompetencyFrameworkKey.IS_ACTIVATE: True,
                                CompetencyFrameworkKey.POINT_MIN: int(
                                    row[f"Điểm tối thiểu của cấp độ {idx_behavior_expression}"]
                                ),
                                "point_max": 0,
                                "point_mid": 0,
                            }
                        )
                    else:
                        behavior_expressions.append(
                            {
                                "description": "",
                                CompetencyFrameworkKey.LEVEL: idx_behavior_expression,
                                CompetencyFrameworkKey.IS_ACTIVATE: False,
                                "point_min": 0,
                                "point_max": 0,
                                "point_mid": 0,
                            }
                        )

                job_title_levels = []
                for job_title_level_name, job_title_level_id in job_title_level_mapping.items():
                    if row[job_title_level_name]:
                        job_title_levels.append(
                            {
                                CompetencyFrameworkKey.JOB_TITLE_LEVEL_ID: job_title_level_id,
                                CompetencyFrameworkKey.BEHAVIOR_EXPRESSION_LEVEL: int(row[job_title_level_name]),
                            }
                        )
                    else:
                        job_title_levels.append(
                            {
                                CompetencyFrameworkKey.JOB_TITLE_LEVEL_ID: job_title_level_id,
                                CompetencyFrameworkKey.BEHAVIOR_EXPRESSION_LEVEL: -1,
                            }
                        )

                lst_competency.append(
                    {
                        CompetencyFrameworkKey.COMPETENCY_ID: generate_uuid_without_dash(),
                        CompetencyFrameworkKey.NAME: row["Tên năng lực"],
                        CompetencyFrameworkKey.DESCRIPTION: (
                            row["Định nghĩa năng lực"] if row["Định nghĩa năng lực"] else ""
                        ),
                        CompetencyFrameworkKey.WEIGHT: row["Trọng số"],
                        CompetencyFrameworkKey.BEHAVIOR_EXPRESSIONS: behavior_expressions,
                        CompetencyFrameworkKey.JOB_TITLE_LEVELS: job_title_levels,
                    }
                )

        competency_groups.append(
            {
                CompetencyFrameworkKey.NAME: competency_group,
                CompetencyFrameworkKey.COMPETENCY_GROUP_ID: generate_uuid_without_dash(),
                CompetencyFrameworkKey.LST_COMPETENCY: lst_competency,
            }
        )

    data_insert = {
        CompetencyFrameworkKey.NAME: "Khung năng lực team PO",
        CommonKey.CREATED_BY: "admin",
        CommonKey.UPDATED_BY: "admin",
        CommonKey.CREATED_TIME: get_time_now(),
        CommonKey.UPDATED_TIME: get_time_now(),
        CompetencyFrameworkKey.DEPARTMENT_ID: department_id,
        CompetencyFrameworkKey.JOB_TITLE_IDS: [job_title.job_title_id for job_title in job_titles],
        # CompetencyFrameworkKey.JOB_TITLE_IDS: job_titles,
        CompetencyFrameworkKey.STATUS: 1,
        CompetencyFrameworkKey.START_TIME: datetime(2024, 9, 15),
        CommonKey.COMPANY_ID: company_id,
        CompetencyFrameworkKey.COMPETENCY_GROUPS: competency_groups,
    }
    MobioLogging().info(f"init_po_competency_framework :: data_insert :: {data_insert}")

    inserted_id = await competency_framework_model.insert_competency_framework(data_insert)
    MobioLogging().info(f"init_po_competency_framework :: inserted_id :: {inserted_id}")
    return


if __name__ == "__main__":
    asyncio.run(init_po_competency_framework())
