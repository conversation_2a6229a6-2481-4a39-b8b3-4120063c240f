#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 02/10/2024
"""

import asyncio
from datetime import timedelta

from mobio.libs.logging import MobioLogging
from sqlalchemy.orm import Session

from configs.database import get_db
from lark.api.company_api import CompanyApi
from src.common.choices import (
    CompetencyFrameworkStatusChoice,
    EvaluateStatusChoice,
    UserEvaluateTypeChoice,
)
from src.common.common import (
    Common<PERSON>ey,
    Competency<PERSON><PERSON><PERSON>ork<PERSON><PERSON>,
    <PERSON><PERSON>ateKey,
    EvaluateTimeKey,
)
from src.models.mongo.competency_framework_model import CompetencyFrameworkModel
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.evaluate_time_model import EvaluateTimeModel
from src.repositories.company_repository import CompanyRepository
from src.repositories.department_repository import DepartmentRepository
from src.repositories.users_repository import UserRepository
from src.utils.time_helper import get_time_now, to_time_offset_aware


async def get_competencies_for_user(competency_groups, job_title_level_id):
    evaluate_competency_groups = []
    for competency_group in competency_groups:
        competency_group_id = competency_group.get(CompetencyFrameworkKey.COMPETENCY_GROUP_ID)
        competency_group_name = competency_group.get(CompetencyFrameworkKey.NAME)
        lst_competency = competency_group.get(CompetencyFrameworkKey.LST_COMPETENCY)
        lst_competency_for_evaluate = []

        for competency in lst_competency:
            competency_id = competency.get(CompetencyFrameworkKey.COMPETENCY_ID)
            competency_name = competency.get(CompetencyFrameworkKey.NAME)
            weight = competency.get(CompetencyFrameworkKey.WEIGHT)
            behavior_expressions = competency.get(CompetencyFrameworkKey.BEHAVIOR_EXPRESSIONS)
            job_title_levels = competency.get(CompetencyFrameworkKey.JOB_TITLE_LEVELS)

            job_title_level_for_user = next(
                filter(
                    lambda x: x.get(CompetencyFrameworkKey.JOB_TITLE_LEVEL_ID) == job_title_level_id,
                    job_title_levels,
                ),
                None,
            )
            if not job_title_level_for_user:
                continue

            behavior_expression_level = job_title_level_for_user.get(
                CompetencyFrameworkKey.BEHAVIOR_EXPRESSION_LEVEL, -1
            )
            if behavior_expression_level == -1 or not behavior_expression_level:
                continue

            behavior_expression = next(
                filter(
                    lambda x: x.get(CompetencyFrameworkKey.LEVEL) == behavior_expression_level, behavior_expressions
                ),
                None,
            )
            point_min = behavior_expression.get(CompetencyFrameworkKey.POINT_MIN)

            lst_competency_for_evaluate.append(
                {
                    EvaluateKey.COMPETENCY_ID: competency_id,
                    EvaluateKey.NAME: competency_name,
                    EvaluateKey.WEIGHT: weight,
                    EvaluateKey.POINT_MIN: point_min,
                    EvaluateKey.BEHAVIOR_EXPRESSIONS: behavior_expressions,
                    EvaluateKey.JOB_TITLE_LEVELS: job_title_levels,
                }
            )

        if lst_competency_for_evaluate:
            evaluate_competency_groups.append(
                {
                    EvaluateKey.COMPETENCY_GROUP_ID: competency_group_id,
                    EvaluateKey.NAME: competency_group_name,
                    EvaluateKey.LST_COMPETENCY: lst_competency_for_evaluate,
                }
            )

    return evaluate_competency_groups


async def async_owner_do():
    session_db: Session = next(get_db())

    competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
    evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
    evaluate_model: EvaluateModel = EvaluateModel()
    evaluate_time_model: EvaluateTimeModel = EvaluateTimeModel()
    company_api: CompanyApi = CompanyApi()
    company_repo: CompanyRepository = CompanyRepository(session_db)
    user_repo: UserRepository = UserRepository(session_db)
    department_repo: DepartmentRepository = DepartmentRepository(session_db)

    company_info_of_lark = await company_api.get_company_information()
    MobioLogging().info(f"HandlerAutoGenerateEvaluate :: get_company_information :: {company_info_of_lark.name}")

    time_now = get_time_now()

    time_start_month = time_now.replace(day=1, hour=0, minute=0, second=0) - timedelta(hours=1)
    time_end_month = time_now.replace(day=30, hour=23, minute=59, second=59) - timedelta(hours=1)

    company = await company_repo.get_company(tenant_key=company_info_of_lark.tenant_key)
    evaluate_times_of_departments = list(
        await evaluate_time_model.find(
            {
                CommonKey.COMPANY_ID: company.company_id,
                EvaluateTimeKey.TIMES: {
                    "$elemMatch": {
                        EvaluateTimeKey.START_TIME: {
                            "$gte": time_start_month,
                            "$lte": time_end_month,
                        }
                    }
                },
            }
        )
    )
    MobioLogging().debug(
        f"HandlerAutoGenerateEvaluate :: evaluate_times_of_departments :: {evaluate_times_of_departments}"
    )
    if not evaluate_times_of_departments:
        return

    for evaluate_time_of_department in evaluate_times_of_departments:
        department_id = evaluate_time_of_department.get(EvaluateTimeKey.DEPARTMENT_ID)
        evaluate_period_id = evaluate_time_of_department.get(EvaluateTimeKey.EVALUATE_PERIOD_ID)
        times = evaluate_time_of_department.get(EvaluateTimeKey.TIMES)
        # time_for_evaluate = times[-1]
        time_for_evaluate = next(
            filter(
                lambda x: (to_time_offset_aware(x.get(EvaluateTimeKey.START_TIME)) >= time_start_month)
                and (to_time_offset_aware(x.get(EvaluateTimeKey.START_TIME)) <= time_end_month),
                times,
            ),
            None,
        )
        # for t in times:
        #     start_time = to_time_offset_aware(t.get(EvaluateTimeKey.START_TIME))

        #     if (start_time >= to_time_offset_aware(time_start_month)) and (start_time <= to_time_offset_aware(time_end_month)):
        #         time_for_evaluate = t
        #         break
        if not time_for_evaluate:
            continue

        start_time_evaluate = to_time_offset_aware(time_for_evaluate.get(EvaluateTimeKey.START_TIME))
        end_time_evaluate = to_time_offset_aware(time_for_evaluate.get(EvaluateTimeKey.END_TIME))
        MobioLogging().info(
            f"HandlerAutoGenerateEvaluate :: start_time_evaluate :: {start_time_evaluate} :: end_time_evaluate :: {end_time_evaluate}"
        )

        competency_framework = await competency_framework_model.find_one(
            {
                CommonKey.COMPANY_ID: company.company_id,
                CompetencyFrameworkKey.DEPARTMENT_ID: department_id,
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
                CompetencyFrameworkKey.START_TIME: {"$lte": start_time_evaluate},
            }
        )

        MobioLogging().info(f"HandlerAutoGenerateEvaluate :: proficiency_framework :: {competency_framework}")
        if not competency_framework:
            continue
        evaluate_exist_time = await evaluate_model.find_one(
            {
                EvaluateKey.DEPARTMENT_ID: department_id,
                EvaluateKey.TIME_EVAL_OF_USERS: {
                    "$elemMatch": {
                        EvaluateTimeKey.START_TIME: {
                            "$gte": time_start_month,
                            "$lte": time_end_month,
                        }
                    }
                },
            }
        )
        MobioLogging().info(f"HandlerAutoGenerateEvaluate :: evaluate_exist_time :: {evaluate_exist_time}")
        if evaluate_exist_time:
            continue

        competency_framework_id = str(competency_framework.get(CommonKey.ID))
        job_title_ids = competency_framework.get(CompetencyFrameworkKey.JOB_TITLE_IDS)
        competency_groups = competency_framework.get(CompetencyFrameworkKey.COMPETENCY_GROUPS)

        users = await user_repo.get_users_by_job_title_ids(job_title_ids)
        if not users:
            continue

        for user in users:
            if not user.job_title_level_id:
                continue
            evaluate_competency_groups = await get_competencies_for_user(competency_groups, user.job_title_level_id)

            if evaluate_competency_groups:

                evaluate_user = {
                    CommonKey.COMPANY_ID: company.company_id,
                    EvaluateKey.DEPARTMENT_ID: department_id,
                    EvaluateKey.USER_ID: user.user_id,
                    EvaluateKey.START_TIME: start_time_evaluate,
                    EvaluateKey.END_TIME: end_time_evaluate,
                    EvaluateKey.EVALUATE_PERIOD_ID: evaluate_period_id,
                    EvaluateKey.COMPETENCY_GROUPS: evaluate_competency_groups,
                    EvaluateKey.COMPETENCY_FRAMEWORK_ID: competency_framework_id,
                    EvaluateKey.TIME_EVAL_OF_USERS: [
                        {
                            EvaluateKey.USER_ID: user.user_id,
                            EvaluateKey.START_TIME: start_time_evaluate,
                            EvaluateKey.END_TIME: start_time_evaluate + timedelta(days=15),
                            EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.OWNER.value,
                        },
                        {
                            EvaluateKey.USER_ID: user.leader_user_id,
                            EvaluateKey.START_TIME: start_time_evaluate + timedelta(days=30),
                            EvaluateKey.END_TIME: end_time_evaluate,
                            EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.LEADER.value,
                        },
                    ],
                    EvaluateKey.STATUS: EvaluateStatusChoice.WAITING.value,
                    EvaluateKey.DISABLE_EDIT: False,
                    EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID: user.job_title_level_id,
                    EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID: "",
                    EvaluateKey.AMOUNT_OF_WORK_NOTE: "",
                    EvaluateKey.USER_NOTE: "",
                    EvaluateKey.HR_REVIEW: "",
                    EvaluateKey.LEADER_REVIEW: "",
                    EvaluateKey.WORK_PROCESS_NOTE: "",
                    CommonKey.CREATED_BY: "admin",
                    CommonKey.UPDATED_BY: "admin",
                    CommonKey.UPDATED_TIME: get_time_now(),
                    CommonKey.CREATED_TIME: get_time_now(),
                }
                MobioLogging().info(
                    f"HandlerAutoGenerateEvaluate :: name :: {user.name} :: evaluate_user :: {evaluate_user}"
                )

                inserted_id = await evaluate_model.insert_evaluate(evaluate_user)
                MobioLogging().info(
                    f"HandlerAutoGenerateEvaluate :: name :: {user.name} :: inserted_id :: {inserted_id}"
                )

        # # push noti đến phòng ban đã sắp đến kì đánh giá của nhân viên -> department_id.
        # department = await department_repo.get_department_by_id(department_id)
        # message = {
        #     "send_type": SendBotMessageSubscriber.SendType.SEND_BATCH_MESSAGES,
        #     "payload": {
        #         "department_ids": [department.open_department_id],
        #         "msg_type": "interactive",
        #         "card": {
        #             "config": {"wide_screen_mode": True},
        #             "elements": [{"tag": "div", "text": {"content": "Build failed 🤒🤒🤒🤒", "tag": "plain_text"}}],
        #             "header": {
        #                 "template": "blue",
        #                 "title": {"content": "Jenkins - Ladder", "tag": "plain_text"},
        #             },
        #         },
        #     },
        # }
        # publisher.publish_message(channel=RedisSubscriberChannel.SEND_BOT_MESSAGE, message=message)
        # Gửi noti bắt đầu kì đánh giá của nhân viên -> user_id
        # text = f"Đã đến thời gian bạn vào đánh giá: {start_time_of_user.strftime('%d-%m-%Y')}"

        # if text:
        #     MobioLogging().info(
        #         f"HandlerUpdateStatusForCompetencyFramework :: text :: {text} :: {get_time_now()}"
        #     )
        #     SendNotiToUserController().send_message_alert_lark(user, text)

    MobioLogging().info(f"HandlerAutoGenerateEvaluate :: end :: {get_time_now()}")
    return


if __name__ == "__main__":
    asyncio.run(async_owner_do(), debug=True)
