import asyncio

from lark.api.base.folder_api import FolderApi
from src.models.mongo.folder_model import FolderModel
from src.utils.time_helper import get_time_now


async def create_folder_contain_competency_framework():
    base_api: FolderApi = FolderApi()
    folder_model: FolderModel = FolderModel()

    folder_name = "Khung năng lực"
    folder = await folder_model.get_folder_by_name(folder_name.lower())

    if not folder:
        root_folder_meta_token = await base_api.get_root_folder_meta()
        folder_info = await base_api.create_folder(folder_token=root_folder_meta_token, folder_name=folder_name)
        folder_info.update({"name": folder_name, "lower_case_name": folder_name.lower()})
        inserted_id = await folder_model.create_folder(folder_info)
        print(
            "{} folder :: {} :: status :: create done :: inserted_id :: {}".format(
                get_time_now(), folder_info, inserted_id
            )
        )
    print("{} :: folder :: {} :: status :: created".format(get_time_now(), folder))
    return True


async def create_folder_contain_evaluate():
    pass


if __name__ == "__main__":
    asyncio.run(create_folder_contain_competency_framework(), debug=True)
    # asyncio.run(create_folder_contain_evaluate())
