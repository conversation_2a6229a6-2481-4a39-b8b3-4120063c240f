#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/09/2024
"""

import asyncio

from sqlalchemy.orm import Session

from configs.database import get_db
from lark.api.company_api import CompanyApi
from src.common.common import CompanyKey
from src.models.postgres.base_model import CompanyModel, JobTitleModel


async def x():
    session_db: Session = next(get_db())
    company_api: CompanyApi = CompanyApi()

    company_info_of_lark = await company_api.get_company_information()
    query_of_company = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark.tenant_key)
    if query_of_company.first():
        query_of_company.update(
            {
                CompanyKey.NAME: company_info_of_lark.name,
                CompanyKey.AVATAR: company_info_of_lark.avatar.avatar_origin,
            }
        )
    else:
        session_db.add(
            CompanyModel(
                tenant_key=company_info_of_lark.tenant_key,
                name=company_info_of_lark.name,
                avatar=company_info_of_lark.avatar.avatar_origin,
            )
        )
    session_db.commit()

    company_info = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark.tenant_key).first()
    print(company_info.company_id)


async def y():
    session_db: Session = next(get_db())
    session_db.query(JobTitleModel).filter(
        JobTitleModel.job_title_id.in_(
            [
                "44d81097-b2ac-4439-8459-a4b54c4ca00b",
                "d6f80d72-2f27-49c8-8b5b-74fb0a4fe45b",
                "ccb8f66f-be31-4b2c-a788-d042b7fe661d",
            ]
        )
    ).delete()
    session_db.commit()
    return


if __name__ == "__main__":
    asyncio.run(y(), debug=True)
