#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 19/08/2024
"""

import asyncio
import sys

from mobio.libs.logging import MobioLogging

from src.models.mongo.setting_model import SettingModel


async def init_setting(app_id, app_secret, app_name, authorization_url, redirect_uri, domain_api):

    setting_model: SettingModel = SettingModel()

    data_insert = {
        "app_id": app_id,
        "app_secret": app_secret,
        "app_name": app_name,
        "authorization_url": authorization_url,
        "redirect_uri": redirect_uri,
        "domain_api": domain_api,
    }
    MobioLogging().info(f"init_setting :: data_insert :: {data_insert}")

    setting_detail = await setting_model.find_one({})
    if setting_detail:
        MobioLogging().info(f"init_setting :: setting_detail :: {setting_detail}")
        status_update = await setting_model.update_by_set({"_id": setting_detail.get("_id")}, update_option=data_insert)
        MobioLogging().info(f"init_setting :: status_update :: {status_update.matched_count}")
    else:
        inserted_id = await setting_model.insert_document(data_insert)
        MobioLogging().info(f"init_setting :: inserted_id :: {inserted_id}")


if __name__ == "__main__":
    app_id = sys.argv[1] if len(sys.argv) > 1 else None
    app_secret = sys.argv[2] if len(sys.argv) > 2 else None
    app_name = sys.argv[3] if len(sys.argv) > 3 else None
    authorization_url = sys.argv[4] if len(sys.argv) > 4 else None
    redirect_uri = sys.argv[5] if len(sys.argv) > 5 else None
    domain_api = sys.argv[6] if len(sys.argv) > 6 else None
    asyncio.run(
        init_setting(
            app_id=app_id,
            app_secret=app_secret,
            app_name=app_name,
            authorization_url=authorization_url,
            redirect_uri=redirect_uri,
            domain_api=domain_api,
        )
    )
