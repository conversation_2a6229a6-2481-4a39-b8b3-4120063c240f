#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 26/08/2024
"""

import asyncio

from sqlalchemy.orm import Session

from configs.database import get_db
from src.models.postgres.base_model import Role<PERSON>ode<PERSON>, UserModel


async def update_leader():

    session_db: Session = next(get_db())

    mapping_user_role_leader = {
        "6671956c-ed17-43c9-bd4c-764d7cdc56c1": False,
        "bc48e7ca-9fa7-4bc2-a16c-6f9720cea8e3": True,
    }
    user_ids = list(mapping_user_role_leader.keys())

    # leader_role = session_db.query(RoleModel).filter_by(lower_case_name="leader").first()
    # role_user = session_db.query(RoleModel).filter_by(name="user").first()
    admin_user = session_db.query(RoleModel).filter_by(name="admin").first()

    users = session_db.query(UserModel).filter(UserModel.user_id.in_(user_ids)).all()
    for user in users:
        user_id = user.user_id
        if mapping_user_role_leader.get(user_id):
            user.roles.append(admin_user)
        if user_id in mapping_user_role_leader and not mapping_user_role_leader[user_id]:
            if admin_user in user.roles:
                user.roles.remove(admin_user)
    session_db.commit()


if __name__ == "__main__":
    asyncio.run(update_leader(), debug=True)
