#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/09/2024
"""


import asyncio

from mobio.libs.logging import MobioLogging
from sqlalchemy.orm import Session

from configs.database import get_db
from src.common.choices import RoleChoice
from src.models.postgres.base_model import (
    DepartmentModel,
    RoleModel,
    UserDepartmentModel,
    UserModel,
)


async def c():
    mapping_owner = {
        "0d1a58ca-959c-4fb5-9d21-03d8216bb397": ["0b1a664a-2c53-4bdd-97ba-811592d97be7"],
        "ebf1185d-e880-42b8-b232-50ec415e6af2": ["0b1a664a-2c53-4bdd-97ba-811592d97be7"],
        "83828ee4-eb99-424f-b351-54ae477ad1a6": ["7c75ccb9-5df3-41ce-a103-7987684f3704"],
        "d81bc45f-9302-448a-a505-4cf2cc1a162d": ["7c75ccb9-5df3-41ce-a103-7987684f3704"],
        "db64a793-8d6d-45ba-841c-6a3556ebdcf3": ["0b1a664a-2c53-4bdd-97ba-811592d97be7"],
        "695c2d0f-8d32-4e12-85f0-cf1753bfce95": ["0b1a664a-2c53-4bdd-97ba-811592d97be7"],
        "81488d8f-b442-427d-8fff-7a997ac22735": ["0b1a664a-2c53-4bdd-97ba-811592d97be7"],
        "06e7fab4-ecdb-460c-8cc5-8307a2d17cde": ["02970114-3ce8-421b-9047-52f11d71eb29"],
        "d499d7b9-4d6e-46eb-aedc-ca7614a88025": ["02970114-3ce8-421b-9047-52f11d71eb29"],
        "fa2df222-9356-421e-b8e5-4738ad15218d": ["02970114-3ce8-421b-9047-52f11d71eb29"],
        "84a32b5c-6860-47f3-9a87-adce607f92eb": [
            "02970114-3ce8-421b-9047-52f11d71eb29",
            "43febd5a-5616-4ae7-9630-37fa74ee5eb6",
        ],
        "9c071396-b7ea-498e-8e44-176437230efd": ["02970114-3ce8-421b-9047-52f11d71eb29"],
        "fc2e3b3d-7117-4dd9-b7fb-c45df5e9f82c": ["02970114-3ce8-421b-9047-52f11d71eb29"],
        "01c78757-c1fa-49b5-a22a-4e95cca787f9": ["0b1a664a-2c53-4bdd-97ba-811592d97be7"],
        "14ea32df-03e5-4e7f-be8f-0c869ac6ec29": ["0b1a664a-2c53-4bdd-97ba-811592d97be7"],
        "a3bcbe87-9042-4345-a5d0-b9fd97391001": ["02970114-3ce8-421b-9047-52f11d71eb29"],
        "5bf5f182-0820-4355-b67c-fb32b36c188b": [],
    }
    session_db: Session = next(get_db())
    departments_in_system = session_db.query(DepartmentModel).all()
    for department_system in departments_in_system:
        owners = mapping_owner.get(department_system.department_id)
        MobioLogging().info("owners: {}".format(owners))
        department_system.owners = owners
        session_db.commit()


async def d():
    session_db: Session = next(get_db())
    users_in_system = session_db.query(UserModel).all()
    role_user = session_db.query(RoleModel).filter_by(name=RoleChoice.ADMIN.value).first()
    for user in users_in_system:
        if user.user_id == "bc48e7ca-9fa7-4bc2-a16c-6f9720cea8e3":
            user.roles = [role_user]
            session_db.commit()
            break


async def update_department_devops():
    session_db: Session = next(get_db())
    users_in_system = session_db.query(UserDepartmentModel).all()
    # role_user = session_db.query(RoleModel).filter_by(name=RoleChoice.ADMIN.value).first()
    for user in users_in_system:
        if user.user_id in ["ba233c8b-07bd-4c78-8697-c40338b3c626", "3cdf67c1-370e-4861-bc3b-4bd48a345f72"]:
            # user.roles = [role_user]
            user.department_id = "81488d8f-b442-427d-8fff-7a997ac22735"
            session_db.commit()
            break


if __name__ == "__main__":
    asyncio.run(d(), debug=True)
