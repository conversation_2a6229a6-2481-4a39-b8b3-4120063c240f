import asyncio
import datetime

from mobio.libs.logging import <PERSON><PERSON>Logging
from sqlalchemy.orm import Session

from configs.database import get_db
from src.common.common import COMMON, CommonKey, EvaluatePeriodEnum, EvaluatePeriodKey
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.repositories.company_repository import CompanyRepository

list_evaluate_period_template_default = [
    {
        EvaluatePeriodKey.START_TIME: datetime.datetime(COMMON.DEFAULT_YEAR, 6, 1, 0, 0, 0, tzinfo=datetime.UTC),
        EvaluatePeriodKey.END_TIME: datetime.datetime(COMMON.DEFAULT_YEAR, 9, 30, 23, 59, 59, tzinfo=datetime.UTC),
        EvaluatePeriodKey.NAME: "",
        EvaluatePeriodKey.REPEAT_TYPE: EvaluatePeriodEnum.RepeatType.YEARLY,
        EvaluatePeriodKey.CONFIG: {
            "employee_evaluate_interval_day": 5,
            "leader_evaluate_interval_day": 25,
        },
        EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS: [],
        EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS: [],
        EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE: None,
        EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE: None,
        EvaluatePeriodKey.COMPANY_ID: None,
        EvaluatePeriodKey.MONTH_REVIEW: EvaluatePeriodEnum.Type.NUMBER_MONTH_SIX,
    },
    {
        EvaluatePeriodKey.START_TIME: datetime.datetime(COMMON.DEFAULT_YEAR, 12, 1, 0, 0, 0, tzinfo=datetime.UTC),
        EvaluatePeriodKey.END_TIME: datetime.datetime(COMMON.DEFAULT_YEAR, 3, 31, 23, 59, 59, tzinfo=datetime.UTC),
        EvaluatePeriodKey.REPEAT_TYPE: EvaluatePeriodEnum.RepeatType.YEARLY,
        EvaluatePeriodKey.NAME: "",
        EvaluatePeriodKey.CONFIG: {
            "employee_evaluate_interval_day": 5,
            "leader_evaluate_interval_day": 25,
        },
        EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS: [],
        EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS: [],
        EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE: None,
        EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE: None,
        EvaluatePeriodKey.MONTH_REVIEW: EvaluatePeriodEnum.Type.NUMBER_MONTH_TWELVE,
    },
]


def _convert_interval_default_time_to_current_time(default_start_time, default_end_time):
    current_year = datetime.datetime.now(datetime.UTC).year
    # Replace current year
    current_start_time = default_start_time.replace(year=current_year)
    current_end_time = default_end_time.replace(year=current_year)
    # Check extend to next year
    if current_end_time <= current_start_time:
        current_end_time = current_end_time.replace(year=current_end_time.year + 1)
    return current_start_time, current_end_time


async def ensure_default_evaluate_period(list_evaluate_period_template=list_evaluate_period_template_default):
    log_prefix = "ensure_default_evaluate_period"
    time_now = datetime.datetime.now(datetime.UTC)
    MobioLogging().info(f"{log_prefix}::start::time_now::{str(time_now)}")
    session_db: Session = next(get_db())
    created_by = "admin"
    evaluate_period_model = EvaluatePeriodModel()
    company_repo: CompanyRepository = CompanyRepository(session_db)

    # ================================================== Init evaluate period ================================================== #
    for evaluate_period_data in list_evaluate_period_template:

        # Get actual time
        actual_evaluate_period_start_time, actual_evaluate_period_end_time = (
            _convert_interval_default_time_to_current_time(
                evaluate_period_data[EvaluatePeriodKey.START_TIME], evaluate_period_data[EvaluatePeriodKey.END_TIME]
            )
        )

        actual_evaluate_period_start_time_month = actual_evaluate_period_start_time.month
        month_review = None
        if 1 <= actual_evaluate_period_start_time_month <= 6:
            month_review = EvaluatePeriodEnum.Type.NUMBER_MONTH_SIX
        elif 7 <= actual_evaluate_period_start_time_month <= 12:
            month_review = EvaluatePeriodEnum.Type.NUMBER_MONTH_TWELVE

        # Name:
        name = evaluate_period_data[EvaluatePeriodKey.NAME]
        if name:
            name += f" tháng {actual_evaluate_period_start_time.month}/{actual_evaluate_period_start_time.year}"
        else:
            name = f"Tháng {actual_evaluate_period_start_time.month}/{actual_evaluate_period_start_time.year}"

        name = name.strip()

        list_company = await company_repo.get_all_company(company_ids=[])

        # Start time aggregate performance
        start_time_aggregate_performance = None
        if evaluate_period_data.get(EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE):
            start_time_aggregate_performance = evaluate_period_data.get(
                EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE
            )

        # End time aggregate performance
        end_time_aggregate_performance = None
        if evaluate_period_data.get(EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE):
            end_time_aggregate_performance = evaluate_period_data.get(EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE)

        if not start_time_aggregate_performance or not end_time_aggregate_performance:
            if month_review == EvaluatePeriodEnum.Type.NUMBER_MONTH_SIX:
                start_time_aggregate_performance = actual_evaluate_period_start_time.replace(
                    month=1, day=1, hour=0, minute=0, second=0
                )
                end_time_aggregate_performance = actual_evaluate_period_start_time.replace(
                    month=6, day=30, hour=23, minute=59, second=59
                )
            elif month_review == EvaluatePeriodEnum.Type.NUMBER_MONTH_TWELVE:
                start_time_aggregate_performance = actual_evaluate_period_start_time.replace(
                    month=7, day=1, hour=0, minute=0, second=0
                )
                end_time_aggregate_performance = actual_evaluate_period_start_time.replace(
                    month=12, day=31, hour=23, minute=59, second=59
                )

        for company in list_company:
            MobioLogging().info(f"{log_prefix}::company::{company.name}")
            # Fetch data
            evaluate_period = await evaluate_period_model.get_evaluate_period_by_start_time(
                company.company_id, actual_evaluate_period_start_time
            )
            # Check existed evaluate period
            if evaluate_period:
                evaluate_period_id = evaluate_period["_id"]
                MobioLogging().debug(f"{log_prefix}::evaluate_period::{evaluate_period}::already_exists")
                # Update evaluate period
                data_update = {}
                if evaluate_period.get("start_time") != actual_evaluate_period_start_time:
                    data_update[EvaluatePeriodKey.START_TIME] = actual_evaluate_period_start_time
                if evaluate_period.get("end_time") != actual_evaluate_period_end_time:
                    data_update[EvaluatePeriodKey.END_TIME] = actual_evaluate_period_end_time
                if evaluate_period.get("name") != name:
                    data_update[EvaluatePeriodKey.NAME] = name
                if evaluate_period.get("config") != evaluate_period_data[EvaluatePeriodKey.CONFIG]:
                    data_update[EvaluatePeriodKey.CONFIG] = evaluate_period_data[EvaluatePeriodKey.CONFIG]
                if evaluate_period.get("exclude_department_ids", []) != evaluate_period_data.get(
                    EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS, []
                ):
                    data_update[EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS] = evaluate_period_data.get(
                        EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS, []
                    )
                if evaluate_period.get("specific_department_ids", []) != evaluate_period_data.get(
                    EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS, []
                ):
                    data_update[EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS] = evaluate_period_data.get(
                        EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS, []
                    )
                if data_update:
                    data_update.update(
                        {
                            CommonKey.UPDATED_TIME: time_now,
                            CommonKey.UPDATED_BY: created_by,
                        }
                    )
                    await evaluate_period_model.update_one_query(
                        {
                            CommonKey.ID: evaluate_period_id,
                        },
                        data_update,
                    )
                    MobioLogging().debug(f"{log_prefix}::{name}::data_update::{data_update}::updated")
                else:
                    MobioLogging().debug(f"{log_prefix}::{name}::not_updated")
            else:
                # Create new
                evaluate_period_data_insert = {
                    EvaluatePeriodKey.START_TIME: actual_evaluate_period_start_time,
                    EvaluatePeriodKey.END_TIME: actual_evaluate_period_end_time,
                    EvaluatePeriodKey.REPEAT_TYPE: evaluate_period_data[EvaluatePeriodKey.REPEAT_TYPE],
                    EvaluatePeriodKey.CONFIG: evaluate_period_data.get(EvaluatePeriodKey.CONFIG, {}),
                    EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS: evaluate_period_data.get(
                        EvaluatePeriodKey.EXCLUDE_DEPARTMENT_IDS, []
                    ),
                    EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS: evaluate_period_data.get(
                        EvaluatePeriodKey.SPECIFIC_DEPARTMENT_IDS, []
                    ),
                    EvaluatePeriodKey.NAME: name,
                    EvaluatePeriodKey.START_TIME_AGGREGATE_PERFORMANCE: start_time_aggregate_performance,
                    EvaluatePeriodKey.END_TIME_AGGREGATE_PERFORMANCE: end_time_aggregate_performance,
                    CommonKey.COMPANY_ID: company.company_id,
                    CommonKey.CREATED_TIME: time_now,
                    CommonKey.UPDATED_TIME: time_now,
                    CommonKey.CREATED_BY: created_by,
                    CommonKey.UPDATED_BY: created_by,
                    EvaluatePeriodKey.STATUS: 1,
                    EvaluatePeriodKey.MONTH_REVIEW: month_review,
                }
                inserted_result = await evaluate_period_model.insert_evaluate_period(evaluate_period_data_insert)
                MobioLogging().debug(
                    f"{log_prefix}::evaluate_period::{evaluate_period_data}::created::{inserted_result}"
                )

    MobioLogging().info(f"{log_prefix}::finished")


if __name__ == "__main__":
    asyncio.run(ensure_default_evaluate_period(), debug=True)
