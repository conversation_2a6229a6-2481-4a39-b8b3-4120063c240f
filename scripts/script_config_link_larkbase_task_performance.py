#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 29/05/2025
"""

import asyncio
import sys

from mobio.libs.logging import MobioLogging

from src.models.mongo.setting_model import SettingModel


async def config_link_larkbase_task_performance(link_larkbase_task_performance, table_id):

    setting_model: SettingModel = SettingModel()

    data_insert = {
        "link_larkbase_task_performance": {
            "url": link_larkbase_task_performance,
            "table_id": table_id,
        },
    }
    MobioLogging().info(f"config_link_larkbase_task_performance :: data_insert :: {data_insert}")

    setting_detail = await setting_model.find_one({})
    if setting_detail:
        MobioLogging().info(f"config_link_larkbase_task_performance :: setting_detail :: {setting_detail}")
        status_update = await setting_model.update_by_set({"_id": setting_detail.get("_id")}, update_option=data_insert)
        MobioLogging().info(f"config_link_larkbase_task_performance :: status_update :: {status_update.matched_count}")
    else:
        inserted_id = await setting_model.insert_document(data_insert)
        MobioLogging().info(f"config_link_larkbase_task_performance :: inserted_id :: {inserted_id}")


if __name__ == "__main__":
    link_larkbase_task_performance = sys.argv[1] if len(sys.argv) > 1 else None
    table_id = sys.argv[2] if len(sys.argv) > 2 else None
    asyncio.run(
        config_link_larkbase_task_performance(
            link_larkbase_task_performance=link_larkbase_task_performance,
            table_id=table_id,
        )
    )
