# Permission System Architecture

```mermaid
C4Context
title AWS-Style Permission System Architecture

Person(user, "Application User", "A user making API requests")
System(app_api, "API Endpoints", "Application services secured by permissions")

Boundary(permission_system, "Permission System") {
    Component(auth_middleware, "Authorization Middleware", "Intercepts API requests and enforces permissions")
    Component(policy_evaluator, "Policy Evaluator", "Evaluates if a user has permission for an action on a resource")
    Component(permission_decorators, "Permission Decorators", "API endpoint decorators for specifying required permissions")
    ComponentDb(policy_db, "Policy Database", "Stores policy documents and assignments")
}

Rel(user, app_api, "Makes requests to", "HTTP")
Rel(app_api, auth_middleware, "Passes requests through")
Rel(auth_middleware, policy_evaluator, "Asks for permission evaluation")
Rel(policy_evaluator, policy_db, "Retrieves policies")
Rel(app_api, permission_decorators, "Uses", "For specifying required permissions")
Rel(permission_decorators, auth_middleware, "Configures")

UpdateLayoutConfig($c4ShapeInRow="3", $c4BoundaryInRow="1")
```

## Policy Evaluation Flow

```mermaid
sequenceDiagram
    participant User
    participant API as API Endpoint
    participant Auth as Authorization Middleware
    participant Evaluator as Policy Evaluator
    participant DB as Policy Database
    
    User->>API: Makes request
    API->>Auth: Pass request
    Auth->>Evaluator: Check permission (user, action, resource)
    Evaluator->>DB: Get user policies
    DB-->>Evaluator: User policies
    Evaluator->>DB: Get user roles
    DB-->>Evaluator: User roles
    Evaluator->>DB: Get role policies
    DB-->>Evaluator: Role policies
    
    Note over Evaluator: Evaluate all policies
    Note over Evaluator: Apply evaluation rules
    
    Evaluator-->>Auth: Permission result (Allow/Deny)
    
    alt Permission Allowed
        Auth-->>API: Continue request
        API-->>User: Process request normally
    else Permission Denied
        Auth-->>User: 403 Access Denied
    end
```

## Policy Data Model

```mermaid
erDiagram
    User ||--o{ UserPolicy : has
    User ||--o{ UserRole : has
    Role ||--o{ UserRole : has
    Role ||--o{ RolePolicy : has
    Policy ||--o{ UserPolicy : has
    Policy ||--o{ RolePolicy : has
    
    User {
        string user_id PK
        string name
        string primary_email
    }
    
    Role {
        string role_id PK
        string name
        string description
    }
    
    Policy {
        string policy_id PK
        string name
        string description
        string policy_type
        json document
    }
    
    UserPolicy {
        string user_policy_id PK
        string user_id FK
        string policy_id FK
    }
    
    RolePolicy {
        string role_policy_id PK
        string role_id FK
        string policy_id FK
    }
    
    UserRole {
        string user_role_id PK
        string user_id FK
        string role_id FK
    }
``` 