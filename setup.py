from distutils.core import setup

from Cython.Build import cythonize

SOURCE_PATH = "./src"
setup(
    name="Mobio",
    author="Mobio Company",
    author_email="<EMAIL>",
    version="1.0",
    ext_modules=cythonize(
        [SOURCE_PATH + "/**/*.py", "./configs/**/*.py"],
        exclude=[SOURCE_PATH + "/routers/**/*.py"],
        compiler_directives=dict(always_allow_keywords=True, language_level=3),
    ),
)
