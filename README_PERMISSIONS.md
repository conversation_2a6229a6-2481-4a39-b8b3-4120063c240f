# AWS-Style Permission System

This document outlines the AWS-style permission system implemented for API access control. The system is modeled after AWS Identity and Access Management (IAM) and provides granular control over who can access specific resources and what actions they can perform.

## Table of Contents

- [Overview](#overview)
- [Core Concepts](#core-concepts)
  - [Policies](#policies)
  - [Actions](#actions)
  - [Resources](#resources)
  - [Effects](#effects)
  - [Conditions](#conditions)
- [Architecture](#architecture)
- [Policy Evaluation Logic](#policy-evaluation-logic)
- [API Reference](#api-reference)
- [Usage Examples](#usage-examples)
  - [Creating Policies](#creating-policies)
  - [Managing Permissions](#managing-permissions)
  - [Evaluating Permissions](#evaluating-permissions)
- [Integration Guide](#integration-guide)

## Overview

The permission system provides a flexible and powerful way to control access to APIs and resources within the application. It follows AWS IAM principles where permissions are defined in JSON policy documents that specify who can access what resources under what conditions.

Key features include:
- JSON-based policy documents
- Granular control with Allow/Deny statements
- Policy attachment to users or roles
- Resource-level permissions
- Conditional access control

## Core Concepts

### Policies

A policy is a JSON document that defines permissions. Each policy consists of one or more statements, each specifying:

```json
{
  "Version": "2023-01-01",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["user:List", "user:Read"],
      "Resource": "arn:app:user:*",
      "Condition": {
        "StringEquals": {
          "user:department_id": "${department:id}"
        }
      }
    }
  ]
}
```

### Actions

Actions represent operations that can be performed on resources. They follow a service:action format:

- `user:List` - List users
- `user:Read` - Read user details
- `policy:*` - All policy operations
- `department:Create` - Create a department

### Resources

Resources are the entities being accessed. They are identified using Amazon Resource Names (ARNs):

- `arn:app:user:*` - All users
- `arn:app:user:123` - A specific user with ID 123
- `arn:app:department:456` - A specific department

### Effects

Each statement specifies an effect:
- `Allow`: Explicitly allows the action
- `Deny`: Explicitly denies the action (overrides any Allow)

### Conditions

Conditions add constraints to when a policy applies:

```json
"Condition": {
  "StringEquals": {
    "user:department_id": "${department:id}"
  }
}
```

This condition only allows the action if the user belongs to the same department as the resource.

## Architecture

The permission system consists of several components:

1. **Policy Model** - Database model for storing policies
2. **Policy Evaluator** - Engine that evaluates if a user has permission
3. **Authorization Middleware** - Intercepts requests and checks permissions
4. **Permission Decorators** - Easy-to-use decorators for protecting endpoints


## Policy Evaluation Logic

When evaluating if a user can perform an action:

1. The system collects all policies:
   - Policies directly attached to the user
   - Policies attached to the user's roles
   
2. The policies are evaluated according to these rules:
   - Default is DENY
   - If ANY policy has an explicit DENY, the result is DENY
   - If NO policy has an explicit DENY and AT LEAST ONE policy has an ALLOW, the result is ALLOW

## API Reference

The permission system exposes several API endpoints for managing policies:

### Policy Management APIs

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/policies` | List all policies |
| GET | `/policies/{policy_id}` | Get policy details |
| POST | `/policies` | Create a new policy |
| PUT | `/policies/{policy_id}` | Update a policy |
| DELETE | `/policies/{policy_id}` | Delete a policy |
| POST | `/policies/create-defaults` | Create default policies |

### Policy Assignment APIs

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/users/{user_id}/policies` | Attach policy to user |
| POST | `/users/{user_id}/policies/detach` | Detach policy from user |
| POST | `/roles/{role_id}/policies` | Attach policy to role |
| POST | `/roles/{role_id}/policies/detach` | Detach policy from role |

### Evaluation API

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/users/{user_id}/evaluate-policy` | Evaluate if a user has permission |

## Usage Examples

### Creating Policies

#### Admin Policy

```bash
curl -X POST "http://localhost:8000/api/v1.0/policies" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "AdminPolicy",
    "description": "Full administrator access",
    "policy_type": "IDENTITY",
    "document": {
      "Version": "2023-01-01",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": "*",
          "Resource": "*"
        }
      ]
    }
  }'
```

#### Read-Only Policy

```bash
curl -X POST "http://localhost:8000/api/v1.0/policies" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ReadOnlyPolicy",
    "description": "Read-only access to all resources",
    "policy_type": "IDENTITY",
    "document": {
      "Version": "2023-01-01",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": [
            "*:List*",
            "*:Get*",
            "*:Describe*",
            "*:Read*"
          ],
          "Resource": "*"
        }
      ]
    }
  }'
```

### Managing Permissions

#### Attach Policy to User

```bash
curl -X POST "http://localhost:8000/api/v1.0/users/user-id-here/policies" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -H "Content-Type: application/json" \
  -d '{
    "policy_id": "policy-id-here"
  }'
```

#### Attach Policy to Role

```bash
curl -X POST "http://localhost:8000/api/v1.0/roles/role-id-here/policies" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -H "Content-Type: application/json" \
  -d '{
    "policy_id": "policy-id-here"
  }'
```

### Evaluating Permissions

```bash
curl -X POST "http://localhost:8000/api/v1.0/users/user-id-here/evaluate-policy" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "user:Read",
    "resource": "arn:app:user:123",
    "context": {
      "request_method": "GET"
    }
  }'
```

## Integration Guide

### 1. Protecting API Endpoints

To protect an API endpoint, use the permission decorators:

```python
from src.auth.permissions import requires_permission

@app.get("/users/{user_id}")
@requires_permission("user:Read", "arn:app:user:{user_id}")
async def get_user(user_id: str):
    # Implementation here
```

### 2. Creating Custom Permission Patterns

You can create custom permission patterns:

```python
def can_manage_users_in_department(department_id):
    return requires_permission(
        "user:Update", 
        "arn:app:user:*", 
        lambda request, **kwargs: {"department_id": department_id}
    )
```

### 3. Initializing Default Policies

When setting up the system, create default policies:

```bash
curl -X POST "http://localhost:8000/api/v1.0/policies/create-defaults" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)"
```

This creates policies like:
- AdministratorAccess
- ReadOnlyAccess
- UserManagerAccess
- DepartmentManagerAccess
- SelfServiceAccess

### 4. Best Practices

- Follow the principle of least privilege
- Use roles for groups of users
- Use explicit Deny for critical resources
- Define clear naming conventions for actions
- Use conditions for context-based access control 