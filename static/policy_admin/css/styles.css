/* General Styles */
body {
    background-color: #f8f9fa;
}

/* Login Form */
.card {
    border-radius: 10px;
    border: none;
}

.card-title {
    color: #343a40;
    font-weight: 600;
}

/* Dashboard */
.navbar-brand {
    font-weight: 600;
}

.page {
    transition: all 0.3s ease;
}

/* Policy Table */
.table th {
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

.action-btn {
    margin-right: 5px;
}

/* Policy Modal */
#policy-document {
    font-family: monospace;
    font-size: 0.9rem;
}

/* Assign Policy Modal */
.assigned-policies-list, 
.available-policies-list {
    max-height: 300px;
    overflow-y: auto;
}

.policy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.policy-item button {
    margin-left: 10px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
} 