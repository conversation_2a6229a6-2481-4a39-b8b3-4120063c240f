<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="/api/v1.0/policy-admin/">
    <title>Policy Management - Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/styles.css">
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">Policy Management</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-page="policies">Policies</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="users">Users</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-page="roles">Roles</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <span class="nav-link text-light"><i class="bi bi-person-fill"></i> Admin</span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logout">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Policies Page -->
        <div class="page" id="policies-page">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Policies</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#policyModal"
                    id="new-policy-btn">
                    <i class="bi bi-plus-lg"></i> New Policy
                </button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="policies-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="policies-list">
                                <!-- Policies will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>


        <!-- Users Page -->
        <div class="page d-none" id="users-page">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Users</h2>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="users-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="users-list">
                                <!-- Users will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Roles Page -->
        <div class="page d-none" id="roles-page">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Roles</h2>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="roles-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="roles-list">
                                <!-- Roles will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Policy Modal -->
    <div class="modal fade" id="policyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="policyModalTitle">Create New Policy</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="policy-form">
                        <input type="hidden" id="policy-id">
                        <div class="mb-3">
                            <label for="policy-name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="policy-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="policy-description" class="form-label">Description</label>
                            <input type="text" class="form-control" id="policy-description">
                        </div>
                        <div class="mb-3">
                            <label for="policy-type" class="form-label">Type</label>
                            <select class="form-select" id="policy-type">
                                <option value="IDENTITY">Identity-based</option>
                                <option value="RESOURCE">Resource-based</option>
                                <option value="PERMISSION_BOUNDARY">Permission boundary</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="policy-document" class="form-label">Policy Document (JSON)</label>
                            <textarea class="form-control" id="policy-document" rows="10" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-policy-btn">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Assign Policy Modal -->
    <div class="modal fade" id="assignPolicyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Assign Policies</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="assign-id">
                    <input type="hidden" id="assign-type">
                    <div class="mb-3">
                        <div class="assigned-policies-list mb-3">
                            <h6>Assigned Policies</h6>
                            <ul class="list-group" id="assigned-policies">
                                <!-- Assigned policies will be loaded here -->
                            </ul>
                        </div>
                        <div class="available-policies-list">
                            <h6>Available Policies</h6>
                            <ul class="list-group" id="available-policies">
                                <!-- Available policies will be loaded here -->
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/api-permissions.js"></script>
</body>

</html>