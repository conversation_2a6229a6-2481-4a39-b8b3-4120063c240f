# Policy Management Admin Interface

This is a web-based interface for managing AWS-style IAM policies in your application. It allows administrators to create, edit, and delete policies, as well as assign them to users and roles.

## Features

- **User Authentication**: Login with username and password (Basic Authentication)
- **Policy Management**: Create, edit, and delete policies
- **User Management**: View users and assign policies to them
- **Role Management**: View roles and assign policies to them
- **Policy Templates**: Create default policies or use templates for new policies

## Installation

1. Copy all files from the `static/policy_admin` directory to your web server's static files directory.
2. Ensure the backend API endpoints are configured correctly in `js/api.js`.
3. Make sure your backend has implemented all the required API endpoints with Basic Authentication.

## Usage

1. Navigate to the admin interface in your browser.
2. Log in with your username and password.
3. Use the navigation tabs to switch between policies, users, and roles.

### Managing Policies

- **View Policies**: The Policies tab shows all existing policies.
- **Create New Policy**: Click the "New Policy" button to create a policy.
- **Edit Policy**: Click the edit icon next to a policy to modify it.
- **Delete Policy**: Click the delete icon next to a policy to remove it.
- **Create Default Policies**: If no policies exist, you can click "Create default policies" to generate a set of standard policies.

### Managing User Permissions

- **View Users**: The Users tab shows all users in the system.
- **Assign Policies**: Click "Manage Policies" next to a user to assign or remove policies.
- **Add/Remove Policies**: In the Assign Policies modal, you can add policies from the available list or remove assigned policies.

### Managing Role Permissions

- **View Roles**: The Roles tab shows all roles in the system.
- **Assign Policies**: Click "Manage Policies" next to a role to assign or remove policies.
- **Add/Remove Policies**: In the Assign Policies modal, you can add policies from the available list or remove assigned policies.

## Policy Structure

Policies follow the AWS IAM format:

```json
{
  "Version": "2023-01-01",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["service:Action"],
      "Resource": ["arn:app:resource:*"]
    }
  ]
}
```

- **Effect**: "Allow" or "Deny"
- **Action**: What actions are allowed/denied, using the format "service:action"
- **Resource**: What resources the policy applies to, using ARN format
- **Condition** (optional): Additional conditions for when the policy applies

## API Requirements

This interface requires the following API endpoints:

- **Authentication**:
  - The system uses Basic Authentication for all API requests

- **Policy APIs**:
  - `GET /api/v1.0/policies` - List all policies
  - `GET /api/v1.0/policies/{policy_id}` - Get a specific policy
  - `POST /api/v1.0/policies` - Create a new policy
  - `PUT /api/v1.0/policies/{policy_id}` - Update a policy
  - `DELETE /api/v1.0/policies/{policy_id}` - Delete a policy
  - `POST /api/v1.0/policies/create-defaults` - Create default policies

- **User APIs**:
  - `GET /api/v1.0/users` - List all users
  - `GET /api/v1.0/users/{user_id}` - Get a specific user
  - `POST /api/v1.0/users/{user_id}/policies` - Attach a policy to a user
  - `POST /api/v1.0/users/{user_id}/policies/detach` - Detach a policy from a user

- **Role APIs**:
  - `GET /api/v1.0/roles` - List all roles
  - `POST /api/v1.0/roles/{role_id}/policies` - Attach a policy to a role
  - `POST /api/v1.0/roles/{role_id}/policies/detach` - Detach a policy from a role

## Security Considerations

- This interface should be deployed on a secure connection (HTTPS).
- Access should be restricted to administrators only.
- The API endpoints should validate all requests and enforce proper authorization.
- Basic Authentication credentials should be strong and changed regularly. 