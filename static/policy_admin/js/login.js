document.addEventListener('DOMContentLoaded', function () {
    // Check if user is already logged in
    if (api.loadCredentials()) {
        // Verify login is still valid
        api.getPolicies()
            .then(() => {
                // Redirect to dashboard
                window.location.href = 'dashboard.html';
            })
            .catch(() => {
                // Clear invalid credentials
                api.clearCredentials();
            });
    }

    // Handle login form submission
    const loginForm = document.getElementById('login-form');
    const loginError = document.getElementById('login-error');

    // Add default admin/admin credentials to help users
    document.getElementById('username').value = 'admin';
    document.getElementById('password').value = 'admin';

    loginForm.addEventListener('submit', async function (event) {
        event.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        try {
            // Show loading state
            document.querySelector('button[type="submit"]').textContent = 'Logging in...';
            document.querySelector('button[type="submit"]').disabled = true;

            // For admin interface, only allow admin/admin login
            if (username === 'admin' && password === 'admin') {
                // Attempt login
                const success = await api.login(username, password);

                if (success) {
                    // Redirect to dashboard
                    window.location.href = 'dashboard.html';
                } else {
                    // Show error
                    loginError.classList.remove('d-none');
                    document.querySelector('button[type="submit"]').textContent = 'Login';
                    document.querySelector('button[type="submit"]').disabled = false;
                }
            } else {
                // Show error for incorrect credentials
                loginError.classList.remove('d-none');
                document.querySelector('button[type="submit"]').textContent = 'Login';
                document.querySelector('button[type="submit"]').disabled = false;
            }
        } catch (error) {
            // Show error
            loginError.classList.remove('d-none');
            document.querySelector('button[type="submit"]').textContent = 'Login';
            document.querySelector('button[type="submit"]').disabled = false;
        }
    });
}); 