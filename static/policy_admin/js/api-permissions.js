/**
 * API Permissions Management
 */
document.addEventListener('DOMContentLoaded', function () {
    // Initialize API permissions tab if user is logged in
    if (api.loadCredentials()) {
        // Setup API permission modal
        setupApiPermissionModal();
    }
});

// Load API permissions list
async function loadApiPermissions() {
    try {
        const apiPermissions = await api.getApiPermissions();
        const apiPermissionsList = document.getElementById('api-permissions-list');

        apiPermissionsList.innerHTML = '';

        if (apiPermissions.length === 0) {
            apiPermissionsList.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">No API permissions found</td>
                </tr>
            `;
            return;
        }

        apiPermissions.forEach(permission => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${permission.path_pattern}</td>
                <td>${permission.method}</td>
                <td>${permission.description || '-'}</td>
                <td>${permission.is_enabled ?
                    '<span class="badge bg-success">Enabled</span>' :
                    '<span class="badge bg-danger">Disabled</span>'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary action-btn edit-api-permission" data-id="${permission.id}">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger action-btn delete-api-permission" data-id="${permission.id}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            apiPermissionsList.appendChild(row);
        });

        // Add event listeners for edit and delete buttons
        document.querySelectorAll('.edit-api-permission').forEach(button => {
            button.addEventListener('click', function () {
                const apiPermissionId = this.getAttribute('data-id');
                editApiPermission(apiPermissionId);
            });
        });

        document.querySelectorAll('.delete-api-permission').forEach(button => {
            button.addEventListener('click', function () {
                const apiPermissionId = this.getAttribute('data-id');
                deleteApiPermission(apiPermissionId);
            });
        });
    } catch (error) {
        alert(`Error loading API permissions: ${error.message}`);
    }
}

// Setup API permission modal
function setupApiPermissionModal() {
    const apiPermissionModalElement = document.getElementById('apiPermissionModal');
    const apiPermissionModal = new bootstrap.Modal(apiPermissionModalElement);
    const saveButton = document.getElementById('save-api-permission-btn');
    const newApiPermissionButton = document.getElementById('new-api-permission-btn');
    const addPolicyButton = document.getElementById('add-policy-btn');

    // Load all policies when opening the modal
    let allPolicies = [];

    // Setup new API permission button
    newApiPermissionButton.addEventListener('click', async function () {
        document.getElementById('apiPermissionModalTitle').textContent = 'Create New API Permission';
        document.getElementById('api-permission-id').value = '';
        document.getElementById('path-pattern').value = '';
        document.getElementById('http-method').value = 'GET';
        document.getElementById('api-description').value = '';
        document.getElementById('is-enabled').checked = true;
        document.getElementById('policies-container').innerHTML = '';

        // Load policies for the dropdown
        try {
            allPolicies = await api.getPolicies();
            // Add initial policy row
            addPolicyRow();
        } catch (error) {
            console.error('Error loading policies:', error);
        }
    });

    // Setup add policy button
    addPolicyButton.addEventListener('click', function () {
        addPolicyRow();
    });

    // Function to add a new policy row
    function addPolicyRow(policyId = '', description = '') {
        const container = document.getElementById('policies-container');
        const rowId = Date.now(); // Unique identifier for the row

        const row = document.createElement('div');
        row.className = 'policy-row mb-2 d-flex';
        row.innerHTML = `
            <select class="form-select policy-select me-2" style="flex: 2">
                <option value="">-- Select Policy --</option>
                ${allPolicies.map(policy => `
                    <option value="${policy.policy_id}" ${policy.policy_id === policyId ? 'selected' : ''}>
                        ${policy.name}
                    </option>
                `).join('')}
            </select>
            <input type="text" class="form-control policy-description me-2" placeholder="Description (optional)" 
                style="flex: 2" value="${description}">
            <button type="button" class="btn btn-outline-danger remove-policy-btn" data-row-id="${rowId}">
                <i class="bi bi-trash"></i>
            </button>
        `;

        container.appendChild(row);

        // Add event listener to remove button
        row.querySelector('.remove-policy-btn').addEventListener('click', function () {
            container.removeChild(row);
        });
    }

    // Setup save button
    saveButton.addEventListener('click', async function () {
        const apiPermissionId = document.getElementById('api-permission-id').value;
        const pathPattern = document.getElementById('path-pattern').value;
        const method = document.getElementById('http-method').value;
        const description = document.getElementById('api-description').value;
        const isEnabled = document.getElementById('is-enabled').checked;

        // Get policies from rows
        const policies = [];
        document.querySelectorAll('.policy-row').forEach(row => {
            const policyId = row.querySelector('.policy-select').value;
            const policyDescription = row.querySelector('.policy-description').value;

            if (policyId) {
                policies.push({
                    policy_id: policyId,
                    description: policyDescription
                });
            }
        });

        try {
            const apiPermissionData = {
                path_pattern: pathPattern,
                method: method,
                description: description,
                is_enabled: isEnabled,
                policies: policies
            };

            if (apiPermissionId) {
                // Update existing API permission
                await api.updateApiPermission(apiPermissionId, apiPermissionData);
            } else {
                // Create new API permission
                await api.createApiPermission(apiPermissionData);
            }

            // Close modal and reload API permissions
            bootstrap.Modal.getInstance(apiPermissionModalElement).hide();
            loadApiPermissions();
        } catch (error) {
            alert(`Error saving API permission: ${error.message}`);
        }
    });
}

// Edit API permission
async function editApiPermission(apiPermissionId) {
    try {
        const apiPermission = await api.getApiPermission(apiPermissionId);
        const policies = await api.getPolicies();

        // Store policies globally for the modal
        allPolicies = policies;

        const apiPermissionModalElement = document.getElementById('apiPermissionModal');
        const apiPermissionModal = bootstrap.Modal.getInstance(apiPermissionModalElement) ||
            new bootstrap.Modal(apiPermissionModalElement);

        document.getElementById('apiPermissionModalTitle').textContent = 'Edit API Permission';
        document.getElementById('api-permission-id').value = apiPermission.id;
        document.getElementById('path-pattern').value = apiPermission.path_pattern;
        document.getElementById('http-method').value = apiPermission.method;
        document.getElementById('api-description').value = apiPermission.description || '';
        document.getElementById('is-enabled').checked = apiPermission.is_enabled;

        // Clear and rebuild policies container
        const policiesContainer = document.getElementById('policies-container');
        policiesContainer.innerHTML = '';

        // Add policy rows
        if (apiPermission.policies && apiPermission.policies.length > 0) {
            apiPermission.policies.forEach(policy => {
                addPolicyRow(policy.policy_id, policy.description);
            });
        } else {
            addPolicyRow();
        }

        apiPermissionModal.show();
    } catch (error) {
        alert(`Error loading API permission: ${error.message}`);
    }
}

// Delete API permission
async function deleteApiPermission(apiPermissionId) {
    if (confirm('Are you sure you want to delete this API permission configuration?')) {
        try {
            await api.deleteApiPermission(apiPermissionId);
            loadApiPermissions();
        } catch (error) {
            alert(`Error deleting API permission: ${error.message}`);
        }
    }
} 