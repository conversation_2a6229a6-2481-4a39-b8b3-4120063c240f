/**
 * API client for policy management
 */
class ApiClient {
    constructor() {
        this.baseUrl = '/api/v1.0';
        this.adminApiUrl = '/api/v1.0/policy-admin-api';
        this.credentials = null;
    }

    /**
     * Set credentials for API access
     * @param {string} username - Username for Basic Auth
     * @param {string} password - Password for Basic Auth
     */
    setCredentials(username, password) {
        const base64Credentials = btoa(`${username}:${password}`);
        this.credentials = `Basic ${base64Credentials}`;
        localStorage.setItem('auth_credentials', this.credentials);
    }

    /**
     * Load credentials from localStorage
     * @returns {boolean} - True if credentials were loaded
     */
    loadCredentials() {
        const credentials = localStorage.getItem('auth_credentials');
        if (credentials) {
            this.credentials = credentials;
            return true;
        }
        return false;
    }

    /**
     * Clear stored credentials
     */
    clearCredentials() {
        this.credentials = null;
        localStorage.removeItem('auth_credentials');
    }

    /**
     * Get authorization header
     * @returns {Object} - Headers object
     */
    getHeaders() {
        return {
            'Authorization': this.credentials,
            'Content-Type': 'application/json'
        };
    }

    /**
     * Make API request
     * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
     * @param {string} path - API path
     * @param {Object} data - Request data for POST/PUT
     * @param {boolean} useAdminApi - Whether to use the admin API endpoint
     * @returns {Promise<Object>} - API response
     */
    async request(method, path, data = null, useAdminApi = false) {
        if (!this.credentials) {
            throw new Error('Authentication required');
        }

        const options = {
            method: method,
            headers: this.getHeaders()
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        const baseUrl = useAdminApi ? this.adminApiUrl : this.baseUrl;
        const response = await fetch(`${baseUrl}${path}`, options);

        if (response.status === 401) {
            this.clearCredentials();
            throw new Error('Authentication failed');
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || 'API request failed');
        }

        return result;
    }

    /**
     * Attempt login with credentials
     * @param {string} username - Username
     * @param {string} password - Password
     * @returns {Promise<boolean>} - True if login successful
     */
    async login(username, password) {
        try {
            // Only allow admin/admin login for policy admin interface
            if (username !== 'admin' || password !== 'admin') {
                return false;
            }

            this.setCredentials(username, password);
            // Try to access policies endpoint to verify credentials
            await this.getPolicies();
            return true;
        } catch (error) {
            this.clearCredentials();
            return false;
        }
    }

    // Policy Management API

    /**
     * Get all policies
     * @returns {Promise<Array>} - List of policies
     */
    async getPolicies() {
        const response = await this.request('GET', '/policies');
        return response.data || [];
    }

    /**
     * Get policy by ID
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Policy details
     */
    async getPolicy(policyId) {
        const response = await this.request('GET', `/policies/${policyId}`);
        return response.data;
    }

    /**
     * Create a new policy
     * @param {Object} policy - Policy data
     * @returns {Promise<Object>} - Created policy
     */
    async createPolicy(policy) {
        const response = await this.request('POST', '/policies', policy);
        return response.data;
    }

    /**
     * Update a policy
     * @param {string} policyId - Policy ID
     * @param {Object} policy - Updated policy data
     * @returns {Promise<Object>} - Updated policy
     */
    async updatePolicy(policyId, policy) {
        const response = await this.request('PUT', `/policies/${policyId}`, policy);
        return response.data;
    }

    /**
     * Delete a policy
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Response
     */
    async deletePolicy(policyId) {
        const response = await this.request('DELETE', `/policies/${policyId}`);
        return response.data;
    }

    /**
     * Create default policies
     * @returns {Promise<Object>} - Response
     */
    async createDefaultPolicies() {
        const response = await this.request('POST', '/policies/create-defaults');
        return response.data;
    }

    // API Permission Management

    /**
     * Get all API permission configurations
     * @returns {Promise<Array>} - List of API permissions
     */
    async getApiPermissions() {
        const response = await this.request('GET', '/api-permissions');
        return response.data || [];
    }

    /**
     * Get API permission by ID
     * @param {string} apiPermissionId - API permission ID
     * @returns {Promise<Object>} - API permission details
     */
    async getApiPermission(apiPermissionId) {
        const response = await this.request('GET', `/api-permissions/${apiPermissionId}`);
        return response.data;
    }

    /**
     * Create a new API permission
     * @param {Object} apiPermission - API permission data
     * @returns {Promise<Object>} - Created API permission
     */
    async createApiPermission(apiPermission) {
        const response = await this.request('POST', '/api-permissions', apiPermission);
        return response.data;
    }

    /**
     * Update an API permission
     * @param {string} apiPermissionId - API permission ID
     * @param {Object} apiPermission - Updated API permission data
     * @returns {Promise<Object>} - Updated API permission
     */
    async updateApiPermission(apiPermissionId, apiPermission) {
        const response = await this.request('PUT', `/api-permissions/${apiPermissionId}`, apiPermission);
        return response.data;
    }

    /**
     * Delete an API permission
     * @param {string} apiPermissionId - API permission ID
     * @returns {Promise<Object>} - Response
     */
    async deleteApiPermission(apiPermissionId) {
        const response = await this.request('DELETE', `/api-permissions/${apiPermissionId}`);
        return response.data;
    }

    // User Management API

    /**
     * Get all users
     * @returns {Promise<Array>} - List of users
     */
    async getUsers() {
        const response = await this.request('GET', '/users', null, true);
        return response.data || [];
    }

    /**
     * Get user details
     * @param {string} userId - User ID
     * @returns {Promise<Object>} - User details
     */
    async getUser(userId) {
        const response = await this.request('GET', `/users/${userId}`, null, true);
        return response.data;
    }

    /**
     * Get current logged-in user details
     * @returns {Promise<Object>} - Current user details
     */
    async getCurrentUser() {
        const response = await this.request('GET', '/users/current');
        return response.data;
    }

    /**
     * Attach policy to user
     * @param {string} userId - User ID
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Response
     */
    async attachPolicyToUser(userId, policyId) {
        const response = await this.request('POST', `/users/${userId}/policies`, { policy_id: policyId });
        return response.data;
    }

    /**
     * Detach policy from user
     * @param {string} userId - User ID
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Response
     */
    async detachPolicyFromUser(userId, policyId) {
        const response = await this.request('POST', `/users/${userId}/policies/detach`, { policy_id: policyId });
        return response.data;
    }

    /**
     * Attach policy to current logged-in user
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Response
     */
    async attachPolicyToCurrentUser(policyId) {
        const response = await this.request('POST', `/users/current/policies`, { policy_id: policyId });
        return response.data;
    }

    /**
     * Detach policy from current logged-in user
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Response
     */
    async detachPolicyFromCurrentUser(policyId) {
        const response = await this.request('POST', `/users/current/policies/detach`, { policy_id: policyId });
        return response.data;
    }

    // Role Management API

    /**
     * Get all roles
     * @returns {Promise<Array>} - List of roles
     */
    async getRoles() {
        const response = await this.request('GET', '/roles', null, true);
        return response.data || [];
    }

    /**
     * Get role details
     * @param {string} roleId - Role ID
     * @returns {Promise<Object>} - Role details
     */
    async getRole(roleId) {
        const response = await this.request('GET', `/roles/${roleId}`, null, true);
        return response.data;
    }

    /**
     * Attach policy to role
     * @param {string} roleId - Role ID
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Response
     */
    async attachPolicyToRole(roleId, policyId) {
        const response = await this.request('POST', `/roles/${roleId}/policies`, { policy_id: policyId });
        return response.data;
    }

    /**
     * Detach policy from role
     * @param {string} roleId - Role ID
     * @param {string} policyId - Policy ID
     * @returns {Promise<Object>} - Response
     */
    async detachPolicyFromRole(roleId, policyId) {
        const response = await this.request('POST', `/roles/${roleId}/policies/detach`, { policy_id: policyId });
        return response.data;
    }

    /**
     * Evaluate policy for a user
     * @param {string} userId - User ID
     * @param {string} action - Action to evaluate
     * @param {string} resource - Resource to evaluate
     * @param {Object} context - Additional context
     * @returns {Promise<Object>} - Evaluation result
     */
    async evaluatePolicy(userId, action, resource, context = {}) {
        const payload = { action, resource, context };
        const response = await this.request('POST', `/users/${userId}/evaluate-policy`, payload);
        return response.data;
    }

    /**
     * Evaluate policy for current logged-in user
     * @param {string} action - Action to evaluate
     * @param {string} resource - Resource to evaluate
     * @param {Object} context - Additional context
     * @returns {Promise<Object>} - Evaluation result
     */
    async evaluateCurrentUserPolicy(action, resource, context = {}) {
        const payload = { action, resource, context };
        const response = await this.request('POST', `/users/current/evaluate-policy`, payload);
        return response.data;
    }
}

// Create a global API client instance
const api = new ApiClient(); 