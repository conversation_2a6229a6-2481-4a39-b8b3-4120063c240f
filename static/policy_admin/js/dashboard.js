document.addEventListener('DOMContentLoaded', function () {
    // Check if user is logged in
    if (!api.loadCredentials()) {
        // Redirect to login page
        window.location.href = 'index.html';
        return;
    }

    // Initialize UI
    initNavigation();
    loadPolicies();
    setupPolicyModal();
    setupLogout();

    // Create default policy template for new policies
    window.defaultPolicyTemplate = {
        Version: "2023-01-01",
        Statement: [
            {
                Effect: "Allow",
                Action: ["service:Action"],
                Resource: ["arn:app:resource:*"]
            }
        ]
    };
});

// Initialize navigation
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link[data-page]');

    navLinks.forEach(link => {
        link.addEventListener('click', function (event) {
            event.preventDefault();

            // Get the page to show
            const pageId = this.getAttribute('data-page');

            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('d-none');
            });

            // Show the selected page
            document.getElementById(`${pageId}-page`).classList.remove('d-none');

            // Update active link
            navLinks.forEach(link => link.classList.remove('active'));
            this.classList.add('active');

            // Load page data
            if (pageId === 'policies') {
                loadPolicies();
            } else if (pageId === 'users') {
                loadUsers();
            } else if (pageId === 'roles') {
                loadRoles();
            } else if (pageId === 'api-permissions') {
                loadApiPermissions();
            }
        });
    });
}

// Setup logout button
function setupLogout() {
    document.getElementById('logout').addEventListener('click', function (event) {
        event.preventDefault();
        api.clearCredentials();
        window.location.href = 'index.html';
    });
}

// Load policies list
async function loadPolicies() {
    try {
        const policies = await api.getPolicies();
        const policiesList = document.getElementById('policies-list');

        policiesList.innerHTML = '';

        if (policies.length === 0) {
            policiesList.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center">No policies found. <a href="#" id="create-defaults">Create default policies</a></td>
                </tr>
            `;

            document.getElementById('create-defaults').addEventListener('click', async function (event) {
                event.preventDefault();
                try {
                    await api.createDefaultPolicies();
                    loadPolicies();
                } catch (error) {
                    alert(`Error creating default policies: ${error.message}`);
                }
            });
            return;
        }

        policies.forEach(policy => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${policy.name}</td>
                <td>${policy.description || '-'}</td>
                <td>${policy.policy_type}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary action-btn edit-policy" data-id="${policy.policy_id}">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger action-btn delete-policy" data-id="${policy.policy_id}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            policiesList.appendChild(row);
        });

        // Add event listeners for edit and delete buttons
        document.querySelectorAll('.edit-policy').forEach(button => {
            button.addEventListener('click', function () {
                const policyId = this.getAttribute('data-id');
                editPolicy(policyId);
            });
        });

        document.querySelectorAll('.delete-policy').forEach(button => {
            button.addEventListener('click', function () {
                const policyId = this.getAttribute('data-id');
                deletePolicy(policyId);
            });
        });
    } catch (error) {
        alert(`Error loading policies: ${error.message}`);
    }
}

// Load users list
async function loadUsers() {
    try {
        const users = await api.getUsers();
        const usersList = document.getElementById('users-list');

        usersList.innerHTML = '';

        if (users.length === 0) {
            usersList.innerHTML = `
                <tr>
                    <td colspan="3" class="text-center">No users found</td>
                </tr>
            `;
            return;
        }

        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.name || user.primary_email.split('@')[0]}</td>
                <td>${user.primary_email}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary action-btn manage-user-policies" data-id="${user.user_id}">
                        <i class="bi bi-key"></i> Manage Policies
                    </button>
                </td>
            `;
            usersList.appendChild(row);
        });

        // Add event listeners for manage policies buttons
        document.querySelectorAll('.manage-user-policies').forEach(button => {
            button.addEventListener('click', function () {
                const userId = this.getAttribute('data-id');
                openAssignPolicyModal('user', userId);
            });
        });
    } catch (error) {
        alert(`Error loading users: ${error.message}`);
    }
}

// Load roles list
async function loadRoles() {
    try {
        const roles = await api.getRoles();
        const rolesList = document.getElementById('roles-list');

        rolesList.innerHTML = '';

        if (roles.length === 0) {
            rolesList.innerHTML = `
                <tr>
                    <td colspan="3" class="text-center">No roles found</td>
                </tr>
            `;
            return;
        }

        roles.forEach(role => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${role.name}</td>
                <td>${role.description || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary action-btn manage-role-policies" data-id="${role.role_id}">
                        <i class="bi bi-key"></i> Manage Policies
                    </button>
                </td>
            `;
            rolesList.appendChild(row);
        });

        // Add event listeners for manage policies buttons
        document.querySelectorAll('.manage-role-policies').forEach(button => {
            button.addEventListener('click', function () {
                const roleId = this.getAttribute('data-id');
                openAssignPolicyModal('role', roleId);
            });
        });
    } catch (error) {
        alert(`Error loading roles: ${error.message}`);
    }
}

// Setup policy modal
function setupPolicyModal() {
    const policyModalElement = document.getElementById('policyModal');
    const policyModal = new bootstrap.Modal(policyModalElement);
    const saveButton = document.getElementById('save-policy-btn');
    const newPolicyButton = document.getElementById('new-policy-btn');

    // Setup new policy button
    newPolicyButton.addEventListener('click', function () {
        document.getElementById('policyModalTitle').textContent = 'Create New Policy';
        document.getElementById('policy-id').value = '';
        document.getElementById('policy-name').value = '';
        document.getElementById('policy-description').value = '';
        document.getElementById('policy-type').value = 'IDENTITY';
        document.getElementById('policy-document').value = JSON.stringify(window.defaultPolicyTemplate, null, 2);
    });

    // Setup save button
    saveButton.addEventListener('click', async function () {
        const policyId = document.getElementById('policy-id').value;
        const name = document.getElementById('policy-name').value;
        const description = document.getElementById('policy-description').value;
        const policyType = document.getElementById('policy-type').value;
        const documentText = document.getElementById('policy-document').value;

        try {
            // Validate policy document JSON
            const document = JSON.parse(documentText);

            const policyData = {
                name,
                description,
                policy_type: policyType,
                document
            };

            if (policyId) {
                // Update existing policy
                await api.updatePolicy(policyId, policyData);
            } else {
                // Create new policy
                await api.createPolicy(policyData);
            }

            // Close modal and reload policies
            bootstrap.Modal.getInstance(policyModalElement).hide();
            loadPolicies();
        } catch (error) {
            alert(`Error saving policy: ${error.message}`);
        }
    });
}

// Edit policy
async function editPolicy(policyId) {
    try {
        const policy = await api.getPolicy(policyId);
        const policyModalElement = document.getElementById('policyModal');
        const policyModal = bootstrap.Modal.getInstance(policyModalElement) || new bootstrap.Modal(policyModalElement);

        document.getElementById('policyModalTitle').textContent = 'Edit Policy';
        document.getElementById('policy-id').value = policy.policy_id;
        document.getElementById('policy-name').value = policy.name;
        document.getElementById('policy-description').value = policy.description || '';
        document.getElementById('policy-type').value = policy.policy_type;
        document.getElementById('policy-document').value = JSON.stringify(policy.document, null, 2);

        policyModal.show();
    } catch (error) {
        alert(`Error loading policy: ${error.message}`);
    }
}

// Delete policy
async function deletePolicy(policyId) {
    if (confirm('Are you sure you want to delete this policy?')) {
        try {
            await api.deletePolicy(policyId);
            loadPolicies();
        } catch (error) {
            alert(`Error deleting policy: ${error.message}`);
        }
    }
}

// Open assign policy modal
async function openAssignPolicyModal(type, id) {
    const assignModalElement = document.getElementById('assignPolicyModal');
    const assignModal = bootstrap.Modal.getInstance(assignModalElement) || new bootstrap.Modal(assignModalElement);
    const assignedPoliciesList = document.getElementById('assigned-policies');
    const availablePoliciesList = document.getElementById('available-policies');

    document.getElementById('assign-id').value = id;
    document.getElementById('assign-type').value = type;

    // Clear lists
    assignedPoliciesList.innerHTML = '';
    availablePoliciesList.innerHTML = '';

    try {
        // Get all policies
        const allPolicies = await api.getPolicies();

        // Get assigned policies
        let assignedPolicies = [];
        if (type === 'user') {
            const user = await api.getUser(id);
            assignedPolicies = user.policies || [];
        } else if (type === 'role') {
            const role = await api.getRole(id);
            assignedPolicies = role.policies || [];
        }

        // Create lists
        const assignedPolicyIds = assignedPolicies.map(p => p.policy_id);

        // Create assigned policies list
        if (assignedPolicies.length === 0) {
            assignedPoliciesList.innerHTML = '<li class="list-group-item text-center">No policies assigned</li>';
        } else {
            assignedPolicies.forEach(policy => {
                const item = document.createElement('li');
                item.className = 'list-group-item policy-item';
                item.innerHTML = `
                    <span>${policy.name}</span>
                    <button class="btn btn-sm btn-outline-danger detach-policy" data-id="${policy.policy_id}">
                        <i class="bi bi-x-lg"></i>
                    </button>
                `;
                assignedPoliciesList.appendChild(item);
            });

            // Add event listeners for detach buttons
            document.querySelectorAll('.detach-policy').forEach(button => {
                button.addEventListener('click', async function () {
                    const policyId = this.getAttribute('data-id');
                    try {
                        if (type === 'user') {
                            await api.detachPolicyFromUser(id, policyId);
                        } else if (type === 'role') {
                            await api.detachPolicyFromRole(id, policyId);
                        }

                        // Refresh the modal
                        openAssignPolicyModal(type, id);
                    } catch (error) {
                        alert(`Error detaching policy: ${error.message}`);
                    }
                });
            });
        }

        // Create available policies list
        const availablePolicies = allPolicies.filter(p => !assignedPolicyIds.includes(p.policy_id));

        if (availablePolicies.length === 0) {
            availablePoliciesList.innerHTML = '<li class="list-group-item text-center">No additional policies available</li>';
        } else {
            availablePolicies.forEach(policy => {
                const item = document.createElement('li');
                item.className = 'list-group-item policy-item';
                item.innerHTML = `
                    <span>${policy.name}</span>
                    <button class="btn btn-sm btn-outline-success attach-policy" data-id="${policy.policy_id}">
                        <i class="bi bi-plus-lg"></i>
                    </button>
                `;
                availablePoliciesList.appendChild(item);
            });

            // Add event listeners for attach buttons
            document.querySelectorAll('.attach-policy').forEach(button => {
                button.addEventListener('click', async function () {
                    const policyId = this.getAttribute('data-id');
                    try {
                        if (type === 'user') {
                            await api.attachPolicyToUser(id, policyId);
                        } else if (type === 'role') {
                            await api.attachPolicyToRole(id, policyId);
                        }

                        // Refresh the modal
                        openAssignPolicyModal(type, id);
                    } catch (error) {
                        alert(`Error attaching policy: ${error.message}`);
                    }
                });
            });
        }

        assignModal.show();
    } catch (error) {
        alert(`Error loading data: ${error.message}`);
    }
} 