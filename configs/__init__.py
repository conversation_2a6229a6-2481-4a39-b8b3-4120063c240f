from functools import lru_cache

from pydantic import BaseSettings

# Doc:: https://fastapi.tiangolo.com/advanced/settings/#__tabbed_3_1


class Settings(BaseSettings):
    SECRET_KEY: str
    DATABASE_DEBUG_MODE: bool
    LADDER_MONGO_URI: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_HOURS: int
    LADDER_POSTGRES_URI: str

    ACCESS_TOKEN_EXPIRE_SECONDS: int

    # Basic auth
    BASIC_AUTH_USERNAME: str
    BASIC_AUTH_PASSWORD: str

    FAVICON_URL: str

    REDIS_URI: str
    LADDER_HOME: str
    PUBLIC_HOST: str = ""

    class Config:
        env_file_encoding = "utf-8"
        env_file = ".env"


@lru_cache
def get_environment_variables():
    return Settings(_env_file_encoding="utf-8")


ApplicationConfig = get_environment_variables()
