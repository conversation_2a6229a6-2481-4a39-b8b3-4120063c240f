#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" 
    Author: tungdd
    Date created: 04/01/2024
"""
from typing import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, scoped_session, sessionmaker

from configs import ApplicationConfig

# Create Database Engine
Engine = create_engine(
    ApplicationConfig.LADDER_POSTGRES_URI,
    echo=ApplicationConfig.DATABASE_DEBUG_MODE,
    future=True,
    pool_size=10,
    max_overflow=2,
    pool_recycle=300,
    pool_pre_ping=True,
    pool_use_lifo=True,
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=Engine)


class DBSessionFactory:
    def __init__(self, auto_commit: bool = False, session=None):
        self.auto_commit = auto_commit
        self.session = session

    def __call__(self) -> Generator[Session, None, None]:
        db = self.session or SessionLocal()
        try:
            yield db
            if self.auto_commit:
                db.commit()
        except:
            db.rollback()
            raise
        finally:
            db.close()


get_db = DBSessionFactory()


def get_db_connection():
    db = scoped_session(SessionLocal)
    try:
        yield db
    finally:
        db.close()
