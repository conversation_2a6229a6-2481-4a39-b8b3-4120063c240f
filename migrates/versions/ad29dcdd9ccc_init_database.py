"""init_database

Revision ID: ad29dcdd9ccc
Revises: 
Create Date: 2024-09-01 19:06:48.202722

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ad29dcdd9ccc'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('company',
    sa.Column('company_id', sa.String(), nullable=False),
    sa.Column('tenant_key', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('avatar', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('company_id')
    )
    op.create_index(op.f('ix_company_company_id'), 'company', ['company_id'], unique=False)
    op.create_table('role_permission',
    sa.Column('role_permission_id', sa.String(), nullable=False),
    sa.Column('role_id', sa.String(), nullable=True),
    sa.Column('permission_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['permission_id'], ['permission.permission_id'], use_alter=True),
    sa.ForeignKeyConstraint(['role_id'], ['role.role_id'], use_alter=True),
    sa.PrimaryKeyConstraint('role_permission_id')
    )
    op.create_index(op.f('ix_role_permission_permission_id'), 'role_permission', ['permission_id'], unique=False)
    op.create_index(op.f('ix_role_permission_role_id'), 'role_permission', ['role_id'], unique=False)
    op.create_index(op.f('ix_role_permission_role_permission_id'), 'role_permission', ['role_permission_id'], unique=False)
    op.create_table('user_department',
    sa.Column('user_department_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.Column('department_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['department_id'], ['department.department_id'], use_alter=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], use_alter=True),
    sa.PrimaryKeyConstraint('user_department_id')
    )
    op.create_index(op.f('ix_user_department_department_id'), 'user_department', ['department_id'], unique=False)
    op.create_index(op.f('ix_user_department_user_department_id'), 'user_department', ['user_department_id'], unique=False)
    op.create_index(op.f('ix_user_department_user_id'), 'user_department', ['user_id'], unique=False)
    op.create_table('user_event',
    sa.Column('user_event_id', sa.Integer(), nullable=False),
    sa.Column('employee_code', sa.String(), nullable=True),
    sa.Column('body_event', sa.JSON(), nullable=True),
    sa.Column('event_type', sa.String(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_code'], ['user.employee_code'], use_alter=True),
    sa.PrimaryKeyConstraint('user_event_id')
    )
    op.create_table('user_history',
    sa.Column('history_id', sa.Integer(), nullable=False),
    sa.Column('employee_code', sa.String(), nullable=True),
    sa.Column('before_data', sa.JSON(), nullable=True),
    sa.Column('after_data', sa.JSON(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_code'], ['user.employee_code'], use_alter=True),
    sa.PrimaryKeyConstraint('history_id')
    )
    op.create_table('user_role',
    sa.Column('user_role_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.Column('role_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['role_id'], ['role.role_id'], use_alter=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], use_alter=True),
    sa.PrimaryKeyConstraint('user_role_id')
    )
    op.create_index(op.f('ix_user_role_role_id'), 'user_role', ['role_id'], unique=False)
    op.create_index(op.f('ix_user_role_user_id'), 'user_role', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_role_user_role_id'), 'user_role', ['user_role_id'], unique=False)
    op.create_table('user_sub_department',
    sa.Column('user_sub_department_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.Column('sub_department_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['sub_department_id'], ['sub_department.sub_department_id'], use_alter=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], use_alter=True),
    sa.PrimaryKeyConstraint('user_sub_department_id')
    )
    op.create_index(op.f('ix_user_sub_department_sub_department_id'), 'user_sub_department', ['sub_department_id'], unique=False)
    op.create_index(op.f('ix_user_sub_department_user_id'), 'user_sub_department', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_sub_department_user_sub_department_id'), 'user_sub_department', ['user_sub_department_id'], unique=False)
    op.create_table('branch',
    sa.Column('branch_id', sa.String(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('address', sa.String(), nullable=False),
    sa.Column('phone', sa.String(), nullable=False),
    sa.Column('owner_code', sa.String(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.PrimaryKeyConstraint('branch_id')
    )
    op.create_index(op.f('ix_branch_branch_id'), 'branch', ['branch_id'], unique=False)
    op.create_index(op.f('ix_branch_code'), 'branch', ['code'], unique=False)
    op.create_table('contact_info',
    sa.Column('contact_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('employee_code', sa.String(), nullable=True),
    sa.Column('full_name', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('address', sa.String(), nullable=True),
    sa.Column('relationship_type', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.ForeignKeyConstraint(['employee_code'], ['user.employee_code'], use_alter=True),
    sa.PrimaryKeyConstraint('contact_id')
    )
    op.create_index(op.f('ix_contact_info_employee_code'), 'contact_info', ['employee_code'], unique=False)
    op.create_table('contract',
    sa.Column('contract_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('contract_number', sa.String(), nullable=True),
    sa.Column('contract_file_scan_link', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.PrimaryKeyConstraint('contract_id')
    )
    op.create_index(op.f('ix_contract_contract_number'), 'contract', ['contract_number'], unique=True)
    op.create_table('department',
    sa.Column('department_id', sa.String(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('department_code', sa.String(), nullable=True),
    sa.Column('lark_department_id', sa.String(), nullable=True),
    sa.Column('open_department_id', sa.String(), nullable=True),
    sa.Column('display', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('lower_case_name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('owners', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.PrimaryKeyConstraint('department_id'),
    sa.UniqueConstraint('lower_case_name'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_department_department_id'), 'department', ['department_id'], unique=False)
    op.create_table('employment_type',
    sa.Column('employment_type_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('vi_name', sa.String(), nullable=True),
    sa.Column('en_name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.PrimaryKeyConstraint('employment_type_id'),
    sa.UniqueConstraint('employment_type_id')
    )
    op.create_table('job_title',
    sa.Column('job_title_id', sa.String(), nullable=False),
    sa.Column('department_id', sa.String(), nullable=True),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('lower_case_name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.ForeignKeyConstraint(['department_id'], ['department.department_id'], use_alter=True),
    sa.PrimaryKeyConstraint('job_title_id')
    )
    op.create_index(op.f('ix_job_title_department_id'), 'job_title', ['department_id'], unique=False)
    op.create_index(op.f('ix_job_title_job_title_id'), 'job_title', ['job_title_id'], unique=False)
    op.create_table('job_title_level',
    sa.Column('job_title_level_id', sa.String(), nullable=False),
    sa.Column('job_title_id', sa.String(), nullable=True),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.Column('lower_case_name', sa.String(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.ForeignKeyConstraint(['job_title_id'], ['job_title.job_title_id'], use_alter=True),
    sa.PrimaryKeyConstraint('job_title_level_id')
    )
    op.create_index(op.f('ix_job_title_level_job_title_id'), 'job_title_level', ['job_title_id'], unique=False)
    op.create_index(op.f('ix_job_title_level_job_title_level_id'), 'job_title_level', ['job_title_level_id'], unique=False)
    op.create_table('permission',
    sa.Column('permission_id', sa.String(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('action', sa.String(), nullable=False),
    sa.Column('scope', sa.String(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.PrimaryKeyConstraint('permission_id')
    )
    op.create_index(op.f('ix_permission_permission_id'), 'permission', ['permission_id'], unique=False)
    op.create_table('role',
    sa.Column('role_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('lower_case_name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.PrimaryKeyConstraint('role_id'),
    sa.UniqueConstraint('lower_case_name'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_role_role_id'), 'role', ['role_id'], unique=False)
    op.create_table('sub_department',
    sa.Column('sub_department_id', sa.String(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('department_id', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.ForeignKeyConstraint(['department_id'], ['department.department_id'], use_alter=True),
    sa.PrimaryKeyConstraint('sub_department_id', 'code'),
    sa.UniqueConstraint('code')
    )
    op.create_index(op.f('ix_sub_department_sub_department_id'), 'sub_department', ['sub_department_id'], unique=True)
    op.create_table('user',
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('employee_code', sa.String(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('lark_user_id', sa.String(), nullable=False),
    sa.Column('open_user_id', sa.String(), nullable=False),
    sa.Column('middle_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('unsigned_name', sa.String(), nullable=True),
    sa.Column('primary_email', sa.String(), nullable=False),
    sa.Column('personal_email', sa.String(), nullable=True),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('password', sa.String(), nullable=True),
    sa.Column('gender', sa.Integer(), nullable=False),
    sa.Column('marital_status', sa.Integer(), nullable=True),
    sa.Column('leader_user_id', sa.String(), nullable=True),
    sa.Column('education_level', sa.String(), nullable=True),
    sa.Column('employment_type_id', sa.Integer(), nullable=True),
    sa.Column('thumb_avatar_link', sa.String(), nullable=True),
    sa.Column('icon_avatar_link', sa.String(), nullable=True),
    sa.Column('home_town', sa.String(), nullable=True),
    sa.Column('date_of_birth', sa.DateTime(), nullable=True),
    sa.Column('current_address', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('last_time_login', sa.DateTime(), nullable=True),
    sa.Column('salary_amount', sa.Float(), nullable=True),
    sa.Column('start_salary', sa.DateTime(), nullable=True),
    sa.Column('start_onboard_at', sa.DateTime(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('contract_number', sa.String(), nullable=True),
    sa.Column('job_title_id', sa.String(), nullable=False),
    sa.Column('job_title_level_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.ForeignKeyConstraint(['contract_number'], ['contract.contract_number'], use_alter=True),
    sa.ForeignKeyConstraint(['employment_type_id'], ['employment_type.employment_type_id'], use_alter=True),
    sa.ForeignKeyConstraint(['job_title_id'], ['job_title.job_title_id'], use_alter=True),
    sa.ForeignKeyConstraint(['job_title_level_id'], ['job_title_level.job_title_level_id'], use_alter=True),
    sa.PrimaryKeyConstraint('user_id', 'employee_code'),
    sa.UniqueConstraint('contract_number'),
    sa.UniqueConstraint('primary_email'),
    sa.UniqueConstraint('username')
    )
    op.create_index(op.f('ix_user_employee_code'), 'user', ['employee_code'], unique=True)
    op.create_index(op.f('ix_user_job_title_id'), 'user', ['job_title_id'], unique=False)
    op.create_index(op.f('ix_user_job_title_level_id'), 'user', ['job_title_level_id'], unique=False)
    op.create_index(op.f('ix_user_user_id'), 'user', ['user_id'], unique=True)
    op.create_table('user_identification',
    sa.Column('user_identification_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('employee_code', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('issue_place', sa.String(), nullable=False),
    sa.Column('front_image', sa.String(), nullable=False),
    sa.Column('back_image', sa.String(), nullable=False),
    sa.Column('identification_number', sa.String(), nullable=True),
    sa.Column('issue_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.ForeignKeyConstraint(['employee_code'], ['user.employee_code'], use_alter=True),
    sa.PrimaryKeyConstraint('user_identification_id')
    )
    op.create_index(op.f('ix_user_identification_employee_code'), 'user_identification', ['employee_code'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_identification_employee_code'), table_name='user_identification')
    op.drop_table('user_identification')
    op.drop_index(op.f('ix_user_user_id'), table_name='user')
    op.drop_index(op.f('ix_user_job_title_level_id'), table_name='user')
    op.drop_index(op.f('ix_user_job_title_id'), table_name='user')
    op.drop_index(op.f('ix_user_employee_code'), table_name='user')
    op.drop_table('user')
    op.drop_index(op.f('ix_sub_department_sub_department_id'), table_name='sub_department')
    op.drop_table('sub_department')
    op.drop_index(op.f('ix_role_role_id'), table_name='role')
    op.drop_table('role')
    op.drop_index(op.f('ix_permission_permission_id'), table_name='permission')
    op.drop_table('permission')
    op.drop_index(op.f('ix_job_title_level_job_title_level_id'), table_name='job_title_level')
    op.drop_index(op.f('ix_job_title_level_job_title_id'), table_name='job_title_level')
    op.drop_table('job_title_level')
    op.drop_index(op.f('ix_job_title_job_title_id'), table_name='job_title')
    op.drop_index(op.f('ix_job_title_department_id'), table_name='job_title')
    op.drop_table('job_title')
    op.drop_table('employment_type')
    op.drop_index(op.f('ix_department_department_id'), table_name='department')
    op.drop_table('department')
    op.drop_index(op.f('ix_contract_contract_number'), table_name='contract')
    op.drop_table('contract')
    op.drop_index(op.f('ix_contact_info_employee_code'), table_name='contact_info')
    op.drop_table('contact_info')
    op.drop_index(op.f('ix_branch_code'), table_name='branch')
    op.drop_index(op.f('ix_branch_branch_id'), table_name='branch')
    op.drop_table('branch')
    op.drop_index(op.f('ix_user_sub_department_user_sub_department_id'), table_name='user_sub_department')
    op.drop_index(op.f('ix_user_sub_department_user_id'), table_name='user_sub_department')
    op.drop_index(op.f('ix_user_sub_department_sub_department_id'), table_name='user_sub_department')
    op.drop_table('user_sub_department')
    op.drop_index(op.f('ix_user_role_user_role_id'), table_name='user_role')
    op.drop_index(op.f('ix_user_role_user_id'), table_name='user_role')
    op.drop_index(op.f('ix_user_role_role_id'), table_name='user_role')
    op.drop_table('user_role')
    op.drop_table('user_history')
    op.drop_table('user_event')
    op.drop_index(op.f('ix_user_department_user_id'), table_name='user_department')
    op.drop_index(op.f('ix_user_department_user_department_id'), table_name='user_department')
    op.drop_index(op.f('ix_user_department_department_id'), table_name='user_department')
    op.drop_table('user_department')
    op.drop_index(op.f('ix_role_permission_role_permission_id'), table_name='role_permission')
    op.drop_index(op.f('ix_role_permission_role_id'), table_name='role_permission')
    op.drop_index(op.f('ix_role_permission_permission_id'), table_name='role_permission')
    op.drop_table('role_permission')
    op.drop_index(op.f('ix_company_company_id'), table_name='company')
    op.drop_table('company')
    # ### end Alembic commands ###
