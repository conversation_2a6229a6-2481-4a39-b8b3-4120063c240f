"""Add policy model

Revision ID: 36ab2043dbf0
Revises: ad29dcdd9ccc
Create Date: 2025-03-14 11:58:57.653463

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '36ab2043dbf0'
down_revision: Union[str, None] = 'ad29dcdd9ccc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('role_policies',
    sa.Column('role_policy_id', sa.String(), nullable=False),
    sa.Column('role_id', sa.String(), nullable=True),
    sa.Column('policy_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['policy_id'], ['policies.policy_id'], use_alter=True),
    sa.ForeignKeyConstraint(['role_id'], ['role.role_id'], use_alter=True),
    sa.PrimaryKeyConstraint('role_policy_id')
    )
    op.create_index(op.f('ix_role_policies_policy_id'), 'role_policies', ['policy_id'], unique=False)
    op.create_index(op.f('ix_role_policies_role_id'), 'role_policies', ['role_id'], unique=False)
    op.create_index(op.f('ix_role_policies_role_policy_id'), 'role_policies', ['role_policy_id'], unique=False)
    op.create_table('user_policies',
    sa.Column('user_policy_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.Column('policy_id', sa.String(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['policy_id'], ['policies.policy_id'], use_alter=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], use_alter=True),
    sa.PrimaryKeyConstraint('user_policy_id')
    )
    op.create_index(op.f('ix_user_policies_policy_id'), 'user_policies', ['policy_id'], unique=False)
    op.create_index(op.f('ix_user_policies_user_id'), 'user_policies', ['user_id'], unique=False)
    op.create_index(op.f('ix_user_policies_user_policy_id'), 'user_policies', ['user_policy_id'], unique=False)
    op.create_table('policies',
    sa.Column('policy_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('policy_type', sa.String(), nullable=True),
    sa.Column('company_id', sa.String(), nullable=True),
    sa.Column('document', sa.JSON(), nullable=False),
    sa.Column('created_by', sa.String(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(), nullable=True),
    sa.Column('updated_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['company.company_id'], ),
    sa.PrimaryKeyConstraint('policy_id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_policies_policy_id'), 'policies', ['policy_id'], unique=False)
    op.create_foreign_key(None, 'contact_info', 'user', ['employee_code'], ['employee_code'], use_alter=True)
    op.create_unique_constraint(None, 'employment_type', ['employment_type_id'])
    op.create_foreign_key(None, 'job_title', 'department', ['department_id'], ['department_id'], use_alter=True)
    op.create_foreign_key(None, 'job_title_level', 'job_title', ['job_title_id'], ['job_title_id'], use_alter=True)
    op.create_foreign_key(None, 'role_permission', 'permission', ['permission_id'], ['permission_id'], use_alter=True)
    op.create_foreign_key(None, 'role_permission', 'role', ['role_id'], ['role_id'], use_alter=True)
    op.create_foreign_key(None, 'sub_department', 'department', ['department_id'], ['department_id'], use_alter=True)
    op.create_foreign_key(None, 'user', 'employment_type', ['employment_type_id'], ['employment_type_id'], use_alter=True)
    op.create_foreign_key(None, 'user', 'job_title_level', ['job_title_level_id'], ['job_title_level_id'], use_alter=True)
    op.create_foreign_key(None, 'user', 'job_title', ['job_title_id'], ['job_title_id'], use_alter=True)
    op.create_foreign_key(None, 'user', 'contract', ['contract_number'], ['contract_number'], use_alter=True)
    op.create_foreign_key(None, 'user_department', 'user', ['user_id'], ['user_id'], use_alter=True)
    op.create_foreign_key(None, 'user_department', 'department', ['department_id'], ['department_id'], use_alter=True)
    op.create_foreign_key(None, 'user_event', 'user', ['employee_code'], ['employee_code'], use_alter=True)
    op.create_foreign_key(None, 'user_history', 'user', ['employee_code'], ['employee_code'], use_alter=True)
    op.create_foreign_key(None, 'user_identification', 'user', ['employee_code'], ['employee_code'], use_alter=True)
    op.create_foreign_key(None, 'user_role', 'role', ['role_id'], ['role_id'], use_alter=True)
    op.create_foreign_key(None, 'user_role', 'user', ['user_id'], ['user_id'], use_alter=True)
    op.create_foreign_key(None, 'user_sub_department', 'user', ['user_id'], ['user_id'], use_alter=True)
    op.create_foreign_key(None, 'user_sub_department', 'sub_department', ['sub_department_id'], ['sub_department_id'], use_alter=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_sub_department', type_='foreignkey')
    op.drop_constraint(None, 'user_sub_department', type_='foreignkey')
    op.drop_constraint(None, 'user_role', type_='foreignkey')
    op.drop_constraint(None, 'user_role', type_='foreignkey')
    op.drop_constraint(None, 'user_identification', type_='foreignkey')
    op.drop_constraint(None, 'user_history', type_='foreignkey')
    op.drop_constraint(None, 'user_event', type_='foreignkey')
    op.drop_constraint(None, 'user_department', type_='foreignkey')
    op.drop_constraint(None, 'user_department', type_='foreignkey')
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_constraint(None, 'sub_department', type_='foreignkey')
    op.drop_constraint(None, 'role_permission', type_='foreignkey')
    op.drop_constraint(None, 'role_permission', type_='foreignkey')
    op.drop_constraint(None, 'job_title_level', type_='foreignkey')
    op.drop_constraint(None, 'job_title', type_='foreignkey')
    op.drop_constraint(None, 'employment_type', type_='unique')
    op.drop_constraint(None, 'contact_info', type_='foreignkey')
    op.drop_index(op.f('ix_policies_policy_id'), table_name='policies')
    op.drop_table('policies')
    op.drop_index(op.f('ix_user_policies_user_policy_id'), table_name='user_policies')
    op.drop_index(op.f('ix_user_policies_user_id'), table_name='user_policies')
    op.drop_index(op.f('ix_user_policies_policy_id'), table_name='user_policies')
    op.drop_table('user_policies')
    op.drop_index(op.f('ix_role_policies_role_policy_id'), table_name='role_policies')
    op.drop_index(op.f('ix_role_policies_role_id'), table_name='role_policies')
    op.drop_index(op.f('ix_role_policies_policy_id'), table_name='role_policies')
    op.drop_table('role_policies')
    # ### end Alembic commands ###
