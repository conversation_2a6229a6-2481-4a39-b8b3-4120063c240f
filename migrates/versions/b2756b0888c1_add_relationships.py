"""Add Relationships

Revision ID: b2756b0888c1
Revises: 36ab2043dbf0
Create Date: 2025-03-14 13:32:39.400800

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b2756b0888c1'
down_revision: Union[str, None] = '36ab2043dbf0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, 'role_policies', 'role', ['role_id'], ['role_id'], use_alter=True)
    op.create_foreign_key(None, 'role_policies', 'policies', ['policy_id'], ['policy_id'], use_alter=True)
    op.create_foreign_key(None, 'user_policies', 'policies', ['policy_id'], ['policy_id'], use_alter=True)
    op.create_foreign_key(None, 'user_policies', 'user', ['user_id'], ['user_id'], use_alter=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_policies', type_='foreignkey')
    op.drop_constraint(None, 'user_policies', type_='foreignkey')
    op.drop_constraint(None, 'role_policies', type_='foreignkey')
    op.drop_constraint(None, 'role_policies', type_='foreignkey')
    # ### end Alembic commands ###
