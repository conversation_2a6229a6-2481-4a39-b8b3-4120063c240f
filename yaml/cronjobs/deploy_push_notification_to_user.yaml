apiVersion: batch/v1
kind: CronJob
metadata:
  name: ladder-cj-push-notification-to-user
  labels:
    app: ladder-cj-push-notification-to-user
spec:
  schedule: "1 2 * * *" # 09:01 GMT+7
  # schedule: 0 * * * *
  startingDeadlineSeconds: 200 # Thời hạn bắt đầu <Default: không được thiết lập>
  concurrencyPolicy: Allow # Chính sách đồng thời: Allow, Forbid, Replace <Default: Allow>
  suspend: false # Tạm dừng CronJob <Default: false>
  successfulJobsHistoryLimit: 3 # Giới hạn lịch sử Job thành công <Default: 3>
  failedJobsHistoryLimit: 1 # Giớ<PERSON> hạn lịch sử Job thất bại <Default: 1>
  jobTemplate:
    spec:
      parallelism: 1 # Giá trị mặc định là 1l
      completions: 1 # Giá trị mặc định là 1
      activeDeadlineSeconds: 3600 # <Default: khô<PERSON> đư<PERSON> thiết lập>
      backoffLimit: 6 # <PERSON><PERSON><PERSON> trị mặc định là 6
      ttlSecondsAfterFinished: 3600 #<Default: không được thiết lập>
      template:
        spec:
          serviceAccountName: mobio
          containers:
            - name: ladder
              image: {image}
              imagePullPolicy: IfNotPresent
              command: ["/bin/sh", "-c"]
              args: ["cd $LADDER_HOME; PYTHONPATH=./ python3.11 -u start_cronjobs.py push-notification-to-user"]
              resources:
                requests:
                  memory: 30Mi
                  cpu: 40m
                limits:
                  memory: 1Gi
                  cpu: 500m
              envFrom:
                # - configMapRef:
                #     name: mobio-config
                - secretRef:
                    name: mobio-secret
              volumeMounts:
                - name: mobio-shared-data
                  mountPath: /media/data/resources/
                # - name: mobio-public-shared-data
                #   mountPath: /media/data/public_resources/
          initContainers:
            - name: init-ladder
              image: {image}
              command: ["/bin/sh", "-c", "cd $LADDER_HOME; sh check_image.sh"]
              envFrom:
                # - configMapRef:
                #     name: mobio-config
                - secretRef:
                    name: mobio-secret
              volumeMounts:
                - name: mobio-shared-data
                  mountPath: /media/data/resources/
                # - name: mobio-public-shared-data
                #   mountPath: /media/data/public_resources/
          imagePullSecrets:
            - name: registrypullsecret
          volumes:
            - name: mobio-shared-data
              persistentVolumeClaim:
                claimName: ladder-pvc

          restartPolicy: OnFailure
