apiVersion: apps/v1
kind: Deployment
metadata:
  name: ladder-api
  labels:
    app: ladder-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ladder-api
  template:
    metadata:
      labels:
        app: ladder-api
    spec:
      serviceAccountName: mobio
      nodeSelector:
        workertype: api
      tolerations:
        - key: workertype
          operator: Equal
          value: api
      containers:
        - name: ladder
          image: {image}
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c"]
          args:
            ["cd $LADDER_HOME;python3.11 -m uvicorn app_web:app --host 0.0.0.0 --port 80 --workers 2 --loop asyncio"]
          envFrom:
            # - configMapRef:
            #     name: mobio-config
            - secretRef:
                name: mobio-secret
          ports:
            - containerPort: 80
          resources:
            requests:
              memory: 300Mi
              cpu: 40m
            limits:
              memory: 1Gi
              cpu: 800m
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            # - name: mobio-public-shared-data
            #   mountPath: /media/data/public_resources/
          startupProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              port: 80
              path: /api/v1.0/ping
            initialDelaySeconds: 60
            periodSeconds: 30
          livenessProbe:
            httpGet:
              port: 80
              path: /api/v1.0/ping
            initialDelaySeconds: 120
            periodSeconds: 5
            timeoutSeconds: 4
            successThreshold: 1
            failureThreshold: 3
      initContainers:
        - name: init-ladder
          image: {image}
          command: ["/bin/sh", "-c", "cd $LADDER_HOME;sh check_image.sh"]
          envFrom:
            # - configMapRef:
            #     name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            # - name: mobio-public-shared-data
            #   mountPath: /media/data/public_resources/
      imagePullSecrets:
        - name: registrypullsecret
      volumes:
        - name: mobio-shared-data
          persistentVolumeClaim:
            claimName: ladder-pvc
        # - name: mobio-public-shared-data
        #   persistentVolumeClaim:
        #     claimName: mobio-public-resources-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: ladder-app-api-service
  labels:
    app: ladder-app-api
spec:
  ports:
    - name: ladder-api
      nodePort: 31974
      port: 80
      protocol: TCP
      targetPort: 80
  type: NodePort
  selector:
    app: ladder-api
