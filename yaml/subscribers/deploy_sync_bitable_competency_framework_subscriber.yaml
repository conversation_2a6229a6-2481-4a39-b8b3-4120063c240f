apiVersion: apps/v1
kind: Deployment
metadata:
  name: ladder-subscriber-sync-bitable-competency-framework
  labels:
    app: ladder-subscriber-sync-bitable-competency-framework
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ladder-subscriber-sync-bitable-competency-framework
  template:
    metadata:
      labels:
        app: ladder-subscriber-sync-bitable-competency-framework
    spec:
      serviceAccountName: mobio
      containers:
        - name: ladder
          image: {image}
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c"]
          args: ["cd $LADDER_HOME; PYTHONPATH=./ python3.11 -u start_subscribers/start_sync_bitable_competency_framework_subscriber.py"]
          resources:
            requests:
              memory: 100Mi
              cpu: 100m
            limits:
              memory: 200Mi
              cpu: 200m
          envFrom:
            # - configMapRef:
            #     name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            # - name: mobio-public-shared-data
            #   mountPath: /media/data/public_resources/
          # livenessProbe:
          #   exec:
          #     command:
          #       - /bin/sh
          #       - -c
          #       - "cat /media/data/resources/kafka-liveness-pod/$HOSTNAME"
          #   timeoutSeconds: 30
          #   periodSeconds: 30
          #   initialDelaySeconds: 300
          #   failureThreshold: 1
      initContainers:
        - name: init-ladder
          image: {image}
          command: ["/bin/sh", "-c", "cd $LADDER_HOME; sh check_image.sh"]
          envFrom:
            # - configMapRef:
            #     name: mobio-config
            - secretRef:
                name: mobio-secret
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
            # - name: mobio-public-shared-data
            #   mountPath: /media/data/public_resources/
      imagePullSecrets:
        - name: registrypullsecret
      volumes:
        - name: mobio-shared-data
          persistentVolumeClaim:
            claimName: ladder-pvc
        # - name: mobio-public-shared-data
        #   persistentVolumeClaim:
        #     claimName: mobio-public-resources-pvc
