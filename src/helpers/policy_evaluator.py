import re
from typing import Any, Dict, List, Optional, Union

from sqlalchemy.orm import Session

from src.models.postgres.base_model import PolicyModel
from src.repositories.policy_repository import PolicyRepository
from src.repositories.role_repository import RoleRepository
from src.repositories.users_repository import UserRepository


class PolicyEvaluator:
    """
    Policy evaluator for AWS-style IAM policy checking.
    Supports:
    - Identity-based policies
    - Explicit deny overrides
    - Resource patterns with wildcards (*)
    - Action patterns with wildcards (*)
    - Context-based conditions
    """

    def __init__(self, session: Session):
        self.session = session
        self.user_repo = UserRepository(session)
        self.policy_repo = PolicyRepository(session)
        self.role_repo = RoleRepository(session)

    async def is_allowed(self, user_id: str, action: str, resource: str, context: Optional[Dict] = None) -> bool:
        """
        Check if a user is allowed to perform an action on a resource.

        Args:
            user_id: The user's ID
            action: The action to check (e.g., "iam:CreateUser")
            resource: The resource to check (e.g., "arn:app:iam::user/john")
            context: Additional context for condition evaluation

        Returns:
            bool: True if the user is allowed, False otherwise
        """
        if not context:
            context = {}

        # Get all policies that apply to the user (directly or via roles)
        user_policies = await self._get_user_policies(user_id)

        # No policies found means no permissions
        if not user_policies:
            return False

        # Check each policy
        any_allow = False

        for policy in user_policies:
            policy_doc = policy.document

            # Check statements in the policy
            for statement in policy_doc.get("Statement", []):
                # Check if this statement applies to the action and resource
                if not self._statement_applies(statement, action, resource):
                    continue

                # Check conditions if specified
                if "Condition" in statement and not self._check_conditions(statement["Condition"], context):
                    continue

                # Check the effect
                effect = statement.get("Effect", "Deny")
                if effect == "Deny":
                    # Explicit deny overrides any allow
                    return False
                elif effect == "Allow":
                    any_allow = True

        # If we've reached here, there's no explicit deny
        # Return True if any policy explicitly allowed the action
        return any_allow

    async def has_policy_permission(self, user_id: str, policy_id: str, context: Optional[Dict] = None) -> bool:
        """
        Check if a user has a specific policy attached.

        Args:
            user_id: The user's ID
            policy_id: The policy ID to check for
            context: Additional context for condition evaluation

        Returns:
            bool: True if the user has the policy or a policy with equivalent permissions, False otherwise
        """
        if not context:
            context = {}

        # Get all policies that apply to the user (directly or via roles)
        user_policies = await self._get_user_policies(user_id)

        # Get the target policy
        policy_repo = PolicyRepository(self.session)
        target_policy = await policy_repo.get_policy_by_id(policy_id)

        if not target_policy:
            # Target policy doesn't exist
            return False

        # Check if the target policy is directly attached
        for policy in user_policies:
            if policy.policy_id == policy_id:
                return True

        # If not directly attached, check if any of the user's policies provide
        # equivalent or greater permissions than the target policy
        # This is a complex check and might be resource-intensive for many policies

        # For now, we'll implement a simple check:
        # If the target policy has Allow statements, check if all actions and resources
        # in those statements are covered by the user's policies

        # Extract all Allow statements from the target policy
        target_allows = []
        for statement in target_policy.document.get("Statement", []):
            if statement.get("Effect", "Deny") == "Allow":
                target_allows.append(statement)

        # If there are no Allow statements, the policy doesn't grant any permissions
        if not target_allows:
            return False

        # Check if all Allow statements are covered
        for statement in target_allows:
            # Get the actions and resources from the statement
            actions = self._normalize_array(statement.get("Action", []))
            resources = self._normalize_array(statement.get("Resource", []))

            # Check each action and resource combination
            for action in actions:
                for resource in resources:
                    # Check if this action and resource is allowed by any of the user's policies
                    allowed = await self.is_allowed(user_id, action, resource, context)
                    if not allowed:
                        # Found an action/resource that's not allowed
                        return False

        # All Allow statements are covered by the user's policies
        return True

    async def _get_user_policies(self, user_id: str) -> List[PolicyModel]:
        """
        Get all policies that apply to a user, both directly attached
        and via roles.

        Args:
            user_id: The user's ID

        Returns:
            List[PolicyModel]: List of policy models
        """
        policy_repo = PolicyRepository(self.session)
        return await policy_repo.get_user_effective_policies(user_id)

    def _statement_applies(self, statement: Dict, action: str, resource: str) -> bool:
        """
        Check if a policy statement applies to the specified action and resource.

        Args:
            statement: The policy statement
            action: The action to check
            resource: The resource to check

        Returns:
            bool: True if the statement applies, False otherwise
        """
        # Check Action
        if "Action" in statement:
            actions = self._normalize_array(statement["Action"])
            if not self._matches_pattern_list(action, actions):
                return False

        # Check NotAction (negated match)
        if "NotAction" in statement:
            not_actions = self._normalize_array(statement["NotAction"])
            if self._matches_pattern_list(action, not_actions):
                return False

        # Check Resource
        if "Resource" in statement:
            resources = self._normalize_array(statement["Resource"])
            if not self._matches_pattern_list(resource, resources):
                return False

        # Check NotResource (negated match)
        if "NotResource" in statement:
            not_resources = self._normalize_array(statement["NotResource"])
            if self._matches_pattern_list(resource, not_resources):
                return False

        return True

    def _check_conditions(self, conditions: Dict, context: Dict) -> bool:
        """
        Check if conditions in a policy statement are satisfied by the context.

        Args:
            conditions: The conditions from the policy statement
            context: The context of the request

        Returns:
            bool: True if all conditions are satisfied, False otherwise
        """
        # Implement condition operators like StringEquals, NumericLessThan, etc.
        # For example:
        # {
        #   "StringEquals": {
        #     "aws:username": "johndoe"
        #   }
        # }

        for operator, condition_values in conditions.items():
            # Handle different condition operators
            if operator == "StringEquals":
                for key, value in condition_values.items():
                    if key not in context or context[key] != value:
                        return False
            elif operator == "StringLike":
                for key, pattern in condition_values.items():
                    if key not in context or not self._matches_pattern(context[key], pattern):
                        return False
            elif operator == "NumericEquals":
                for key, value in condition_values.items():
                    if key not in context or not isinstance(context[key], (int, float)) or context[key] != value:
                        return False
            elif operator == "NumericLessThan":
                for key, value in condition_values.items():
                    if key not in context or not isinstance(context[key], (int, float)) or context[key] >= value:
                        return False
            elif operator == "NumericGreaterThan":
                for key, value in condition_values.items():
                    if key not in context or not isinstance(context[key], (int, float)) or context[key] <= value:
                        return False
            elif operator == "Bool":
                for key, value in condition_values.items():
                    if key not in context or not isinstance(context[key], bool) or context[key] != value:
                        return False
            else:
                # Unsupported operator, fail closed
                return False

        return True

    def _normalize_array(self, value: Union[str, List[str]]) -> List[str]:
        """
        Normalize a value to always be a list.

        Args:
            value: A string or list of strings

        Returns:
            List[str]: The normalized list
        """
        if isinstance(value, str):
            return [value]
        return value

    def _matches_pattern(self, value: str, pattern: str) -> bool:
        """
        Check if a value matches a pattern with wildcards.

        Args:
            value: The value to check
            pattern: The pattern to match against (can contain *)

        Returns:
            bool: True if the value matches the pattern
        """
        # Convert pattern to regex
        import re

        pattern_parts = pattern.split("*")
        regex_pattern = ".*".join(map(re.escape, pattern_parts))
        regex_pattern = f"^{regex_pattern}$"

        return bool(re.match(regex_pattern, value))

    def _matches_pattern_list(self, value: str, patterns: List[str]) -> bool:
        """
        Check if a value matches any pattern in a list.

        Args:
            value: The value to check
            patterns: The list of patterns to match against

        Returns:
            bool: True if the value matches any pattern
        """
        for pattern in patterns:
            if self._matches_pattern(value, pattern):
                return True
        return False

    async def _get_applicable_policies(self, user) -> List[Dict]:
        """Get all policies applicable to the user"""
        # 1. Directly attached user policies
        user_policies = await self.policy_repo.get_user_policies(user.user_id)

        # 2. Policies from user's roles
        role_policies = []
        user_roles = await self.role_repo.get_user_roles(user.user_id)
        for role in user_roles:
            role_policies.extend(await self.policy_repo.get_role_policies(role.role_id))

        # 3. Resource-based policies (if applicable)
        # This would require additional logic based on your resource model

        # Combine all policies
        all_policies = user_policies + role_policies

        return all_policies

    def _evaluate_policy(self, policy: Dict, action: str, resource: str, user: Any, context: Dict[str, Any]) -> str:
        """
        Evaluate a single policy

        Returns:
            str: "ALLOW", "DENY", or "NOT_APPLICABLE"
        """
        policy_doc = policy.get("document", {})
        if not policy_doc:
            return "NOT_APPLICABLE"

        for statement in policy_doc.get("Statement", []):
            # Check if statement applies to this action
            if not self._action_matches(statement.get("Action", []), action):
                continue

            # Check if statement applies to this resource
            if not self._resource_matches(statement.get("Resource", []), resource, user, context):
                continue

            # Check conditions (if any)
            if "Condition" in statement and not self._conditions_satisfied(statement.get("Condition", {}), context):
                continue

            # If we got here, the statement applies
            if statement.get("Effect") == "Allow":
                return "ALLOW"
            elif statement.get("Effect") == "Deny":
                return "DENY"

        return "NOT_APPLICABLE"

    def _action_matches(self, policy_actions, request_action) -> bool:
        """Check if the policy actions match the requested action"""
        if isinstance(policy_actions, str):
            policy_actions = [policy_actions]

        for policy_action in policy_actions:
            # Handle wildcards
            if policy_action == "*":
                return True

            # Handle service-specific wildcards
            if policy_action.endswith(":*"):
                service = policy_action.split(":")[0]
                if request_action.startswith(f"{service}:"):
                    return True

            # Exact match
            if policy_action == request_action:
                return True

        return False

    def _resource_matches(self, policy_resources, request_resource, user, context) -> bool:
        """Check if the policy resources match the requested resource"""
        if isinstance(policy_resources, str):
            policy_resources = [policy_resources]

        for policy_resource in policy_resources:
            # Handle wildcards
            if policy_resource == "*":
                return True

            # Handle ARN wildcards
            if "*" in policy_resource:
                pattern = policy_resource.replace("*", ".*")

                # Handle variable substitution
                pattern = self._substitute_variables(pattern, user, context)

                if re.match(pattern, request_resource):
                    return True

            # Exact match (after variable substitution)
            substituted_resource = self._substitute_variables(policy_resource, user, context)
            if substituted_resource == request_resource:
                return True

        return False

    def _substitute_variables(self, pattern: str, user, context) -> str:
        """Substitute variables in the pattern with values from user and context"""
        # Replace ${aws:username} with user's username/email
        if "${aws:username}" in pattern:
            pattern = pattern.replace("${aws:username}", user.primary_email)

        # Replace ${aws:userid} with user's ID
        if "${aws:userid}" in pattern:
            pattern = pattern.replace("${aws:userid}", user.user_id)

        # Add more variable substitutions as needed

        return pattern

    def _conditions_satisfied(self, conditions: Dict, context: Dict) -> bool:
        """Evaluate if all conditions in the policy are satisfied by the context"""
        for condition_operator, condition_values in conditions.items():
            # Implement condition operators (StringEquals, NumericLessThan, etc.)
            if condition_operator == "StringEquals":
                for key, value in condition_values.items():
                    if context.get(key) != value:
                        return False

            elif condition_operator == "StringLike":
                for key, value in condition_values.items():
                    if not self._string_like(context.get(key, ""), value):
                        return False

            # Add more condition operators as needed

        return True

    def _string_like(self, value: str, pattern: str) -> bool:
        """Implement AWS StringLike condition operator"""
        regex_pattern = pattern.replace("*", ".*")
        return bool(re.match(f"^{regex_pattern}$", value))
