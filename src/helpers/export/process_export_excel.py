#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 26/05/2025
"""

import os

from openpyxl.cell.cell import MergedCell
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.utils.cell import get_column_letter

from src.common.choices import UserEvaluateTypeChoice
from src.common.common import EvaluateKey


# Tạo một hàm helper để định dạng số với dấu ' ở đầu
def format_number(value):
    if value is None:
        return ""
    # Thêm dấu ' ở đầu các giá trị số
    return str(value)


def mapping_text_pask_to_text_vi(pask_code):
    return {
        "personality": "P (Personality - Tố chất con người)",
        "attitude": "A (Attitude - Thái độ)",
        "skill": "S (Skill - <PERSON><PERSON> năng)",
        "knowledge": "K (Knowledge - <PERSON><PERSON><PERSON> thức)",
    }.get(pask_code, "")


# Tạ<PERSON> một hàm helper để tính độ rộng cần thiết cho một chuỗi
def get_column_width(text):
    if text is None:
        return 0
    text = str(text)

    # Tính toán độ rộng dựa trên loại ký tự
    # Ký tự rộng (tiếng Việt, chữ số, ký tự đặc biệt) cần nhiều không gian hơn
    width = 0
    for char in text:
        if ord(char) > 127 or char in "@%$&(){}[]/\|~^":
            # Ký tự rộng - tiếng Việt, ký tự đặc biệt
            width += 1.3
        elif char.isupper() or char in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            # Chữ in hoa chiếm nhiều không gian hơn
            width += 1.1
        elif char.isdigit():
            # Chữ số
            width += 0.9
        else:
            # Các ký tự khác
            width += 0.8

    # Thêm khoảng trống cho padding và điều chỉnh theo font chữ
    return width


async def gen_data_export_evaluate_to_excel(
    company_id,
    user,
    user_repo,
    job_title_level_repo,
    department_repo,
    evaluate,
    competency_groups,
    task_performances,
    file_path,
):
    import openpyxl

    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    leader_evaluate_job_title_level_id = evaluate.get(EvaluateKey.LEADER_EVALUATE_JOB_TITLE_LEVEL_ID)
    user_evaluate_job_title_level_id = evaluate.get(EvaluateKey.USER_EVALUATE_JOB_TITLE_LEVEL_ID)

    data_exports = []
    index_row = 1

    total_point_min = 0
    total_user_point = 0
    total_leader_point = 0
    total_point_min_of_level = 0
    total_point_achieved_by_user = 0
    total_point_achieved_by_leader = 0
    total_gap = 0
    mapping_job_title_point = {}
    for competency_group in competency_groups:
        competency_group_name = competency_group.get(EvaluateKey.NAME)
        lst_competency = competency_group.get(EvaluateKey.LST_COMPETENCY, [])

        for competency in lst_competency:
            data = {}
            points = competency.get(EvaluateKey.POINTS, [])
            competency_name = competency.get(EvaluateKey.NAME)
            weight = competency.get(EvaluateKey.WEIGHT, 0)
            point_min = competency.get(EvaluateKey.POINT_MIN, 0)
            # Thêm nhóm PASK (giả định là trống nếu không có)
            pask_code = competency.get(EvaluateKey.PASK_CODE, "")

            for point in points:
                user_type = point.get(EvaluateKey.USER_TYPE)
                point = point.get(EvaluateKey.POINT, None)

                if user_type == UserEvaluateTypeChoice.OWNER.value:
                    data.update(
                        {
                            EvaluateKey.USER_POINT: point,
                            EvaluateKey.POINT_ACHIEVED_BY_USER: point * weight if point else None,
                        }
                    )
                elif user_type == UserEvaluateTypeChoice.LEADER.value:
                    data.update(
                        {
                            EvaluateKey.LEADER_POINT: point,
                            EvaluateKey.POINT_ACHIEVED_BY_LEADER: point * weight if point else None,
                        }
                    )
            for job_title_level in competency.get("job_title_levels", []):
                job_title_level_id = job_title_level.get("job_title_level_id")
                behavior_expression_level = job_title_level.get("behavior_expression_level")
                if behavior_expression_level == -1:
                    continue
                if mapping_job_title_point.get(job_title_level_id):
                    mapping_job_title_point[job_title_level_id] += behavior_expression_level
                else:
                    mapping_job_title_point[job_title_level_id] = behavior_expression_level

            point_achieved_by_user = data.get(EvaluateKey.POINT_ACHIEVED_BY_USER, 0)
            user_point = data.get(EvaluateKey.USER_POINT, 0)
            leader_point = data.get(EvaluateKey.LEADER_POINT, 0)
            point_achieved_by_leader = data.get(EvaluateKey.POINT_ACHIEVED_BY_LEADER, 0)
            point_min_of_level = weight * point_min
            gap = point_achieved_by_leader - point_min_of_level if point_achieved_by_leader else 0

            total_point_min += point_min
            total_user_point += user_point
            total_leader_point += leader_point if leader_point else 0
            total_point_min_of_level += point_min_of_level if point_min_of_level else 0
            total_point_achieved_by_user += point_achieved_by_user if point_achieved_by_user else 0
            total_point_achieved_by_leader += point_achieved_by_leader if point_achieved_by_leader else 0
            total_gap += gap

            # Thêm nhóm PASK vào data_insert
            data_insert = (
                index_row,
                competency_group_name,
                competency_name,
                mapping_text_pask_to_text_vi(pask_code),  # Thêm nhóm PASK
                weight,
                point_min,
                user_point,
                leader_point,
                point_min_of_level,
                point_achieved_by_user,
                point_achieved_by_leader,
                gap,
            )
            data_exports.append(data_insert)
            index_row += 1

    job_title_level_min_point_id = None
    mapping_job_title_point = dict(sorted(mapping_job_title_point.items(), key=lambda item: item[1], reverse=True))
    for job_title_level_id, point_value in mapping_job_title_point.items():
        if point_value <= total_point_min:
            job_title_level_min_point_id = job_title_level_id

    # Create thông tin user
    time_eval_of_users = evaluate.get(EvaluateKey.TIME_EVAL_OF_USERS)

    user_submit_time = None

    leader_title = ""
    job_title_level_label = ""

    lst_job_title_level = await job_title_level_repo.get_job_title_level_by_ids(
        [
            evaluate.get(EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID),
            leader_evaluate_job_title_level_id,
            user_evaluate_job_title_level_id,
            job_title_level_min_point_id,
        ]
    )

    mapping_job_title_level = {}
    for job_title_level in lst_job_title_level:
        if job_title_level.job_title_level_id == evaluate.get(EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID):
            mapping_job_title_level["before_job_title_level"] = job_title_level.name
        if job_title_level.job_title_level_id == leader_evaluate_job_title_level_id:
            mapping_job_title_level["leader_job_title_level"] = job_title_level.name
        if job_title_level.job_title_level_id == user_evaluate_job_title_level_id:
            mapping_job_title_level["user_job_title_level"] = job_title_level.name
        if job_title_level.job_title_level_id == job_title_level_min_point_id:
            mapping_job_title_level["job_title_level_min_point"] = job_title_level.name
    job_title_level_label = mapping_job_title_level.get("leader_job_title_level")
    leader_job_title_level_label = mapping_job_title_level.get("leader_job_title_level")
    user_job_title_level_label = mapping_job_title_level.get("leader_job_title_level")
    job_title_level_min_point_label = mapping_job_title_level.get("job_title_level_min_point")
    department_label = ""
    department_data = await department_repo.get_department_by_id(evaluate.get(EvaluateKey.DEPARTMENT_ID))
    if department_data:
        department_label = department_data.name
    for time_eval_of_user in time_eval_of_users:
        user_id = time_eval_of_user.get(EvaluateKey.USER_ID)
        user_type = time_eval_of_user.get(EvaluateKey.USER_TYPE)
        if user_type == 1:
            user_submit_time = time_eval_of_user.get(EvaluateKey.SUBMIT_TIME)

        if user_type == 2:
            detail_user = await user_repo.get_detail_user_by_id(user_id)
            job_title_name = detail_user.job_title_name
            leader_title = "{}-{}".format(detail_user.name, job_title_name)

    # Create a new workbook and select the active worksheet
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.page_setup.fitToWidth = 1
    # Set the title for the sheet
    ws.title = "Sheet1"  # Đúng tên sheet như file mẫu

    # Define styles dựa theo màu sắc trong file mẫu
    table_header_fill_light = PatternFill(
        start_color="FFDEE0E3", end_color="FFDEE0E3", fill_type="solid"
    )  # Màu xám nhạt
    table_header_fill_dark = PatternFill(start_color="FF8F959E", end_color="FF8F959E", fill_type="solid")  # Màu xám đậm
    white_fill = PatternFill(start_color="FFFFFFFF", end_color="FFFFFFFF", fill_type="solid")  # Màu trắng
    bold_font = Font(bold=True, size=16)
    normal_font = Font(bold=False, size=16)

    # Thiết lập độ rộng cột tối thiểu ban đầu và hệ số padding
    min_widths = {
        "A": 6,  # STT
        "B": 18,  # Nhóm năng lực
        "C": 25,  # Tên năng lực
        "D": 15,  # Nhóm PASK
        "E": 12,  # Trọng số
        "F": 18,  # Điểm tối thiểu
        "G": 15,  # Tự đánh giá
        "H": 15,  # Quản lý đánh giá
        "I": 18,  # Điểm tối thiểu theo cấp độ
        "J": 18,  # Điểm tự đánh giá
        "K": 15,  # Điểm quản lý đánh giá
        "L": 15,  # Điểm GAP
    }

    # Padding dùng cho mỗi loại cột khi tính toán độ rộng
    padding_factors = {
        "A": 1.2,  # STT (số)
        "B": 1.5,  # Văn bản dài
        "C": 1.5,  # Văn bản dài
        "D": 1.4,  # Văn bản ngắn
        "E": 1.3,  # Số
        "F": 1.3,  # Số
        "G": 1.3,  # Số
        "H": 1.3,  # Số
        "I": 1.3,  # Số
        "J": 1.3,  # Số
        "K": 1.3,  # Số
        "L": 1.3,  # Số
    }

    # Khởi tạo từ điển để theo dõi độ rộng tối đa của mỗi cột
    max_lengths = {col: 0 for col in min_widths.keys()}

    # Đảm bảo cột L (GAP) có các thuộc tính giống hệt cột K

    # Merge cells and set the headers
    ws.merge_cells("A1:I1")
    ws["A1"] = "ĐÁNH GIÁ KẾT QUẢ CÔNG VIỆC ĐỊNH KÌ"
    ws["A1"].font = Font(bold=True, size=16)
    ws["A1"].alignment = Alignment(horizontal="center", vertical="center")

    start_time = evaluate.get(EvaluateKey.START_TIME)
    end_time = evaluate.get(EvaluateKey.END_TIME)

    month_start_time = "" if not start_time else start_time.month
    month_end_time = "" if not end_time else end_time.month
    year_start_time = "" if not start_time else start_time.year
    year_end_time = "" if not end_time else end_time.year
    month_submit_time = "" if not user_submit_time else user_submit_time.month
    year_submit_time = "" if not user_submit_time else user_submit_time.year

    # Define the header details với định dạng theo mẫu
    header_details = [
        ("B2", "Họ và tên:", "C2", user.name, "E2", "Người đánh giá:", "F2", leader_title),
        (
            "B3",
            "Chức danh:",
            "C3",
            department_label,
            "E3",
            "Cấp độ chức danh hiện tại",
            "F3",
            job_title_level_label,
        ),
        (
            "B4",
            "Kỳ đánh giá",
            "C4",
            "Tháng {}/{} - Tháng {}/{}".format(month_start_time, year_start_time, month_end_time, year_end_time),
            "E4",
            "Thời gian đánh giá",
            "F4",
            "Tháng {} - {}".format(month_submit_time, year_submit_time),
        ),
    ]

    # Áp dụng định dạng và căn chỉnh theo mẫu
    for detail in header_details:
        ws[detail[0]] = detail[1]
        ws[detail[0]].alignment = Alignment(horizontal="right", vertical="center")
        ws[detail[0]].font = Font(size=16)

        ws[detail[2]] = detail[3]
        ws[detail[2]].alignment = Alignment(horizontal="left", vertical="center")
        ws[detail[2]].font = Font(size=16)

        ws[detail[4]] = detail[5]
        ws[detail[4]].alignment = Alignment(horizontal="right", vertical="center")
        ws[detail[4]].font = Font(size=16)

        ws[detail[6]] = detail[7]
        ws[detail[6]].alignment = Alignment(horizontal="left", vertical="center")
        ws[detail[6]].font = Font(size=16)

    # Thêm dòng trống dòng 5
    for col in range(1, 12 + 1):  # 12+1 để bao gồm cả cột L
        cell = ws.cell(row=5, column=col)
        cell.alignment = Alignment(horizontal="center", vertical="center")
        cell.font = Font(size=16)

    # Định nghĩa border style - không có border
    thin_border = Border(left=Side(style=None), right=Side(style=None), top=Side(style=None), bottom=Side(style=None))

    # Thêm tiêu đề section I
    ws.merge_cells("A6:K6")
    ws["A6"] = "I - ĐÁNH GIÁ VỀ NĂNG LỰC"
    ws["A6"].font = bold_font
    ws["A6"].alignment = Alignment(horizontal="left", vertical="center")
    # ws["A6"].fill = white_fill

    for col in range(1, 12 + 1):
        cell = ws.cell(row=6, column=col)
        # cell.fill = white_fill
        cell.font = bold_font

    # Set the main table headers with correct styles
    # Row 7 - header with dark fill
    ws.merge_cells("A7:A8")
    ws["A7"] = "STT"
    ws["A7"].font = bold_font
    ws["A7"].alignment = Alignment(horizontal="center", vertical="center")
    ws["A7"].fill = table_header_fill_light
    ws.merge_cells("B7:B8")
    ws["B7"] = "Nhóm năng lực"
    ws["B7"].font = bold_font
    ws["B7"].alignment = Alignment(horizontal="center", vertical="center")
    ws["B7"].fill = table_header_fill_light
    ws.merge_cells("C7:C8")
    ws["C7"] = "Tên năng lực"
    ws["C7"].font = bold_font
    ws["C7"].alignment = Alignment(horizontal="center", vertical="center")
    ws["C7"].fill = table_header_fill_light
    ws.merge_cells("D7:D8")
    ws["D7"] = "Nhóm PASK"
    ws["D7"].font = bold_font
    ws["D7"].alignment = Alignment(horizontal="center", vertical="center")
    ws["D7"].fill = table_header_fill_light
    ws.merge_cells("E7:E8")
    ws["E7"] = "Trọng số\n(a)"
    ws["E7"].font = bold_font
    ws["E7"].alignment = Alignment(horizontal="center", vertical="center")
    ws["E7"].fill = table_header_fill_light

    # Merge F7:H7 for "Điểm đánh giá"
    ws.merge_cells("F7:H7")
    ws["F7"] = "Điểm đánh giá"
    ws["F7"].font = bold_font
    ws["F7"].alignment = Alignment(horizontal="center", vertical="center")
    ws["F7"].fill = table_header_fill_dark

    # Merge I7:K7 for "Điểm đạt được"
    ws.merge_cells("I7:K7")
    ws["I7"] = "Điểm đạt được"
    ws["I7"].font = bold_font
    ws["I7"].alignment = Alignment(horizontal="center", vertical="center")
    ws["I7"].fill = table_header_fill_dark

    # GAP header
    ws["L7"] = "GAP\n(h) = (g)-(e)"
    ws["L7"].font = bold_font
    ws["L7"].alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
    ws["L7"].fill = table_header_fill_dark
    ws.merge_cells("L7:L8")

    # Row 8 - sub headers
    sub_headers = [
        ("F8", "Điểm tối thiểu theo cấp độ hiện tại\n(b)"),
        ("G8", "Tự đánh giá\n(c)"),
        ("H8", "Quản lý đánh giá\n(d)"),
        ("I8", "Điểm tối thiểu theo cấp độ\n(e) = (a)*(b)"),
        ("J8", "Tự đánh giá\n(f) = (a)*(c)"),
        ("K8", "Quản lý đánh giá\n(g) = (a)*(d)"),
    ]

    for header in sub_headers:
        ws[header[0]] = header[1]
        ws[header[0]].font = bold_font
        ws[header[0]].alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        ws[header[0]].fill = table_header_fill_light

    # Thêm dòng tổng điểm (row 9)
    ws["E9"] = "Tổng điểm"

    # Thêm giá trị tổng điểm
    ws["I9"] = format_number(total_point_min_of_level)
    ws["I9"].alignment = Alignment(horizontal="left", vertical="center")
    ws["I9"].font = Font(size=16)
    ws["J9"] = format_number(total_point_achieved_by_user)
    ws["J9"].alignment = Alignment(horizontal="left", vertical="center")
    ws["J9"].font = Font(size=16)
    ws["K9"] = format_number(total_point_achieved_by_leader)
    ws["K9"].alignment = Alignment(horizontal="left", vertical="center")
    ws["K9"].font = Font(size=16)
    ws["L9"] = format_number(total_gap)
    ws["L9"].alignment = Alignment(horizontal="left", vertical="center")
    ws["L9"].font = Font(size=16)

    ws["E10"] = "Cấp độ chức danh"
    ws["I10"] = job_title_level_min_point_label
    ws["I10"].alignment = Alignment(horizontal="left", vertical="center")
    ws["I10"].font = Font(size=16)
    ws["J10"] = leader_job_title_level_label
    ws["J10"].alignment = Alignment(horizontal="left", vertical="center")
    ws["J10"].font = Font(size=16)
    ws["K10"] = user_job_title_level_label
    ws["K10"].alignment = Alignment(horizontal="left", vertical="center")
    ws["K10"].font = Font(size=16)

    # Định dạng căn trái cho các ô tổng điểm
    for col in ["I", "J", "K", "L"]:
        ws[f"{col}9"].alignment = Alignment(horizontal="left", vertical="center")
        ws[f"{col}9"].font = Font(size=16)

    # Thêm dữ liệu từ data_exports và điều chỉnh định dạng theo offset mới (bắt đầu từ dòng 10)
    start_row = 11
    for i, row_data in enumerate(data_exports):
        for j, value in enumerate(row_data):
            if j == 0:  # STT
                ws.cell(row=start_row + i, column=j + 1, value=value)
                ws.cell(row=start_row + i, column=j + 1).alignment = Alignment(horizontal="center")
                ws.cell(row=start_row + i, column=j + 1).font = Font(size=16)
            elif j in [4, 5, 6, 7, 8, 9, 10, 11, 12]:  # Các cột số (bao gồm cả cột L - GAP)
                ws.cell(row=start_row + i, column=j + 1, value=format_number(value))
                ws.cell(row=start_row + i, column=j + 1).alignment = Alignment(horizontal="left")
                ws.cell(row=start_row + i, column=j + 1).font = Font(size=16)
            else:
                ws.cell(row=start_row + i, column=j + 1, value=value)
                ws.cell(row=start_row + i, column=j + 1).font = Font(size=16)

    # Calculate the row number for section II
    section_ii_row = start_row + len(data_exports) + 1

    # Adding section II
    ws.merge_cells(f"A{section_ii_row}:L{section_ii_row}")
    ws[f"A{section_ii_row}"] = "II - Đánh giá về hiệu suất công việc"
    ws[f"A{section_ii_row}"].font = Font(bold=True, size=16)
    # ws[f"A{section_ii_row}"].fill = white_fill

    # ws.merge_cells(f"A{section_ii_row + 1}:L{section_ii_row + 1}")
    # ws[f"A{section_ii_row + 1}"] = "(Phần đánh cho nhân viên tự đánh giá)"
    # ws[f"A{section_ii_row + 1}"].fill = white_fill

    performance_evaluation_headers = [
        ("1", "Khối lượng công việc", evaluate.get("amount_of_work_note")),
        ("2", "Chất lượng công việc", evaluate.get("work_quality_note")),
        ("3", "Tiến độ, thời gian xử lý công việc", evaluate.get("work_process_note")),
        ("4", "Đề xuất của cá nhân viên", evaluate.get("user_note")),
    ]

    # Vị trí bắt đầu của phần đánh giá
    current_row = section_ii_row + 1  # Sửa thành +2 để không ghi đè lên section header

    # Tạo bảng cho phần Khối lượng công việc
    # Thêm tiêu đề và nội dung
    ws[f"A{current_row}"] = performance_evaluation_headers[0][0]  # Số thứ tự "1"
    ws[f"A{current_row}"].alignment = Alignment(horizontal="center")
    ws[f"A{current_row}"].font = Font(size=16)
    ws[f"B{current_row}"] = performance_evaluation_headers[0][1]  # "Khối lượng công việc"
    ws[f"B{current_row}"].alignment = Alignment(horizontal="left")
    ws[f"B{current_row}"].font = Font(size=16)
    # ws.merge_cells(f"C{current_row}:L{current_row}")
    ws[f"C{current_row}"] = performance_evaluation_headers[0][2]  # Nội dung ghi chú
    ws[f"C{current_row}"].font = Font(size=16)

    # Tiếp tục tạo bảng chi tiết
    current_row += 1

    # Tạo header cho bảng chi tiết
    table_headers = [("B", "Loại công việc"), ("C", "SL công việc đã làm"), ("D", "Số giờ làm việc")]

    # Tạo header cho bảng
    for header in table_headers:
        col, title = header
        ws[f"{col}{current_row}"] = title
        ws[f"{col}{current_row}"].alignment = Alignment(horizontal="center", vertical="center")
        ws[f"{col}{current_row}"].font = bold_font
        ws[f"{col}{current_row}"].fill = table_header_fill_light
        ws[f"{col}{current_row}"].border = thin_border

    # Đảm bảo cột L luôn có cùng style với cột K
    # ws[f"L{current_row}"].alignment = Alignment(horizontal="center", vertical="center")
    # ws[f"L{current_row}"].font = bold_font
    # ws[f"L{current_row}"].fill = table_header_fill_light
    # ws[f"L{current_row}"].border = thin_border

    # Kiểm tra nếu có dữ liệu task_performances
    if task_performances:
        for task_category in task_performances:
            current_row += 1
            # Gán dữ liệu cho các ô
            ws[f"B{current_row}"] = task_category.get("task_category", "")

            # Lấy giá trị số
            total_completed_tasks = task_category.get("total_completed_tasks", 0)
            total_hours_completed = task_category.get("total_hours_completed", 0)

            # Định dạng số với dấu ' ở đầu
            ws[f"C{current_row}"] = format_number(total_completed_tasks)
            ws[f"D{current_row}"] = format_number(total_hours_completed)

        # Áp dụng style
        ws[f"C{current_row}"].alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
        ws[f"C{current_row}"].font = Font(size=16)
        ws[f"D{current_row}"].alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
        ws[f"D{current_row}"].font = Font(size=16)
        ws[f"E{current_row}"].alignment = Alignment(horizontal="left", vertical="center", wrap_text=True)
        ws[f"E{current_row}"].font = Font(size=16)

        # Áp dụng border cho các ô (cả L nếu có)
        for col in ["C", "D", "E", "F", "G", "H", "I", "J", "K", "L"]:
            if col in ["C", "D", "E"]:  # Chỉ cần các cột hiển thị trong bảng này
                ws[f"{col}{current_row}"].border = thin_border
                ws[f"{col}{current_row}"].font = Font(size=16)
    else:
        # Nếu không có dữ liệu, tạo 4 hàng trống
        for i in range(4):
            current_row += 1
            # Tạo các ô trống trong hàng
            for col in ["C", "D", "E", "F", "G", "H", "I", "J", "K", "L"]:
                # Áp dụng border cho các ô
                if col in ["C", "D", "E"]:  # Chỉ cần các cột hiển thị trong bảng này
                    ws[f"{col}{current_row}"].border = thin_border
                    ws[f"{col}{current_row}"].font = Font(size=16)

    # Chuyển đến phần Chất lượng công việc
    current_row += 1

    # Tiếp tục với các phần đánh giá khác
    for i in range(1, len(performance_evaluation_headers)):
        ws[f"A{current_row}"] = performance_evaluation_headers[i][0]  # Số thứ tự
        ws[f"A{current_row}"].alignment = Alignment(horizontal="center")
        ws[f"A{current_row}"].font = Font(size=16)
        ws[f"B{current_row}"] = performance_evaluation_headers[i][1]  # Tiêu đề
        ws[f"B{current_row}"].alignment = Alignment(horizontal="left")
        ws[f"B{current_row}"].font = Font(size=16)
        ws[f"C{current_row}"] = performance_evaluation_headers[i][2]  # Nội dung
        ws[f"C{current_row}"].font = Font(size=16)
        ws[f"C{current_row}"].alignment = Alignment(horizontal="left", wrap_text=True)
        current_row += 1
    # Calculate the row number for section III
    section_iii_row = current_row + 1

    # Adding section III
    ws.merge_cells(f"A{section_iii_row}:L{section_iii_row}")
    ws[f"A{section_iii_row}"] = "III - Đánh giá & Đề xuất của Quản lý"
    ws[f"A{section_iii_row}"].font = bold_font
    # ws[f"A{section_iii_row}"].fill = table_header_fill_light

    # Set value before merging
    ws[f"B{section_iii_row + 1}"] = evaluate.get("leader_review")
    ws[f"B{section_iii_row + 1}"].font = Font(size=16)
    ws[f"B{section_iii_row + 1}"].alignment = Alignment(wrap_text=True, horizontal="left")
    # ws[f"A{section_iii_row + 1}"].fill = table_header_fill_light
    # ws.merge_cells(f"B{section_iii_row + 1}:L{section_iii_row + 1}")

    # Áp dụng border cho toàn bộ bảng

    for row in range(1, section_iii_row + 2):
        for col in range(1, 12 + 1):  # 12+1 để bao gồm cả cột L
            cell = ws.cell(row=row, column=col)
            cell.border = thin_border
            cell.font = Font(size=16)

    # Cập nhật độ rộng của các cột dựa trên nội dung

    # Import này sẽ được dùng cả trong backup method
    # Duyệt qua tất cả các cell trong bảng, để tính độ rộng tối đa cho mỗi cột
    # for row in range(1, section_iii_row + 2):
    #     for col in range(1, 12 + 1):  # 12+1 để bao gồm cả cột L
    #         try:
    #             cell = ws.cell(row=row, column=col)
    #             if isinstance(cell, Cell) and not isinstance(cell, MergedCell):
    #                 col_letter = get_column_letter(col)
    #                 if col_letter in max_lengths and cell.value is not None:
    #                     # Tính toán độ rộng dựa trên nội dung
    #                     width = get_column_width(cell.value)
    #                     # Cập nhật độ rộng tối đa cho cột nếu cần
    #                     if width > max_lengths[col_letter]:
    #                         max_lengths[col_letter] = width
    #         except Exception:
    #             # Bỏ qua lỗi nếu có
    #             pass
    # Bổ sung kiểm tra các header dài (đặc biệt cho các cột dịnh dạng)
    header_texts = {
        "F": "Điểm tối thiểu theo cấp độ hiện tại",
        "G": "Tự đánh giá",
        "H": "Quản lý đánh giá",
        "I": "Điểm tối thiểu theo cấp độ",
        "J": "Điểm tự đánh giá",
        "K": "Điểm quản lý đánh giá",
        "L": "Điểm GAP",
    }

    # Kiểm tra độ rộng của các header
    for col, text in header_texts.items():
        width = get_column_width(text)
        if width > max_lengths[col]:
            max_lengths[col] = width

    # # Áp dụng độ rộng tối đa cho mỗi cột với padding phù hợp
    # for col, min_width in min_widths.items():
    #     # Áp dụng hệ số padding tùy theo loại cột
    #     padding_factor = padding_factors.get(col, 1.3)

    #     # Tính toán độ rộng cần thiết cho cột, với padding
    #     calculated_width = max_lengths[col] * padding_factor

    #     # Đảm bảo độ rộng không nhỏ hơn giá trị tối thiểu
    #     final_width = max(min_width, calculated_width)

    #     # Đặt độ rộng cho cột
    #     ws.column_dimensions[col].width = final_width
    for col in ws.columns:
        max_length = 0
        column_index = col[0].column  # Get column index
        column_letter = get_column_letter(column_index)  # Convert to letter
        for cell in col:
            if isinstance(cell, MergedCell):  # Skip merged cells
                continue
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = max_length + 2
        if column_letter in ["A"]:
            adjusted_width = 10
        if column_letter in ["L"]:
            adjusted_width = 15
        if column_letter in ["B", "C"]:
            adjusted_width = 70
        ws.column_dimensions[column_letter].width = adjusted_width

    try:
        # Lưu file Excel mà không cần xử lý thêm các MergedCell
        # Vì MergedCell trong openpyxl là read-only, chúng ta không thể gán giá trị trực tiếp cho nó

        # Lưu file với tùy chọn để đảm bảo tính tương thích
        wb.save(file_path)
    except Exception as e:
        # Ghi lại lỗi nếu có
        print(f"Error saving Excel file: {str(e)}")
        # Thử lưu lại với phương pháp an toàn hơn
        try:
            # Tạo workbook mới
            wb_new = openpyxl.Workbook()
            ws_new = wb_new.active
            ws_new.title = "Sheet1"  # Đặt tên sheet giống như workbook gốc

            # Sao chép tất cả dữ liệu, style cũng như merge cells
            # Bước 1: Sao chép dữ liệu cơ bản
            for row in range(1, section_iii_row + 3):
                for col in range(1, 12 + 1):
                    try:
                        src_cell = ws.cell(row=row, column=col)
                        dest_cell = ws_new.cell(row=row, column=col)

                        # Chỉ sao chép giá trị của các ô không phải MergedCell
                        # hoặc ô đầu tiên trong MergedCell
                        if not isinstance(src_cell, MergedCell):
                            dest_cell.value = src_cell.value

                        # Sao chép style
                        if src_cell.has_style:
                            dest_cell.font = src_cell.font
                            dest_cell.font.size = 16
                            dest_cell.border = src_cell.border
                            dest_cell.fill = src_cell.fill
                            dest_cell.number_format = src_cell.number_format
                            dest_cell.protection = src_cell.protection
                            dest_cell.alignment = src_cell.alignment
                    except Exception:
                        pass

            # Bước 2: Sao chép thông tin về merge cells
            for merge_range in list(ws.merged_cells.ranges):
                try:
                    # Dùng chính xác các chỉ số hàng/cột như trong các merge cũ
                    start_col = get_column_letter(merge_range.min_col)
                    end_col = get_column_letter(merge_range.max_col)
                    start_row = merge_range.min_row
                    end_row = merge_range.max_row

                    # Tạo range mới và merge
                    merge_string = f"{start_col}{start_row}:{end_col}{end_row}"
                    ws_new.merge_cells(merge_string)
                except Exception:
                    pass

            # Bước 3: Sao chép độ rộng cột
            for col in range(1, 12 + 1):
                try:
                    col_letter = get_column_letter(col)
                    if col_letter in ws.column_dimensions:
                        ws_new.column_dimensions[col_letter].width = ws.column_dimensions[col_letter].width
                except Exception:
                    pass

            # Lưu file mới
            wb_new.save(file_path)
        except Exception as backup_error:
            print(f"Error with backup save method: {str(backup_error)}")
            # Nếu vẫn không thể lưu, tạo file đơn giản nhất có thể
            simple_wb = openpyxl.Workbook()
            simple_wb.save(file_path)
