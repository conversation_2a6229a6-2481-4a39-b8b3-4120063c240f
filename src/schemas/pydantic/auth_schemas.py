#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 15/01/2024
"""


from pydantic import Field

from src.schemas.pydantic import BaseResponseSchema


class AuthResponseSchema(BaseResponseSchema):
    token: str = Field(..., description="Thông tin token")


class DataDecodeSchema(BaseResponseSchema):
    name: str = Field(..., description="Tên người dùng")
    email: str = Field(..., description="Email")
    user_id: str = Field(..., description="Id người dùng bên lark")
    open_id: str = Field(..., description="OpenId người dùng bên lark")
    status: dict = Field(..., description="Trạng thái")
    is_admin: bool = Field(..., description="Kiểm tra admin")
    exp: int = Field(..., description="Thời gian hế<PERSON> hạn")
