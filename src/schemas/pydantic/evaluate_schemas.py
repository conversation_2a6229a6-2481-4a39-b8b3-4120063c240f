from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, Field, validator

from src.common.common import PaskEnum
from src.schemas.pydantic import CustomBaseSchema


class TimeEvalOfUserSchema(BaseModel):
    user_id: str = Field(...)
    user_type: int = Field(...)
    start_time: datetime = Field(...)
    end_time: datetime = Field(...)
    submit_time: Optional[datetime] = Field(default=None)


class PointSchema(BaseModel):
    user_id: str
    user_type: int
    point: float


class BehaviorExpressionInCompetencySchema(BaseModel):
    description: str = Field(default="", min_length=0)
    level: int = Field(None, ge=1, le=5)
    is_activate: bool = Field(False)
    point_min: int = Field(0, ge=0, le=10)
    point_max: int = Field(0, ge=0, le=10)
    point_mid: int = Field(0, ge=0, le=10)


class LstCompetencySchema(BaseModel):
    competency_id: str = Field("")
    name: str
    description: Optional[str] = Field("")
    weight: int
    point_min: int
    points: List[PointSchema]
    behavior_expressions: Optional[List[BehaviorExpressionInCompetencySchema]]
    job_title_levels: Optional[List[dict]]
    pask_code: Optional[Literal[tuple(PaskEnum.Code.get_list_allow())]] = Field(None)
    reference_info: Optional[str] = Field(None)
    default_apply_department_ids: Optional[List[str]] = Field([])
    is_default_apply: bool = Field(False)
    competency_default_id: Optional[str] = Field("")
    competency_reference_id: Optional[str] = Field("")


class CompetencyGroupsSchema(BaseModel):
    competency_group_id: str = Field("")
    name: str
    lst_competency: List[LstCompetencySchema]
    competency_group_default_id: Optional[str] = Field(None)
    competency_group_id: Optional[str] = Field(None)


class AddEvaluateRequestSchema(CustomBaseSchema):
    user_id: str = Field(...)
    evaluate_period_id: str = Field(...)
    time_eval_of_users: List[TimeEvalOfUserSchema] = Field(..., min_items=2)
    competency_framework_id: str = Field(...)

    @validator("time_eval_of_users")
    def check_time_eval_of_users(cls, values):
        for value in values:
            if value.start_time > value.end_time:
                raise ValueError("End time must be greater than start time")

        return values


class SaveDraftPointSchema(BaseModel):
    user_id: str
    user_type: int
    point: Optional[float]


class SaveDraftLstCompetencySchema(BaseModel):
    competency_id: str = Field("")
    name: str = Field("")
    description: Optional[str] = Field("")
    weight: int = Field(0)
    point_min: int = Field(0)
    points: List[SaveDraftPointSchema] = Field([])
    behavior_expressions: Optional[List[dict]] = Field([])
    job_title_levels: Optional[List[dict]] = Field([])
    pask_code: Optional[Literal[tuple(PaskEnum.Code.get_list_allow())]] = Field(None)
    reference_info: Optional[str] = Field(None)
    default_apply_department_ids: Optional[List[str]] = Field([])
    is_default_apply: bool = Field(False)
    competency_default_id: Optional[str] = Field("")
    competency_reference_id: Optional[str] = Field("")


class saveDraftCompetencyGroupSchema(BaseModel):
    competency_group_id: str
    name: str
    lst_competency: List[SaveDraftLstCompetencySchema]
    competency_group_default_id: Optional[str] = Field(None)
    competency_group_id: Optional[str] = Field(None)


class SaveDraftEvaluateRequestSchema(CustomBaseSchema):
    evaluate_id: Optional[str]
    time_eval_of_users: Optional[List[TimeEvalOfUserSchema]]
    competency_groups: Optional[List[saveDraftCompetencyGroupSchema]]
    before_job_title_level_id: Optional[str]
    after_job_title_level_id: Optional[str]
    amount_of_work_note: Optional[str]
    work_quality_note: Optional[str]
    user_note: Optional[str]
    hr_review: Optional[str]
    leader_review: Optional[str]
    work_process_note: Optional[str]


class UpdateEvaluateRequestSchema(CustomBaseSchema):
    time_eval_of_users: Optional[List[TimeEvalOfUserSchema]]
    competency_groups: Optional[List[CompetencyGroupsSchema]]
    before_job_title_level_id: Optional[str]
    after_job_title_level_id: Optional[str]
    amount_of_work_note: Optional[str]
    work_quality_note: Optional[str]
    user_note: Optional[str]
    hr_review: Optional[str]
    leader_review: Optional[str]
    work_process_note: Optional[str]


class EvaluateFiltersSchema(BaseModel):
    field: str = Field("")
    operator: str = Field("")
    value: list = Field([])


class EvaluateFiltersRequestSchema(BaseModel):
    search: Optional[str]
    evaluate_filters: Optional[List[EvaluateFiltersSchema]] = Field([])

    class Config:
        orm_mode = True


class EvaluateFiltersManagedTeamRequestSchema(BaseModel):
    search: Optional[str]
    evaluate_filters: Optional[List[EvaluateFiltersSchema]] = Field([])

    class Config:
        orm_mode = True


class ListEvaluateByUserIdRequestSchema(BaseModel):
    evaluate_filters: Optional[List[EvaluateFiltersSchema]] = Field([])

    class Config:
        orm_mode = True
