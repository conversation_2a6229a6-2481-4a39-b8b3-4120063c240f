from datetime import datetime
from typing import Dict, List, Optional

from pydantic import Field, validator
from src.schemas.pydantic import CustomBaseSchema


class AddEvaluatePeriodTemplateRequestSchema(CustomBaseSchema):
    name: str = Field(...)
    start_time: datetime = Field(...)
    end_time: datetime = Field(...)
    start_time_aggregate_performance: Optional[datetime] = Field(default=None)
    end_time_aggregate_performance: Optional[datetime] = Field(default=None)
    config: Dict[str, int] = Field(...)
    exclude_department_ids: Optional[List[str]] = Field(default=[])
    specific_department_ids: Optional[List[str]] = Field(default=[])

    @validator("start_time", "end_time", pre=True)
    def validate_datetime_format(cls, value: str) -> datetime:
        try:
            return datetime.strptime(value, "%Y-%m-%dT%H:%M:%SZ")
        except ValueError:
            raise ValueError(f"Datetime must be in format '%Y-%m-%dT%H:%M:%SZ', but got '{value}'")

    @validator("end_time")
    def validate_time_interval(cls, end_time, values):
        start_time = values.get("start_time")
        if start_time and end_time:
            if end_time <= start_time:
                raise ValueError("end_time must be greater than start_time")
            # if (end_time - start_time) < timedelta(days=30): # Comment out for testing
            #     raise ValueError("Time interval between start_time and end_time must be at least 30 days")
        return end_time


class FilterEvaluatePeriodRequestSchema(CustomBaseSchema):
    pass
