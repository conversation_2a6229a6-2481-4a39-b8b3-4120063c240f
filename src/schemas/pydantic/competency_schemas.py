from typing import List, Optional, Literal

from pydantic import BaseModel, Field

from src.schemas.pydantic import CustomBaseSchema, CustomBaseUpdateSchema
from src.common.common import PaskEnum


class BehaviorExpressionSchema(BaseModel):
    description: str = Field("")
    level: int = Field(ge=1, le=5)
    is_activate: bool = Field(False)

    class Config:
        orm_mode = True
        allow_population_by_field_name = True


class AddCompetencyRequestSchema(CustomBaseSchema):
    name: str = Field(min_length=1)
    description: str = Field("")
    competency_group_id: str = Field(min_length=1, max_length=255)
    behavior_expressions: List[BehaviorExpressionSchema] = Field(..., min_items=1, max_items=5)
    pask_code: Literal[tuple(PaskEnum.Code.get_list_allow())] = Field()
    reference_info: Optional[str]
    default_apply_department_ids: Optional[List[str]] = Field([])
    is_default_apply = bool = Field(False)

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class UpdateCompetencyRequestSchema(CustomBaseUpdateSchema):
    name: Optional[str]
    description: Optional[str]
    competency_group_id: Optional[str]
    behavior_expressions: List[BehaviorExpressionSchema] = Field(None, min_items=1, max_items=5)
    pask_code: Literal[tuple(PaskEnum.Code.get_list_allow())] = Field()
    reference_info: Optional[str]
    default_apply_department_ids: Optional[List[str]] = Field([])
    is_default_apply = bool = Field(False)

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class DeleteCompetencyRequestSchema(BaseModel):
    competency_ids: List[str] = Field(..., min_items=1)

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class UpdateGroupsRequestSchema(BaseModel):
    competency_group_id: str = Field(min_length=1, max_length=255)
    competency_ids: List[str] = Field(..., min_items=1)

    class Config:
        orm_mode = True
