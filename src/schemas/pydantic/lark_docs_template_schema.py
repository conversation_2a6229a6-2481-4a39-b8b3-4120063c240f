from pydantic import Field

from src.common.choices import LarkDocsTemplateTypeChoice
from src.schemas.pydantic import BaseRequestBodySchema, BaseResponseSchema, BaseDetailResponse


class CreateLarkDocsTemplateSchema(BaseRequestBodySchema):
    template_name: str = Field(min_length=1, max_length=255)
    folder_token: str = Field()
    type: LarkDocsTemplateTypeChoice = Field()
    key: str = Field()
    template_config: dict = Field()


class DetailLarkDocsTemplateSchema(BaseDetailResponse):
    template_id: str
    template_name: str = Field(min_length=1, max_length=255)
    folder_token: str = Field()
    type: LarkDocsTemplateTypeChoice = Field()
    template_config: dict = Field()


class ListLarkDocsTemplateResponseSchema(BaseResponseSchema):
    data: list[DetailLarkDocsTemplateSchema]
