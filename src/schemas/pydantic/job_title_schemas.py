from typing import List
from pydantic import BaseModel, Field, validator

class JobTitleLevelSchema(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    level: int = Field(..., gte=1, lte=10)

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class JobTitlesSchema(BaseModel):
    job_title_id: str = Field(...)
    job_title_levels: List[JobTitleLevelSchema]

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True

    @validator("job_title_levels")
    def data_unique(cls, v):
        names = [i.name.lower() for i in v]
        levels = [i.level for i in v]

        if len(names) != len(set(names)):
            raise ValueError("Name must be unique")
        if len(levels) != len(set(levels)):
            raise ValueError("Level must be unique")
        return v


class AddJobTitleLevelSchema(BaseModel):
    job_titles: List[JobTitlesSchema]

    class Config:
        orm_mode = True
