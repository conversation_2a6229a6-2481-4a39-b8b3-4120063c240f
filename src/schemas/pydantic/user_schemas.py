import dataclasses
import datetime
import re
from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from src.common.choices import PatternsChoice
from src.common.constants import ConstantGender, ConstantMaritalStatus, ConstantStatus
from src.schemas.pydantic import BaseRequestBodySchema, BaseResponseSchema, Field


def validate_email_by_domain(email):
    return re.match(PatternsChoice.MOBIO_EMAIL_PATTERN, email)


@dataclasses.dataclass
class Data:
    employee_code: int
    name: str
    email: str
    information: Dict[str, Any]


class AddUserRequestSchema(BaseRequestBodySchema):
    name: str = Field(min_length=1, max_length=255)
    email: str = Field(min_length=1, max_length=255)
    information: Dict[str, Any]


class GetUserRequestSchema(BaseRequestBodySchema):
    email: str = Field(min_length=1, max_length=255)


class UserResponseSchema(BaseResponseSchema):
    data: List[Data]


class UserDetailResponseSchema(BaseResponseSchema):
    data: Data


class UserInsertDataSchema(BaseModel):
    employee_code: str = Field(None)
    first_name: str
    middle_name: str = Field("")
    last_name: str
    primary_email: str
    personal_email: str = Field(None)
    username: str = Field(None)
    password: str = Field("123456")
    gender: int = Field(ConstantGender.DEFAULT)
    marital_status: int = Field(ConstantMaritalStatus.SINGLE)
    role: str = Field("user")
    status: int = Field(ConstantStatus.ACTIVE)

    class Config:
        orm_mode = True


class LoginDefaultSchema(BaseModel):
    email: str = Field("")

    class Config:
        orm_mode = True

    # @validator("email")
    # def validate_email(cls, v: str):
    #     if not any(v, validate_email_by_domain(v)):
    #         raise ValueError("Email is required")


class UserInforSchema(BaseModel):
    pass


class UserEncodeSchema(BaseModel):
    email: str
    name: str
    user_id: str
    open_id: str
    status: dict
    is_admin: bool


class GetUsersResponseSchema(BaseModel):
    user_id: str
    name: str
    thumb_avatar_link: str
    job_title: str
    job_title_id: Optional[str]
    job_title_level_id: Optional[str]
    job_title_level_name: str
    is_leader: bool
    employment_type: int
    department_owner_id: List[str]
    department_id: str
    order: int
    leader_user_id: Optional[str]
    color: Optional[str]
    working_seniority: Optional[str]


class GetUserDetailResponseSchema(BaseModel):
    user_id: str
    name: str
    thumb_avatar_link: str
    job_title: str
    primary_email: str
    leader_user: str
    leader_user_id: str
    department_id: str
    type: str
    role_names: list
    department: str
    phone_number: str
    start_onboard_at: datetime.datetime
    working_seniority: Optional[str]
    job_title_level_name: Optional[str]


class UpdateJobTitleLevelUsersSchema(BaseModel):
    job_title_level_id: str = Field(...)
    user_ids: List[str] = Field(...)


class UserFiltersSchema(BaseModel):
    field: str = Field("")
    operator: str = Field("")
    value: list = Field([])


class UserFiltersRequestSchema(BaseModel):
    search: str = Field("")
    user_filters: List[UserFiltersSchema] = Field([])
    user_ids: List[str] = Field([])

    class Config:
        orm_mode = True
