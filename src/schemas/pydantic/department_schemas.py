from typing import Optional, List
from pydantic import BaseModel, Field

from src.schemas.pydantic import CustomBaseSchema, CustomBaseUpdateSchema


class AddDepartmentSchema(CustomBaseSchema):
    name: str
    lark_department_id: str
    open_department_id: str
    note: str = Field("")
    department_code: str = Field("")

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class GetDepartmentsSchema(BaseModel):
    department_id: str
    name: str
    level: Optional[int]
    order: Optional[int]
    display: int


class OrderDepartmentSchema(BaseModel):
    department_id: str
    order: int


class ChangeOrderDepartmentsRequestSchema(CustomBaseUpdateSchema):
    departments: List[OrderDepartmentSchema]
