from typing import Optional

from pydantic import Field

from src.schemas.pydantic import CustomBaseSchema, CustomBaseUpdateSchema


class AddCompetencyGroupRequestSchema(CustomBaseSchema):
    name: str = Field(min_length=1, max_length=255)
    description: str = Field("")

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class UpdateCompetencyGroupRequestSchema(CustomBaseUpdateSchema):
    name: Optional[str]
    description: Optional[str]

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True
