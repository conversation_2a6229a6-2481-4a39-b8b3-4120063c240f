#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" 
    Author: tungdd
    Date created: 04/01/2024
"""
from datetime import datetime
from typing import Any, Optional, Union

from pydantic import BaseModel, Field

from src.utils.time_helper import get_time_now


class CustomBaseSchema(BaseModel):
    updated_time: datetime = Field(default=get_time_now())
    created_time: datetime = Field(default=get_time_now())
    created_by: str = Field(default="admin")
    updated_by: str = Field(default="admin")


class CustomBaseUpdateSchema(BaseModel):
    updated_time: datetime = Field(default=get_time_now())
    updated_by: str = Field(default="admin")
    created_by: str = Field(default=None)
    created_time: datetime = Field(default=None)


class BaseResponseSchema(BaseModel):
    code: int = Field(default=200, description="Mã code")
    message: str = Field(
        default="Request Success!!!",
        description="Message trả về (<PERSON><PERSON> thể kèm theo thông tin lỗi)",
    )
    data: Union[list, dict, Any] = Field(
        default=None,
        description="Dữ liệu trả về",
    )
    

class TestCallBackSchema(BaseModel):
    challenge: str = Field()


class BaseRequestBodySchema(CustomBaseSchema):
    pass


class BaseResponsePagingTokenSchema(CustomBaseSchema):
    after_token: str = Field(
        "",
        description="Token của page tiếp theo",
        examples=["MTI5MDMxOTAyMDMxMDMxMDMxMDIzZGFzZDI="],
    )
    before_token: str = Field(
        "",
        description="Token của page trước đó",
        examples=["MTI5MDMxOTAyMDMxMDMxMDMxMDIzZGFzZDI="],
    )
    per_page: int = Field(0, description="Số phần tử trên 1 page", examples=[10])
    total_page: int = Field(0, description="Tổng số page", examples=[110])


class BaseResponsePagingNumberSchema(BaseResponseSchema):
    paging: dict = Field(
        default={},
        description="Phải trình trang",
        # examples={
        #     "page": 1,
        #     "per_page": 10,
        #     "total_count": 110,
        #     "total_page": 11,
        # },
    )


class BaseDetailResponse(BaseModel):
    # id: Optional[str] = None
    updated_time: Optional[datetime]
    created_time: Optional[datetime]
    created_by: Optional[Any] = None
    updated_by: Optional[Any] = None
