from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class PolicyStatementSchema(BaseModel):
    Effect: str = Field(..., description="Allow or Deny")
    Action: Union[List[str], str] = Field(..., description="Action or actions to apply")
    Resource: Union[List[str], str] = Field(..., description="Resource ARN patterns")
    Condition: Optional[Dict[str, Dict[str, str]]] = Field(None, description="Optional conditions")


class PolicyDocumentSchema(BaseModel):
    Version: str = Field("2023-01-01", description="Policy language version")
    Id: Optional[str] = Field(None, description="Optional policy identifier")
    Statement: List[PolicyStatementSchema] = Field(..., description="Policy statements")


class PolicyCreateSchema(BaseModel):
    user_id: str = Field(..., description="User ID")
    name: str = Field(..., description="Unique policy name")
    description: Optional[str] = Field(None, description="Policy description")
    policy_type: str = Field("IDENTITY", description="Policy type (IDENTITY, RESOURCE, or PERMISSION_BOUNDARY)")
    document: PolicyDocumentSchema = Field(..., description="Policy document")


class PolicyUpdateSchema(BaseModel):
    name: Optional[str] = Field(None, description="Policy name")
    description: Optional[str] = Field(None, description="Policy description")
    document: Optional[PolicyDocumentSchema] = Field(None, description="Policy document")


class PolicyResponseSchema(BaseModel):
    policy_id: str = Field(..., description="Policy ID")
    name: str = Field(..., description="Policy name")
    description: Optional[str] = Field(None, description="Policy description")
    policy_type: str = Field(..., description="Policy type")
    document: PolicyDocumentSchema = Field(..., description="Policy document")
    created_time: str = Field(..., description="Creation timestamp")
    updated_time: str = Field(..., description="Last update timestamp")
    created_by: str = Field(..., description="Creator user ID")
    updated_by: str = Field(..., description="Last updater user ID")


class AttachPolicySchema(BaseModel):
    policy_id: str = Field(..., description="Policy ID to attach")


class DetachPolicySchema(BaseModel):
    policy_id: str = Field(..., description="Policy ID to detach")


class PolicyEvaluationRequestSchema(BaseModel):
    action: str = Field(..., description="Action to evaluate (e.g., 'user:List')")
    resource: str = Field(..., description="Resource ARN to evaluate (e.g., 'arn:app:user:*')")
    context: Optional[Dict[str, Any]] = Field({}, description="Additional context for condition evaluation")


class PolicyEvaluationResponseSchema(BaseModel):
    allowed: bool = Field(..., description="Whether the action is allowed")
    evaluated_policies: List[str] = Field(..., description="List of policy IDs that were evaluated")
    decisive_statement: Optional[Dict[str, Any]] = Field(None, description="The statement that determined the result")
