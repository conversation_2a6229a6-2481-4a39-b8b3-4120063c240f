from datetime import datetime
from typing import List, Literal, Optional, Union

from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, Field, validator

from src.common.choices import CompetencyFrameworkStatusChoice
from src.common.common import PaskEnum
from src.schemas.pydantic import CustomBaseSchema, CustomBaseUpdateSchema


class BehaviorExpressionInCompetencySchema(BaseModel):
    description: str = Field(default="", min_length=0)
    level: int = Field(None, ge=1, le=5)
    is_activate: bool = Field(False)
    point_min: int = Field(0, ge=0, le=10)
    point_max: int = Field(0, ge=0, le=10)
    point_mid: int = Field(0, ge=0, le=10)


class JobTitleLevelPointSchema(BaseModel):
    job_title_level_id: str = Field("", min_length=1)
    behavior_expression_level: int = Field(None, ge=-1, le=5)


class CompetencyInFrameworkSchema(BaseModel):
    name: str = Field("")
    description: str = Field("")
    weight: int = Field(0)
    behavior_expressions: List[BehaviorExpressionInCompetencySchema] = Field([])
    job_title_levels: List[JobTitleLevelPointSchema] = Field([])
    pask_code: Literal[tuple(PaskEnum.Code.get_list_allow())] = Field()
    reference_info: Optional[str]
    is_default_apply: bool = Field(False)
    competency_default_id: Optional[str] = Field(None, alias="id")
    competency_reference_id: Optional[str]

    @validator("competency_default_id", pre=True, always=True)
    def set_competency_default_id(cls, v, values):
        if values.get("is_default_apply"):
            return v
        return None

    class Config:
        allow_population_by_field_name = True


class CompetencyGroupSchema(BaseModel):
    name: str = Field(min_length=1)
    lst_competency: List[CompetencyInFrameworkSchema] = Field(...)
    is_default: bool = Field(default=False)
    competency_group_default_id: Optional[str] = Field(None, alias="id")

    @validator("competency_group_default_id", pre=True, always=True)
    def set_competency_group_default_id(cls, v, values):
        if values.get("is_default"):
            return v
        return None

    class Config:
        anystr_strip_whitespace = True
        allow_population_by_field_name = True


class AddCompetencyFrameworkRequestSchema(CustomBaseSchema):
    name: str = Field(min_length=1)
    department_id: str = Field(min_length=1)
    job_title_ids: List[str] = Field(
        ...,
    )
    start_time: datetime = Field(...)
    end_time: Optional[Union[datetime, str]] = Field(default=None)
    status: int = Field(...)
    competency_groups: List[CompetencyGroupSchema] = Field(...)

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True

    @validator("status")
    def validate_status(cls, value):
        if value not in [
            CompetencyFrameworkStatusChoice.ACTIVE.value,
            CompetencyFrameworkStatusChoice.DRAFT.value,
            CompetencyFrameworkStatusChoice.INACTIVE.value,
        ]:
            raise RequestValidationError("status must be active inactive or draft")
        return value


class UpdateCompetencyFrameworkRequestSchema(CustomBaseUpdateSchema):
    name: Optional[str]
    department_id: Optional[str]
    job_title_ids: Optional[list]
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    status: Optional[int]
    competency_groups: Optional[List[CompetencyGroupSchema]]

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class SaveDraftBehaviorExpressionInCompetencySchema(BaseModel):
    description: str = Field(default="")
    level: int = Field(default=0)
    is_activate: bool = Field(default=False)
    point_min: int = Field(default=None)


class SaveDraftJobTitleLevelPointSchema(BaseModel):
    job_title_level_id: str = Field(default="")
    behavior_expression_level: int = Field(default=0)


class SaveDraftCompetencyInFrameworkSchema(BaseModel):
    name: Optional[str] = Field("")
    description: Optional[str] = Field("")
    weight: Optional[int] = Field(0)
    behavior_expressions: Optional[List[BehaviorExpressionInCompetencySchema]] = Field([])
    job_title_levels: Optional[List[JobTitleLevelPointSchema]] = Field([])
    pask_code: Optional[Literal[tuple(PaskEnum.Code.get_list_allow())]] = Field()
    reference_info: Optional[str]
    is_default_apply: Optional[bool] = Field(False)
    competency_default_id: Optional[str] = Field(None, alias="id")
    competency_reference_id: Optional[str]

    @validator("competency_default_id", pre=True, always=True)
    def set_competency_default_id(cls, v, values):
        if values.get("is_default_apply"):
            return v
        return None

    class Config:
        allow_population_by_field_name = True


class SaveDraftCompetencyGroupSchema(BaseModel):
    name: Optional[str] = Field(min_length=1)
    lst_competency: Optional[List[SaveDraftCompetencyInFrameworkSchema]] = Field([])
    is_default: Optional[bool] = Field(False)
    competency_group_default_id: Optional[str] = Field(None, alias="id")

    @validator("competency_group_default_id", pre=True, always=True)
    def set_competency_group_default_id(cls, v, values):
        if values.get("is_default"):
            return v
        return None

    class Config:
        allow_population_by_field_name = True


class SaveDraftCompetencyFrameworkRequestSchema(CustomBaseSchema):
    session_id: Optional[str]
    name: Optional[str]
    competency_framework_id: Optional[str]
    department_id: Optional[str]
    job_title_ids: Optional[list]
    start_time: Optional[datetime]
    end_time: Optional[Union[datetime, str]] = Field(default=None)
    competency_groups: Optional[List[SaveDraftCompetencyGroupSchema]]

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class SaveDraftCompetencyFrameworkActiveRequestSchema(CustomBaseSchema):
    session_id: Optional[str]
    name: Optional[str]
    competency_framework_id: Optional[str]
    department_id: Optional[str]
    job_title_ids: Optional[list]
    start_time: Optional[datetime]
    end_time: Optional[Union[datetime, str]] = Field(default=None)
    competency_groups: Optional[List[SaveDraftCompetencyGroupSchema]]

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class CompetencyFrameworkFiltersSchema(BaseModel):
    field: str = Field("")
    operator: str = Field("")
    value: list = Field([])


class CompetencyFrameworkFiltersRequestSchema(BaseModel):
    search: str = Field("")
    competency_framework_filters: List[CompetencyFrameworkFiltersSchema] = Field([])

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True


class ChangeStatusRequestSchema(CustomBaseUpdateSchema):
    status: int = Field(...)

    class Config:
        orm_mode = True
        anystr_strip_whitespace = True
