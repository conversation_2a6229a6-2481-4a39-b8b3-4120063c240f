import asyncio
import datetime

from bson import ObjectId
from mobio.libs.logging import MobioLogging

from configs.database import get_db
from src.libs.lark_docs_sdk.builder.bitable import (
    BitableAppBuilder,
    BitableTableBuilder,
    BitableTableRecordsBuilder,
)
from src.libs.lark_docs_sdk.builder.folder import FolderBuilder
from src.libs.lark_docs_sdk.oapi.bitable import BitableApi
from src.libs.lark_docs_sdk.oapi.wiki import WikiApi
from src.models.mongo.company_config_model import CompanyConfigModel
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.task_performance_model import TaskPerformanceModel
from src.repositories.company_repository import CompanyRepository
from src.repositories.department_repository import DepartmentRepository
from src.repositories.users_repository import UserRepository


class SummaryPerformanceFileConst:
    COL_NAME = {
        "leader": "Quản lý",
        "department": "Phòng ban",
        "url": "Link đánh giá hiệu suất",
        "note": "Ghi chú",
    }

    COL_CONFIG = [
        {"field_name": COL_NAME["department"], "type": 1},
        {"field_name": COL_NAME["leader"], "type": 11},
        {
            "field_name": COL_NAME["url"],
            "type": 15,
            "description": {"text": "URL link file đánh giá hiệu suất. Lưu ý: Cần cấp quyền Xem cho Ladder"},
        },
        {"field_name": COL_NAME["note"], "type": 1},
    ]


class PerformanceFileConst:
    COL_NAME = {
        "user": "Nhân sự",
        "task_category": "Category",
        "total_completed_tasks": "Số lượng task đã làm",
        "total_hours_completed": "Workhours (Actual)",
    }


class HandlerSyncLarkbasePerformanceData:

    def __init__(self):
        self.s_db = next(get_db())
        self.company_repo = CompanyRepository(self.s_db)
        self.department_repo = DepartmentRepository(self.s_db)
        self.user_repo = UserRepository(self.s_db)
        self.evaluate_period_model = EvaluatePeriodModel()
        self.evaluate_model = EvaluateModel()
        self.task_performance_model = TaskPerformanceModel()
        self.company_config_model = CompanyConfigModel()

    async def gen_bitable_summary_performance_data(self, company_id, evaluate_period_data):
        log_prefix = "HandlerSyncLarkbasePerformanceData::_gen_bitable_summary_performance_data"
        evaluate_period_name = evaluate_period_data["name"]
        include_department_ids = evaluate_period_data.get("specific_department_ids", [])
        exclude_department_ids = evaluate_period_data.get("exclude_department_ids", [])
        folder_title = f"[Ladder] Tổng hợp đánh giá hiệu suất - {evaluate_period_name}"
        table_title = "File tổng hợp link đánh giá hiệu suất của các team"

        list_user_access_data = []
        list_leader_oid_empty_url = []
        list_department = await self.department_repo.get_all_departments_by_company_id(
            company_id=company_id, department_ids=include_department_ids
        )
        manager_info = {}
        user_mapping = {}

        for department in list_department:
            # Skip BOD
            if department.lower_case_name == "bod" or (
                exclude_department_ids and department.department_id in exclude_department_ids
            ):
                MobioLogging().info(f"{log_prefix}::department::{department.name}::skipped")
                continue

            # Check all leader in department
            for user in department.users:
                # Grant access
                for role in user.roles:
                    if role.lower_case_name in ["leader", "admin"]:
                        list_leader_oid_empty_url.append(user.open_user_id)
                        perm_data = {"user_open_id": user.open_user_id, "perm": "edit", "user_name": user.name}
                        if perm_data not in list_user_access_data:
                            list_user_access_data.append(perm_data)
                        break

                # Build manage info
                user_mapping[user.user_id] = user
                leader_user_id = user.leader_user_id
                if not leader_user_id:
                    continue
                if not manager_info.get(leader_user_id):
                    manager_info[leader_user_id] = {"leader_in_department": []}
                if not user.departments:
                    continue
                user_department = user.departments[0].name
                if (
                    user_department.lower() != "bod"
                    and user_department not in manager_info[leader_user_id]["leader_in_department"]
                ):
                    manager_info[leader_user_id]["leader_in_department"].append(user_department)

        # Build insert record
        insert_record = []
        for leader_user_id, info in manager_info.items():
            leader_data = user_mapping[leader_user_id]
            for department in info["leader_in_department"]:
                insert_record.append(
                    {
                        "user_name": leader_data.name,  # for sort
                        "fields": {
                            SummaryPerformanceFileConst.COL_NAME["department"]: department,
                            SummaryPerformanceFileConst.COL_NAME["leader"]: [{"id": leader_data.open_user_id}],
                            # SummaryPerformanceFileConst.COL_NAME["url"]: "",
                        },
                    }
                )

        # Create folder:
        folder_builder = FolderBuilder()
        folder_info = folder_builder.create_folder_with_permissions(
            folder_name=folder_title,
            list_user_access_data=list_user_access_data,
            need_notification=False,
        )
        folder_token = folder_info["token"]

        # Create bitable
        bitable_app_builder = BitableAppBuilder(folder_token)
        bitable_app_name = "File tổng hợp link đánh giá hiệu suất của các team"
        app_info = bitable_app_builder.create_app_with_permissions(
            name=bitable_app_name,
            list_user_access_data=list_user_access_data,
            need_notification_add_perm=False,
        )
        app_token = app_info["app"]["app_token"]
        default_table_id = app_info["app"]["default_table_id"]
        url = app_info["app"]["url"]

        # Create new table
        bitable_table_builder = BitableTableBuilder(app_token)
        new_table_params = {
            "table_name": table_title,
            "default_view_name": table_title,
            "fields": SummaryPerformanceFileConst.COL_CONFIG,
        }
        table_response = bitable_table_builder.create_table(**new_table_params)
        table_id = table_response["table_id"]

        # Delete default view
        bitable_table_builder.delete_table(table_id=default_table_id)

        # Insert record
        bitable_records_builder = BitableTableRecordsBuilder(app_token=app_token, table_id=table_id)
        # Sort record before insert
        insert_record = sorted(insert_record, key=lambda x: x["user_name"])
        bitable_records_builder.create_batch_record(records=insert_record)

        self._notify_leader_fill_file(
            evaluate_period_name=evaluate_period_name,
            list_leader_oid_empty_url=list_leader_oid_empty_url,
            summary_performance_url=url,
        )

        return {"app_token": app_token, "folder_token": folder_token, "table_id": table_id, "url": url}

    async def _save_performance_data_to_db(self, raw_data=[]):
        if not raw_data:
            return
        time_now = datetime.datetime.now(datetime.UTC)
        log_prefix = "HandlerSyncLarkbasePerformanceData::_save_performance_data_to_db"

        performance_data_by_user_id = {}
        for item in raw_data:
            record_data = item.get("fields", {})
            try:
                user_oid = record_data.get(PerformanceFileConst.COL_NAME["user"])[0].get("id")
                task_category = record_data.get(PerformanceFileConst.COL_NAME["task_category"])[0].get("text").strip()
                if not user_oid:
                    continue
                user = await self.user_repo.get_user_by_open_user_id(user_oid)
                if not user:
                    continue
                if not performance_data_by_user_id.get(user.user_id):
                    performance_data_by_user_id[user.user_id] = {}
                if not performance_data_by_user_id[user.user_id].get(task_category):
                    performance_data_by_user_id[user.user_id][task_category] = {
                        "task_category": str(task_category),
                        "total_completed_tasks": float(
                            record_data.get(PerformanceFileConst.COL_NAME["total_completed_tasks"])
                        ),
                        "total_hours_completed": float(
                            record_data.get(PerformanceFileConst.COL_NAME["total_hours_completed"])
                        ),
                    }
                else:
                    performance_data_by_user_id[user.user_id][task_category]["total_completed_tasks"] += int(
                        record_data.get(PerformanceFileConst.COL_NAME["total_completed_tasks"])
                    )
                    performance_data_by_user_id[user.user_id][task_category]["total_hours_completed"] += int(
                        record_data.get(PerformanceFileConst.COL_NAME["total_hours_completed"])
                    )
            except Exception:
                import traceback

                full_error = traceback.format_exc()
                MobioLogging().info(f"{log_prefix}::error save performance data::{str(full_error)}")

        return performance_data_by_user_id

    def _notify_leader_fill_file(self, evaluate_period_name, list_leader_oid_empty_url, summary_performance_url=""):

        # if not list_leader_oid_empty_url:
        #     return

        # msg = f"Vui lòng hoàn thành file đánh giá hiệu suất công việc cho kỳ đánh giá: [{evaluate_period_name}]({summary_performance_url})"
        # send_lark_notify.send_simple_text(open_ids=list(set(list_leader_oid_empty_url)), message_text=msg)
        return

    async def sync_data_from_lark(self):
        log_prefix = "SyncLarkbasePerformanceData::sync_data_from_lark"
        time_now = datetime.datetime.now(datetime.UTC)

        # Get list current evaluate period
        list_current_evaluate_period = await self.evaluate_period_model.find(
            {
                # "start_time": {"$lte": time_now},
                # "end_time": {"$gte": time_now},
            }
        )

        for period in list_current_evaluate_period:
            MobioLogging().info(f"{log_prefix}::processing::period::{period}")
            company_id = period.get("company_id")
            period_id = period["_id"]

            period_name = period.get("name", "")
            larkbase_summary_performance = period.get("larkbase_summary_performance")
            app_token = larkbase_summary_performance["app_token"]
            table_id = larkbase_summary_performance["table_id"]
            path = larkbase_summary_performance["url"]
            if "wiki" in path:
                wiki_token = path.split("/")[-1]
                node = WikiApi().get_node(
                    wiki_token=wiki_token
                )  # [!] Careful with no permission to Ladder will response 400
                app_id = node.get("node", {}).get("obj_token", "")
            else:
                app_id = path.split("/")[-1]
            batch_update_records = []
            list_leader_oid_empty_url = []
            after_token = ""
            start_time = period.get("start_time_aggregate_performance")
            end_time = period.get("end_time_aggregate_performance")

            condition_records = []

            start_timestamp = int((start_time - datetime.timedelta(days=2)).timestamp() * 1000) if start_time else None
            end_timestamp = int((end_time + datetime.timedelta(days=2)).timestamp() * 1000) if end_time else None

            if start_time:
                condition_records.append(
                    {
                        "field_name": "Từ ngày",
                        "operator": "isGreater",
                        "value": ["ExactDate", f"{start_timestamp}"],
                    }
                )

            if end_time:
                condition_records.append(
                    {
                        "field_name": "Đến ngày",
                        "operator": "isLess",
                        "value": ["ExactDate", f"{end_timestamp}"],
                    }
                )
            filter_condition = {}
            if condition_records:
                filter_condition = {
                    "conjunction": "and",
                    "conditions": condition_records,
                }
                # filter_str = f"AND({filter_records})"

            all_performance_data_by_user_id = {}
            while True:
                list_record = BitableApi().get_list_record_by_filter(
                    app_token=app_id, table_id=table_id, filter=filter_condition, per_page=10, after_token=after_token
                )
                if not list_record:
                    break
                performance_data_by_user_id = await self._save_performance_data_to_db(list_record.get("items", []))
                for user_id, task_performance in performance_data_by_user_id.items():
                    for category, performance in task_performance.items():
                        task_category = category
                        if not all_performance_data_by_user_id.get(user_id):
                            all_performance_data_by_user_id[user_id] = {
                                task_category: {
                                    "task_category": task_category,
                                    "total_completed_tasks": 0,
                                    "total_hours_completed": 0,
                                }
                            }

                        if not all_performance_data_by_user_id[user_id].get(task_category):
                            all_performance_data_by_user_id[user_id][task_category] = performance
                        else:
                            all_performance_data_by_user_id[user_id][task_category]["total_completed_tasks"] += float(
                                performance.get("total_completed_tasks", 0)
                            )
                            all_performance_data_by_user_id[user_id][task_category]["total_hours_completed"] += float(
                                performance.get("total_hours_completed", 0)
                            )

                after_token = list_record.get("page_token", "")
                if not after_token:
                    break
            evaluate_period_id = ObjectId(period_id)
            lst_user_id = list(all_performance_data_by_user_id.keys())
            performance_data = await self.task_performance_model.find(
                {
                    "company_id": company_id,
                    "evaluate_period_id": evaluate_period_id,
                    "user_id": {"$in": lst_user_id},
                }
            )
            if performance_data:
                await self.task_performance_model.delete_many(
                    {
                        "company_id": company_id,
                        "evaluate_period_id": evaluate_period_id,
                        "user_id": {"$in": lst_user_id},
                    }
                )

            for user_id, task_performance in all_performance_data_by_user_id.items():
                await self.task_performance_model.insert(
                    {
                        "evaluate_period_id": evaluate_period_id,
                        "company_id": company_id,
                        "user_id": user_id,
                        "task_performance": list(task_performance.values()),
                        "created_time": time_now,
                    }
                )
            # Update note
            if batch_update_records:
                BitableApi().update_batch_records(app_token=app_token, table_id=table_id, records=batch_update_records)

            # Notify fill file
            self._notify_leader_fill_file(period_name, list_leader_oid_empty_url, larkbase_summary_performance["url"])

    def owner_do(self):
        MobioLogging().info("owner_do::start")
        asyncio.run(self.sync_data_from_lark())
        MobioLogging().info("owner_do::finished")
        return


if __name__ == "__main__":
    HandlerSyncLarkbasePerformanceData().owner_do()
