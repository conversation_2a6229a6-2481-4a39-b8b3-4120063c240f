import asyncio
import datetime
import os

from mobio.libs.logging import <PERSON><PERSON><PERSON><PERSON>ging
from sqlalchemy.orm.session import Session

from configs.database import get_db
from src.common.choices import (
    CompetencyFrameworkStatusChoice,
    EvaluateStatusChoice,
    EvaluateStatusFilterChoice,
    UserEvaluateTypeChoice,
)
from src.common.common import CommonKey, CompetencyFrameworkKey, EvaluateKey
from src.models.mongo.competency_framework_model import CompetencyFrameworkModel
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.setting_model import SettingModel
from src.repositories.company_repository import CompanyRepository
from src.repositories.department_repository import DepartmentRepository
from src.repositories.users_repository import UserRepository
from src.utils import send_lark_notify


class HandlerAutoGenerateEvaluate:

    def __init__(self) -> None:
        self.s_db: Session = next(get_db())
        self.company_repo: CompanyRepository = CompanyRepository(self.s_db)
        self.department_repo: DepartmentRepository = DepartmentRepository(self.s_db)
        self.user_repo: UserRepository = UserRepository(self.s_db)
        self.evaluate_model: EvaluateModel = EvaluateModel()
        self.competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        self.evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()

    def _send_notify_missing_competency_framework(
        self, department_ids, evaluate_period_name, job_title_level_name=None
    ):
        if not department_ids:
            return
        msg = f"Không thể tạo bản đánh giá cho {evaluate_period_name} do phòng ban chưa cấu hình khung năng lực"

        if job_title_level_name:
            msg += f" cho cấp độ chức danh {job_title_level_name}"

        send_lark_notify.send_simple_text(department_ids=department_ids, message_text=msg)

    def _send_notify_created_evaluate(self, open_ids, evaluate_period_name, user_name, link_access):
        if not open_ids:
            return
        msg = f"{user_name} đang có 1 bản đánh giá năng lực đang chờ. [Truy cập]({link_access}) để đánh giá luôn cho nóng."
        send_lark_notify.send_simple_text(open_ids=open_ids, message_text=msg, title="Bạn có 1 bản đánh giá đang chờ")

    async def _build_mapping_evaluate_competency_groups_by_job_title_id(self, company_id, department_id):
        time_now = datetime.datetime.now(datetime.UTC)
        result_mapping = {}
        list_competency_framework = await self.competency_framework_model.find(
            {
                CommonKey.COMPANY_ID: company_id,
                CompetencyFrameworkKey.DEPARTMENT_ID: department_id,
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
                CompetencyFrameworkKey.START_TIME: {"$lte": time_now},
            },
        )
        list_competency_framework.sort([("updated_time", 1)])

        for competency_framework in list_competency_framework:
            competency_framework_id = competency_framework["_id"]
            job_title_ids = competency_framework.get("job_title_ids", [])
            for job_title_id in job_title_ids:
                if not result_mapping.get(job_title_id):
                    result_mapping[job_title_id] = {
                        "competency_groups": [],
                        "competency_framework_id": competency_framework_id,
                    }
                result_mapping[job_title_id]["competency_groups"].extend(
                    competency_framework.get("competency_groups", [])
                )
        return result_mapping

    def _build_competency_for_job_title_level(self, competency_groups, job_title_level_id):
        """Logic author: SonPT"""
        competency_for_user = []
        for competency_group in competency_groups:
            competency_group_id = competency_group.get(CompetencyFrameworkKey.COMPETENCY_GROUP_ID)
            competency_group_name = competency_group.get(CompetencyFrameworkKey.NAME)
            lst_competency = competency_group.get(CompetencyFrameworkKey.LST_COMPETENCY)
            lst_competency_for_evaluate = []

            for competency in lst_competency:
                competency_id = competency.get(CompetencyFrameworkKey.COMPETENCY_ID)
                competency_name = competency.get(CompetencyFrameworkKey.NAME)
                weight = competency.get(CompetencyFrameworkKey.WEIGHT)
                behavior_expressions = competency.get(CompetencyFrameworkKey.BEHAVIOR_EXPRESSIONS)
                description = competency.get(CompetencyFrameworkKey.DESCRIPTION)
                job_title_levels = competency.get(CompetencyFrameworkKey.JOB_TITLE_LEVELS)
                job_title_level_for_user = next(
                    filter(
                        lambda x: x.get(CompetencyFrameworkKey.JOB_TITLE_LEVEL_ID) == job_title_level_id,
                        job_title_levels,
                    ),
                    {},
                )
                behavior_expression_level = job_title_level_for_user.get(
                    CompetencyFrameworkKey.BEHAVIOR_EXPRESSION_LEVEL, -1
                )
                if behavior_expression_level == -1 or not behavior_expression_level:
                    continue

                behavior_expression = next(
                    filter(
                        lambda x: x.get(CompetencyFrameworkKey.LEVEL) == behavior_expression_level,
                        behavior_expressions,
                    ),
                    None,
                )
                point_min = behavior_expression.get(CompetencyFrameworkKey.POINT_MIN)

                lst_competency_for_evaluate.append(
                    {
                        EvaluateKey.COMPETENCY_ID: competency_id,
                        EvaluateKey.NAME: competency_name,
                        EvaluateKey.WEIGHT: weight,
                        EvaluateKey.POINT_MIN: point_min,
                        EvaluateKey.BEHAVIOR_EXPRESSIONS: behavior_expressions,
                        EvaluateKey.JOB_TITLE_LEVELS: job_title_levels,
                        EvaluateKey.DESCRIPTION: description,
                        EvaluateKey.PASK_CODE: competency.get(CompetencyFrameworkKey.PASK_CODE),
                        EvaluateKey.REFERENCE_INFO: competency.get(CompetencyFrameworkKey.REFERENCE_INFO),
                    }
                )
            if lst_competency_for_evaluate:
                competency_for_user.append(
                    {
                        EvaluateKey.COMPETENCY_GROUP_ID: competency_group_id,
                        EvaluateKey.NAME: competency_group_name,
                        EvaluateKey.LST_COMPETENCY: lst_competency_for_evaluate,
                    }
                )
        return competency_for_user

    async def async_owner_do(self):
        log_prefix = "HandlerAutoGenerateEvaluate::async_owner_do"
        time_now = datetime.datetime.now()
        list_company = await self.company_repo.get_all_company()

        setting_company = await SettingModel().find_one({})
        link_larkbase_task_performance = (
            setting_company.get("link_larkbase_task_performance") if setting_company else {}
        )
        status_filter = EvaluateStatusFilterChoice.WAITING_USER.value

        for company in list_company:
            MobioLogging().info(f"{log_prefix}::company::{company.name}")
            company_id = company.company_id
            # ================================================== Init evaluate time ================================================== #
            # Get all evaluate period: priority for yearly -> one_time
            list_evaluate_period = await self.evaluate_period_model.find(
                {
                    "company_id": company_id,
                    "start_time": {"$lte": time_now},
                    "end_time": {"$gte": time_now},
                },
            )
            list_evaluate_period.sort(
                [
                    ("repeat_type", -1),  # Ensure process repeat_type=`yearly` first
                    ("start_time", 1),
                ]
            )

            # Check evaluate period
            for evaluate_period in list_evaluate_period:
                evaluate_period_id = evaluate_period["_id"]
                config = evaluate_period.get("config", {})
                employee_evaluate_interval_day = config.get("employee_evaluate_interval_day", 5)
                leader_evaluate_interval_day = config.get("leader_evaluate_interval_day", 25)
                start_time_evaluate = evaluate_period["start_time"]
                end_time_evaluate = evaluate_period["end_time"]
                exclude_department_ids = evaluate_period.get("exclude_department_ids", [])
                include_department_ids = evaluate_period.get("specific_department_ids", [])
                larkbase_summary_performance = evaluate_period.get("larkbase_summary_performance")

                MobioLogging().info(
                    f"{log_prefix}::evaluate_period_name::{evaluate_period['name']}:generating evaluate data"
                )
                if link_larkbase_task_performance and not larkbase_summary_performance:
                    larkbase_summary_performance = {
                        "url": link_larkbase_task_performance.get("url"),
                        "app_token": setting_company.get("app_token") if setting_company else None,
                        "folder_token": setting_company.get("folder_token") if setting_company else None,
                        "table_id": (
                            link_larkbase_task_performance.get("table_id") if link_larkbase_task_performance else None
                        ),
                    }
                    await self.evaluate_period_model.update_by_set(
                        {"_id": evaluate_period["_id"]},
                        {
                            "larkbase_summary_performance": larkbase_summary_performance,
                            "updated_time": time_now,
                        },
                    )
                # # Calculate time evaluate
                # if start_time_evaluate < time_now:
                #     start_time_evaluate = time_now
                employee_evaluate_start_time = start_time_evaluate
                employee_evaluate_end_time = (
                    employee_evaluate_start_time + datetime.timedelta(days=employee_evaluate_interval_day)
                ).replace(hour=16, minute=59, second=59)

                # Tính time của leader
                leader_evaluate_start_time = employee_evaluate_end_time
                leader_evaluate_end_time = leader_evaluate_start_time + datetime.timedelta(
                    days=leader_evaluate_interval_day
                )
                leader_evaluate_end_time = leader_evaluate_end_time
                list_department = await self.department_repo.get_all_departments_by_company_id(
                    company_id=company.company_id, department_ids=include_department_ids
                )

                # Loop department
                for department in list_department:
                    MobioLogging().info(f"{log_prefix}::department.name::{department.name}")
                    # Skip bod
                    if department.lower_case_name == "bod" or (
                        exclude_department_ids and department.department_id in exclude_department_ids
                    ):
                        MobioLogging().info(f"{log_prefix}::skip department.name::{department.name}")
                        continue

                    # Get mapping evaluate competency groups by job title
                    mapping_evaluate_competency_groups_by_job_title_id = (
                        await self._build_mapping_evaluate_competency_groups_by_job_title_id(
                            company_id=company.company_id,
                            department_id=department.department_id,
                        )
                    )
                    # MobioLogging().info(
                    #     f"{log_prefix}::mapping_evaluate_competency_groups_by_job_title_id::{mapping_evaluate_competency_groups_by_job_title_id}"
                    # )
                    if not mapping_evaluate_competency_groups_by_job_title_id:
                        MobioLogging().info(f"{log_prefix}::skip department.name::{department.name}")
                        self._send_notify_missing_competency_framework(
                            department_ids=[department.department_id],
                            evaluate_period_name=evaluate_period["name"],
                        )
                        continue
                    list_open_id_user_created_evaluate = []

                    for user in department.users:

                        if not user.leader_user_id:
                            MobioLogging().error(
                                f"{log_prefix}::name::{user.name}:job_title_id::{user.job_title_id}::leader_user_id::is empty"
                            )
                            continue
                        MobioLogging().info(
                            f"{log_prefix}::name::{user.name}: department_name ::{department.name}: start generating evaluate data"
                        )

                        competency_framework_id = mapping_evaluate_competency_groups_by_job_title_id.get(
                            user.job_title_id, {}
                        ).get("competency_framework_id", None)
                        competency_groups = mapping_evaluate_competency_groups_by_job_title_id.get(
                            user.job_title_id, {}
                        ).get("competency_groups", [])
                        if not competency_groups:
                            MobioLogging().error(
                                f"{log_prefix}::name::{user.name}:job_title_id::{user.job_title_id}::competency_groups::is empty"
                            )
                            continue

                        # Build competency for user
                        job_title_level_id = user.job_title_level_id
                        if not job_title_level_id:
                            MobioLogging().error(
                                f"{log_prefix}::name::{user.name}:job_title_id::{user.job_title_id}::job_title_level_id::is empty"
                            )
                            continue

                        competency_group_for_user = self._build_competency_for_job_title_level(
                            competency_groups, job_title_level_id
                        )

                        if not competency_group_for_user:
                            job_title_level_name = (
                                user.job_title_level.name if user.job_title_level else "không xác định"
                            )
                            self._send_notify_missing_competency_framework(
                                department_ids=[department.department_id],
                                evaluate_period_name=evaluate_period["name"],
                                job_title_level_name=job_title_level_name,
                            )
                            MobioLogging().info(
                                f"{log_prefix}::name::{user.name}:job_title_id::{user.job_title_id}:job_title_level_id::{job_title_level_id}::competency_group_for_user::is empty"
                            )
                            continue

                        time_eval_of_users = [
                            {
                                EvaluateKey.USER_ID: user.user_id,
                                EvaluateKey.START_TIME: employee_evaluate_start_time,
                                EvaluateKey.END_TIME: employee_evaluate_end_time,
                                EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.OWNER.value,
                            },
                            {
                                EvaluateKey.USER_ID: user.leader_user_id,
                                EvaluateKey.START_TIME: leader_evaluate_start_time,
                                EvaluateKey.END_TIME: leader_evaluate_end_time,
                                EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.LEADER.value,
                            },
                        ]

                        # Check existed evaluate
                        evaluate_user_data = await self.evaluate_model.find_one(
                            {
                                CommonKey.COMPANY_ID: company.company_id,
                                EvaluateKey.DEPARTMENT_ID: department.department_id,
                                EvaluateKey.USER_ID: user.user_id,
                                EvaluateKey.EVALUATE_PERIOD_ID: evaluate_period_id,
                                # EvaluateKey.STATUS: EvaluateStatusChoice.WAITING.value,
                            }
                        )

                        if evaluate_user_data:
                            # Tạm thời bỏ luồng Update
                            pass
                            # if evaluate_user_data.get(EvaluateKey.STATUS) == EvaluateStatusChoice.COMPLETED.value:
                            #     continue
                            # MobioLogging().info(
                            #     f"{log_prefix}::name::{user.name}::evaluate_period_id::{evaluate_period_id}::existed evaluate data"
                            # )
                            # evaluate_user_update_data = {}
                            # if time_eval_of_users != evaluate_user_data.get(EvaluateKey.TIME_EVAL_OF_USERS):
                            #     evaluate_user_update_data["time_eval_of_users"] = time_eval_of_users
                            # if competency_group_for_user != evaluate_user_data.get(EvaluateKey.COMPETENCY_GROUPS):
                            #     evaluate_user_update_data["competency_groups"] = competency_group_for_user
                            # if competency_framework_id != evaluate_user_data.get(EvaluateKey.COMPETENCY_FRAMEWORK_ID):
                            #     evaluate_user_update_data["competency_framework_id"] = competency_framework_id
                            # if start_time_evaluate != evaluate_user_data.get(EvaluateKey.START_TIME):
                            #     evaluate_user_update_data["start_time"] = start_time_evaluate
                            # if end_time_evaluate != evaluate_user_data.get(EvaluateKey.END_TIME):
                            #     evaluate_user_update_data["end_time"] = end_time_evaluate

                            # if evaluate_user_update_data:
                            #     evaluate_user_update_data["updated_time"] = time_now
                            #     await self.evaluate_model.update_by_set(
                            #         {
                            #             CommonKey.COMPANY_ID: company.company_id,
                            #             EvaluateKey.USER_ID: user.user_id,
                            #             EvaluateKey.EVALUATE_PERIOD_ID: evaluate_period_id,
                            #         },
                            #         evaluate_user_update_data,
                            #     )
                            #     MobioLogging().info(
                            #         f"{log_prefix}::name::{user.name}::evaluate_period_id::{evaluate_period_id}::updated evaluate data::success"
                            #     )
                        else:
                            evaluate_user_data = {
                                CommonKey.COMPANY_ID: company.company_id,
                                EvaluateKey.DEPARTMENT_ID: department.department_id,
                                EvaluateKey.USER_ID: user.user_id,
                                EvaluateKey.START_TIME: start_time_evaluate,
                                EvaluateKey.END_TIME: end_time_evaluate,
                                EvaluateKey.EVALUATE_PERIOD_ID: evaluate_period_id,
                                EvaluateKey.COMPETENCY_GROUPS: competency_groups,
                                EvaluateKey.COMPETENCY_FRAMEWORK_ID: competency_framework_id,
                                EvaluateKey.TIME_EVAL_OF_USERS: time_eval_of_users,
                                EvaluateKey.STATUS: EvaluateStatusChoice.WAITING.value,
                                EvaluateKey.DISABLE_EDIT: False,
                                EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID: user.job_title_level_id,
                                EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID: "",
                                EvaluateKey.AMOUNT_OF_WORK_NOTE: "",
                                EvaluateKey.USER_NOTE: "",
                                EvaluateKey.HR_REVIEW: "",
                                EvaluateKey.LEADER_REVIEW: "",
                                EvaluateKey.WORK_PROCESS_NOTE: "",
                                CommonKey.CREATED_BY: "admin",
                                CommonKey.UPDATED_BY: "admin",
                                CommonKey.UPDATED_TIME: time_now,
                                CommonKey.CREATED_TIME: time_now,
                                EvaluateKey.STATUS_FILTER: status_filter,
                            }
                            inserted_id = await self.evaluate_model.insert_evaluate(evaluate_user_data)
                            MobioLogging().info(
                                f"{log_prefix}::name::{user.name}::inserted_id::{inserted_id}::evaluate_user_data::{evaluate_user_data}"
                            )
                            list_open_id_user_created_evaluate.append(user.open_user_id)
                            list_open_id_user_created_evaluate.append(user.leader_user_id)

                            # Push notify success
                            self._send_notify_created_evaluate(
                                open_ids=[user.open_user_id],
                                evaluate_period_name=evaluate_period["name"],
                                link_access=os.path.join(os.environ.get("PUBLIC_HOST"), "evaluate"),
                                user_name=user.name,
                            )
                            MobioLogging().info(f"{log_prefix}::name::{user.name}:push notify success")

        MobioLogging().info(f"{log_prefix}::finished")

    def owner_do(self):
        asyncio.run(self.async_owner_do())


if __name__ == "__main__":
    import asyncio

    # from scripts.ensure_default_evaluate_period import ensure_default_evaluate_period
    # asyncio.run(ensure_default_evaluate_period())
    HandlerAutoGenerateEvaluate().owner_do()
