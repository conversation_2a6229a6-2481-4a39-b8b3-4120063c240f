import datetime
import json

from mobio.libs.logging import Mo<PERSON>Logging
from sqlalchemy.orm import Session

from configs.database import get_db
from src.common.caching import lru_cache_m_caching
from src.common.choices import DepartmentChoice
from src.common.common import CompanyKey, EmploymentTypeKey
from src.common.constants import ConstantStatus
from src.libs.lark_docs_sdk.oapi.object.company import LarkObjectCompanyApi
from src.libs.lark_docs_sdk.oapi.object.department import LarkObjectDepartmentApi
from src.libs.lark_docs_sdk.oapi.object.user import LarkObjectUserApi
from src.models.postgres.base_model import (
    CompanyModel,
    DepartmentModel,
    EmploymentTypeModel,
    JobTitleModel,
    RoleModel,
    UserDepartmentModel,
    UserModel,
    UserRoleModel,
)
from src.utils import utf8_to_ascii
from src.utils.time_helper import to_time_at

employee_types_init = [
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 1,
        EmploymentTypeKey.VI_NAME: "Chính thức",
        EmploymentTypeKey.EN_NAME: "Regular",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 2,
        EmploymentTypeKey.VI_NAME: "Thực tập sinh",
        EmploymentTypeKey.EN_NAME: "Intern",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 5,
        EmploymentTypeKey.VI_NAME: "Tư vấn",
        EmploymentTypeKey.EN_NAME: "Consultant",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 6,
        EmploymentTypeKey.VI_NAME: "Thử việc",
        EmploymentTypeKey.EN_NAME: "Probation",
    },
]


class HandlerSyncDataFromLark:

    class Locker:
        """
        Ensure only one job sync data from lark is running at the same time
        """

        exp = 3600
        key = f"{lru_cache_m_caching.cache_prefix}#sync_data_from_lark"

        @staticmethod
        def get_lock_data():
            lock_data_dumps = lru_cache_m_caching.cache._redis.get(HandlerSyncDataFromLark.Locker.key)
            if not lock_data_dumps:
                return {}
            lock_data = json.loads(lock_data_dumps)
            return lock_data

        @staticmethod
        def lock():
            if HandlerSyncDataFromLark.Locker.get_lock_data().get("is_locked", False):
                raise RuntimeError("Sync data from lark is running")

            time_now = datetime.datetime.now(datetime.UTC).timestamp()
            data = {
                "is_locked": True,
                "created_time": time_now,
                "exp_time": time_now + HandlerSyncDataFromLark.Locker.exp,
            }
            lock_data_dumps = json.dumps(data, default=str)
            lru_cache_m_caching.cache._redis.set(
                HandlerSyncDataFromLark.Locker.key, lock_data_dumps, ex=HandlerSyncDataFromLark.Locker.exp
            )

        @staticmethod
        def unlock():
            lru_cache_m_caching.cache._redis.delete(HandlerSyncDataFromLark.Locker.key)

    @staticmethod
    def sync_data_from_lark():
        session_db: Session = next(get_db())
        company_info_of_lark = LarkObjectCompanyApi().get_company_information()
        if company_info_of_lark:
            company_info_of_lark = company_info_of_lark["tenant"]
        MobioLogging().info(f"sync_data_from_lark::company_info_of_lark::{company_info_of_lark}")
        if not company_info_of_lark:
            MobioLogging().error(f"sync_data_from_lark::company_info_of_lark::error::company from lark is empty")
            return

        query_of_company = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark["tenant_key"])
        if query_of_company.first():
            query_of_company.update(
                {
                    CompanyKey.NAME: company_info_of_lark["name"],
                    CompanyKey.AVATAR: company_info_of_lark["avatar"]["avatar_origin"],
                }
            )
        else:
            session_db.add(
                CompanyModel(
                    tenant_key=company_info_of_lark["tenant_key"],
                    name=company_info_of_lark["name"],
                    avatar=company_info_of_lark["avatar"]["avatar_origin"],
                )
            )
        session_db.commit()

        company_info = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark["tenant_key"]).first()

        for employee_type in employee_types_init:
            employee_type_current = (
                session_db.query(EmploymentTypeModel)
                .filter_by(employment_type_id=employee_type[EmploymentTypeKey.EMPLOYMENT_TYPE_ID])
                .update(
                    {
                        EmploymentTypeKey.VI_NAME: employee_type[EmploymentTypeKey.VI_NAME],
                        EmploymentTypeKey.EN_NAME: employee_type[EmploymentTypeKey.EN_NAME],
                    }
                )
            )
            if not employee_type_current:
                session_db.add(
                    EmploymentTypeModel(
                        company_id=company_info.company_id,
                        employment_type_id=employee_type[EmploymentTypeKey.EMPLOYMENT_TYPE_ID],
                        vi_name=employee_type[EmploymentTypeKey.VI_NAME],
                        en_name=employee_type[EmploymentTypeKey.EN_NAME],
                    )
                )
            session_db.commit()

        departments = LarkObjectDepartmentApi().get_all_department_can_access()
        MobioLogging().info(f"sync_data_from_lark::departments::{departments}")
        if not departments:
            MobioLogging().error(f"sync_data_from_lark::departments::error::department from lark is empty")
            return
        department_mapping = {}
        list_activating_user_id = set()
        child_departments = list()

        department_order = 0

        # Clear all relation user-department
        session_db.query(UserDepartmentModel).delete()
        session_db.query(UserRoleModel).delete()
        session_db.commit()

        admin_role = session_db.query(RoleModel).filter_by(lower_case_name="admin").first()
        leader_role = session_db.query(RoleModel).filter_by(lower_case_name="leader").first()
        user_role = session_db.query(RoleModel).filter_by(lower_case_name="user").first()

        # insert information department
        for department_information in departments:
            MobioLogging().info(f"sync_data_from_lark::department_information::{department_information}")
            if department_information["name"] == "Technical Services":
                pass

            department_id = department_information["department_id"]
            department_order += 1
            department_mapping[department_id] = {**department_information, "order": department_order}

            if int(department_information["parent_department_id"]):
                child_departments.append(department_information)
                continue

            display = 1
            if department_information["name"].lower() in [DepartmentChoice.BOD.value]:
                display = 0

            status = 1 if not department_information["status"]["is_deleted"] else 0
            lower_case_name = department_information["name"].lower()
            department_current = (
                session_db.query(DepartmentModel)
                .filter_by(lark_department_id=department_information["department_id"])
                .first()
            )
            owners = department_information.get("owners", [])

            department_name = department_information["name"]
            if not department_current:
                department_current = DepartmentModel(
                    lark_department_id=department_information["department_id"],
                    company_id=company_info.company_id,
                    open_department_id=department_information["open_department_id"],
                    name=department_information["name"],
                    lower_case_name=lower_case_name,
                    status=status,
                    display=display,
                    order=department_order,
                    owners=owners,
                )
                session_db.add(department_current)
            else:
                department_current.status = status
                department_current.display = display
                department_current.lower_case_name = lower_case_name
                department_current.name = department_information["name"]
                department_current.owners = owners
            session_db.commit()

            users = LarkObjectUserApi().get_list_all_users_of_department(department_id=department_id)
            department_mapping[department_id]["users"] = users
            if not users:
                continue

            for user_information in users:
                user_information_name = user_information["name"]
                MobioLogging().info(f"sync_data_from_lark::user_name::{user_information_name}")

                lark_user_id = user_information["user_id"]
                user_status = (
                    ConstantStatus.ACTIVE if user_information["status"]["is_activated"] else ConstantStatus.DEACTIVE
                )
                unsigned_name = utf8_to_ascii(user_information["name"]).lower()
                thumb_avatar_link = user_information["avatar"]["avatar_origin"]
                if user_status == ConstantStatus.ACTIVE:
                    list_activating_user_id.add(lark_user_id)
                is_tenant_manager = user_information["is_tenant_manager"]
                # Assign role
                user_information_roles = [user_role]
                if is_tenant_manager:
                    user_information_roles.append(admin_role)

                # Update job title
                job_title = session_db.query(JobTitleModel).filter_by(name=user_information["job_title"]).first()
                if user_information["job_title"]:
                    if not job_title:
                        job_title = JobTitleModel(
                            company_id=company_info.company_id,
                            name=user_information["job_title"],
                            lower_case_name=user_information["job_title"].lower(),
                            department_id=department_current.department_id,
                        )
                        session_db.add(job_title)
                    else:
                        job_title.company_id = company_info.company_id
                        job_title.name = user_information["job_title"]
                        job_title.lower_case_name = user_information["job_title"].lower()
                        job_title.department_id = department_current.department_id

                    session_db.commit()

                user_current = session_db.query(UserModel).filter_by(primary_email=user_information["email"]).first()
                if not user_current:
                    user_current = UserModel(
                        primary_email=user_information["email"],
                        company_id=company_info.company_id,
                        employee_code=UserModel().generate_employee_code(session_db),
                        name=user_information["name"],
                        phone_number=user_information["mobile"],
                        gender=user_information["gender"],
                        lark_user_id=lark_user_id,
                        open_user_id=user_information["open_id"],
                        thumb_avatar_link=thumb_avatar_link,
                        status=user_status,
                        start_onboard_at=to_time_at(user_information["join_time"]),
                        employment_type_id=user_information["employee_type"],
                        leader_user_id=user_information["leader_user_id"],
                        departments=[department_current],
                        job_title_id=job_title.job_title_id,
                        unsigned_name=unsigned_name,
                        roles=user_information_roles,
                        order=2 if user_information["leader_user_id"] else 1,
                    )
                    session_db.add(user_current)
                else:
                    if department_current not in user_current.departments:
                        user_current.departments.append(department_current)
                    user_current.status = user_status
                    user_current.thumb_avatar_link = thumb_avatar_link
                    user_current.employment_type_id = user_information["employee_type"]
                    user_current.leader_user_id = user_information.get("leader_user_id")
                    user_current.gender = user_information["gender"]
                    user_current.phone_number = user_information["mobile"]
                    user_current.name = user_information["name"]
                    user_current.lark_user_id = lark_user_id
                    user_current.start_onboard_at = to_time_at(user_information["join_time"])
                    user_current.open_user_id = user_information["open_id"]
                    if job_title:
                        user_current.job_title_id = job_title.job_title_id
                    user_current.unsigned_name = unsigned_name
                    user_current.roles = user_information_roles
                    user_current.order = 1
                session_db.commit()

        # replace leader_user_id of user
        users_in_system = session_db.query(UserModel).all()
        bod_department = session_db.query(DepartmentModel).filter_by(lower_case_name="bod").first()
        for user in users_in_system:

            # Update user leader id
            if user.leader_user_id:
                leader = session_db.query(UserModel).filter_by(open_user_id=user.leader_user_id).first()
                user.leader_user_id = leader.user_id if leader else None
            if bod_department in user.departments:
                user.leader_user_id = None

            # # Check user deleted in lark
            if user.lark_user_id not in list_activating_user_id:
                MobioLogging().info(
                    f"sync_data_from_lark::user_name::{user.name}::user_lark_id::{user.lark_user_id} is deleted"
                )
                user.status = ConstantStatus.DELETED

        session_db.commit()

        # Update owners in department
        list_department_system = session_db.query(DepartmentModel).all()
        for department_system in list_department_system:
            department_in_lark = department_mapping.get(department_system.lark_department_id)
            if not department_in_lark:
                continue
            leaders = department_in_lark.get("leaders", [])
            if not leaders:
                continue
            owners = []
            for leader in leaders:
                lark_leader_user_id = leader.get("leaderID")
                user = session_db.query(UserModel).filter_by(lark_user_id=lark_leader_user_id).first()
                user.roles.append(leader_role)
                if user_role in user.roles:
                    user.roles.remove(user_role)
                owners.append(user.user_id)
            department_system.owners = owners
        session_db.commit()

        # # Update job title in department BOD
        # bod_department = session_db.query(DepartmentModel).filter_by(lower_case_name="bod").first()
        # users_in_bod = department_mapping[bod_department.lark_department_id]["users"]
        # for user_bod in users_in_bod:
        #     job_title = (
        #         session_db.query(JobTitleModel)
        #         .filter_by(name=user_bod["job_title"])
        #         .update({JobTitleKey.DEPARTMENT_ID: bod_department.department_id})
        #     )
        #     session_db.commit()

        MobioLogging().info(f"sync_data_from_lark::done")

    def owner_do(self):
        MobioLogging().info(f"owner_do::start")
        lock_data = HandlerSyncDataFromLark.Locker.get_lock_data()
        if lock_data.get("is_locked", False):
            MobioLogging().info(f"HandlerSyncDataFromLark::owner_do::is locked before::{lock_data}")
            return
        try:
            HandlerSyncDataFromLark.Locker.lock()
            HandlerSyncDataFromLark.sync_data_from_lark()
        except Exception as e:
            import traceback

            MobioLogging().error(f"owner_do::error::{str(e)}::{traceback.format_exc()}")
        finally:
            HandlerSyncDataFromLark.Locker.unlock()
        MobioLogging().info(f"owner_do::done")
        return


if __name__ == "__main__":
    HandlerSyncDataFromLark.sync_data_from_lark()
