import asyncio
import datetime
import os

from mobio.libs.logging import <PERSON><PERSON>Logging
from sqlalchemy.orm.session import Session

from configs.database import get_db
from src.common.choices import (
    EvaluatePeriodStatusChoice,
    EvaluateStatusChoice,
    StatusSendReviewFrameworkToLeaderChoice,
)
from src.common.common import EvaluateKey
from src.models.mongo.competency_framework_model import CompetencyFrameworkModel
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.repositories.company_repository import CompanyRepository
from src.repositories.department_repository import DepartmentRepository
from src.repositories.users_repository import UserRepository
from src.utils import send_lark_notify
from src.utils.time_helper import get_time_now, to_as_timezone


class HandlerPushNotificationToUser:

    def __init__(self) -> None:
        self.s_db: Session = next(get_db())
        self.company_repo: CompanyRepository = CompanyRepository(self.s_db)
        self.department_repo: DepartmentRepository = DepartmentRepository(self.s_db)
        self.user_repo: UserRepository = UserRepository(self.s_db)
        self.evaluate_model: EvaluateModel = EvaluateModel()
        self.competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        self.evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()

    async def get_notify_send_review_framework_to_leader(self):
        start_time_condition = (
            datetime.datetime.now(datetime.UTC).replace(hour=0, minute=0, second=0)
            + datetime.timedelta(days=5)
            - datetime.timedelta(hours=7)
        )
        end_time_condition = (
            datetime.datetime.now(datetime.UTC).replace(hour=23, minute=59, second=59)
            + datetime.timedelta(days=5)
            - datetime.timedelta(hours=7)
        )
        filter_options = {
            "status": EvaluatePeriodStatusChoice.ACTIVE.value,
            "$and": [
                {"start_time": {"$lte": end_time_condition}},
                {"start_time": {"$gte": start_time_condition}},
            ],
            "status_send_review_framework_to_leader": {
                "$exists": False,
            },
        }
        evaluate_periods = await self.evaluate_period_model.find(filter_options)
        link_access = os.path.join(os.environ.get("PUBLIC_HOST"), "competency-framework")
        for evaluate_period in evaluate_periods:
            company_id = evaluate_period["company_id"]
            evaluate_period_name = evaluate_period["name"]
            MobioLogging().info(f"Evaluate period {evaluate_period_name} is not sent review framework to user")
            specific_department_ids = evaluate_period.get("specific_department_ids", [])
            departments = await self.department_repo.get_department_owners(
                company_id=company_id, department_ids=specific_department_ids
            )
            for department in departments:
                user_by_ids = await self.user_repo.get_users_by_ids(
                    company_id=department.company_id, user_ids=department.owners
                )
                user_lark_open_ids = [user.open_user_id for user in user_by_ids]
                MobioLogging().info(f"User lark open ids: {user_lark_open_ids}")
                text = f"Còn <font color='red'>5 ngày</font> nữa bắt đầu kỳ đánh giá năng lực, bạn hãy xem lại và cập nhật [Khung năng lực team - {department.name}]({link_access}) trước khi kỳ đánh giá bắt đầu."
                send_lark_notify.send_simple_text(
                    open_ids=user_lark_open_ids, message_text=text, title="Review khung năng lực team"
                )
            status_update_send_review_framework_to_leader = await self.evaluate_period_model.update_evaluate_period(
                evaluate_period_id=evaluate_period["_id"],
                payload={"status_send_review_framework_to_leader": StatusSendReviewFrameworkToLeaderChoice.SENT.value},
            )
            MobioLogging().info(
                f"Update status send review framework to leader for evaluate period {evaluate_period_name} to {StatusSendReviewFrameworkToLeaderChoice.SENT.value} :: {status_update_send_review_framework_to_leader}"
            )

    async def get_notify_send_review_framework_to_user(self):
        log_prefix = "HandlerUpdateStatusForCompetencyFramework::async_owner_do"
        session_db: Session = next(get_db())

        evaluate_model: EvaluateModel = EvaluateModel()
        evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
        user_repo: UserRepository = UserRepository(session_db)

        time_now = get_time_now()

        evaluates_waiting = await evaluate_model.get_evaluates_by_status(EvaluateStatusChoice.WAITING.value)
        MobioLogging().info(f"{log_prefix}::evaluates_waiting::{evaluates_waiting}")

        for evaluate in evaluates_waiting:
            evaluate_id = str(evaluate.get(EvaluateKey.ID))
            evaluate_period = await evaluate_period_model.get_evaluate_period_by_id(
                evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)
            )
            if not evaluate_period:
                MobioLogging().info(
                    f"{log_prefix}::evaluate_period_id::{evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)}::not_found"
                )
                continue
            time_eval_of_users = evaluate.get(EvaluateKey.TIME_EVAL_OF_USERS)
            end_time = to_as_timezone(evaluate.get(EvaluateKey.END_TIME))
            for time_eval_of_user in time_eval_of_users:
                user_id = time_eval_of_user.get(EvaluateKey.USER_ID)
                start_time_of_user = to_as_timezone(time_eval_of_user.get(EvaluateKey.START_TIME))
                end_time_of_user = to_as_timezone(time_eval_of_user.get(EvaluateKey.END_TIME))

                user = await user_repo.get_user_by_id(user_id)

                if not user:
                    MobioLogging().info(f"{log_prefix}::user_id::{user_id}::not_found")
                    continue

                MobioLogging().info(f"{log_prefix}::user::{user.name}::{user.primary_email}")
                text = ""

                # Calculate days difference
                days_until_end = (end_time_of_user.date() - time_now.date()).days

                # Adjust times to UTC+7 for display
                end_time_utc7 = end_time_of_user + datetime.timedelta(hours=7)

                title = "Ladder"
                link_access = os.path.join(os.environ.get("PUBLIC_HOST"), "evaluate?id={}".format(evaluate_id))
                # if days_from_start == 0:  # First day notification
                #     text = f"Hôm nay là ngày bắt đầu kỳ đánh giá: {start_time_utc7.strftime('%d-%m-%Y %H:%M')}. Vui lòng bắt đầu thực hiện đánh giá của bạn!"
                if days_until_end == 3:  # Last 3 days notification
                    title = f"Còn 3 ngày hết hạn đánh giá"
                    text = f"Chỉ còn <font color='red'>3 ngày</font> hết hạn đánh giá. Bạn vui lòng thực hiện [Đánh giá kỳ hiện tại]({link_access}) hoàn thành đánh giá trước ngày {end_time_utc7.strftime('%d-%m-%Y')}. \nNếu đã hoàn thành, bạn vui lòng bỏ qua tin nhắn này."
                # if days_until_end < 0:  # Evaluation period ended
                #     text = f"Kỳ đánh giá của bạn đã kết thúc vào {end_time_utc7.strftime('%d-%m-%Y %H:%M')}."

                if text:
                    send_lark_notify.send_simple_text(open_ids=[user.open_user_id], message_text=text, title=title)

            if end_time.date() < time_now.date():
                data_update = {EvaluateKey.STATUS: EvaluateStatusChoice.COMPLETED.value, EvaluateKey.DISABLE_EDIT: True}
                await evaluate_model.update_evaluate(evaluate_id, data_update)
                MobioLogging().debug(f"{log_prefix}::update evaluate::{evaluate_id}::completed")

        MobioLogging().info(f"{log_prefix}::end::{get_time_now()}")

    async def async_owner_do(self):
        await self.get_notify_send_review_framework_to_leader()
        await self.get_notify_send_review_framework_to_user()

    def owner_do(self):
        asyncio.run(self.async_owner_do())


if __name__ == "__main__":
    HandlerPushNotificationToUser().owner_do()
