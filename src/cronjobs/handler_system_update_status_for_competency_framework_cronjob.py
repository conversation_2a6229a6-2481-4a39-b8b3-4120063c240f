import asyncio

from mobio.libs.logging import MobioLogging

from src.common.choices import CompetencyFrameworkStatusChoice
from src.common.common import CompetencyFrameworkKey
from src.models.mongo.competency_framework_model import CompetencyFrameworkModel


class HandlerUpdateStatusForCompetencyFramework:

    async def async_owner_do(self):
        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        competency_framework_expired = await competency_framework_model.get_competency_frameworks_expired()
        MobioLogging().info(
            f"HandlerUpdateStatusForCompetencyFramework :: async_owner_do :: {competency_framework_expired}"
        )

        for competency_framework in competency_framework_expired:
            competency_framework_id = competency_framework.get(CompetencyFrameworkKey.ID)

            await competency_framework_model.update_competency_framework(
                str(competency_framework_id),
                {CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.INACTIVE.value},
            )
            MobioLogging().info(
                f"HandlerUpdateStatusForCompetencyFramework :: competency_framework :: {competency_framework} :: deactivated"
            )

        return True

    def owner_do(self):
        return asyncio.run(self.async_owner_do())


if __name__ == "__main__":
    HandlerUpdateStatusForCompetencyFramework().owner_do()
