#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 15/01/2024
"""

import datetime
import json
import uuid


class CustomJSONEncoder(json.JSONEncoder):
    def __init__(self, format_time="%Y-%m-%dT%H:%M:%SZ"):
        super().__init__()
        self.format_time = format_time

    def default(self, o):
        if type(o) == datetime.date:
            value = datetime.datetime.combine(o, datetime.datetime.min.time())
            return value.strftime(self.format_time)
        elif type(o) == datetime.datetime:
            return o.strftime(self.format_time)
        elif isinstance(o, uuid.UUID):
            return str(o)
        elif isinstance(o, (bytes, bytearray)):
            return str(o, "utf-8")
        return json.JSONEncoder.default(self, o=o)

    def encode(self, o):
        return json.JSONEncoder.encode(self, o)

    def loads(self, o):
        return json.loads(o)

    def json_loads(self, o):
        return json.loads(self.encode(o))

    def convert_value(self, o):
        if type(o) == datetime.date:
            value = datetime.datetime.combine(o, datetime.datetime.min.time())
            return value.strftime(self.format_time)
        elif type(o) == datetime.datetime:
            return o.strftime(self.format_time)
        elif isinstance(o, uuid.UUID):
            return str(o)
        elif isinstance(o, (bytes, bytearray)):
            return str(o, "utf-8")
        return o
