from src.libs.lark_docs_sdk.oapi.base import BaseApi
from mobio.libs.logging import Mo<PERSON>Logging

m_log = MobioLogging()


class SendMessageReceiveIdTypeEnum:
    OPEN_ID = "open_id"
    UNION_ID = "union_id"
    USER_ID = "user_id"
    EMAIL = "email"
    CHAT_ID = "chat_id"

    @classmethod
    def get_list_allows(cls):
        return (cls.OPEN_ID, cls.UNION_ID, cls.USER_ID, cls.EMAIL, cls.CHAT_ID)


class BotSendMessageApi(BaseApi):
    base_url = "https://open.larksuite.com/open-apis"

    def __init__(self):
        super().__init__()

        self.url_send_message = self.base_url + "/im/v1/messages"
        self.url_send_message_in_batches = self.base_url + "/message/v4/batch_send"

    def send_message(self, receive_id_type, payload):
        """
        Lark Docs: https://open.larksuite.com/document/server-docs/im-v1/message/create
        """
        try:
            if receive_id_type not in SendMessageReceiveIdTypeEnum.get_list_allows():
                raise ValueError("receive_id_type is not allowed")

            query_params = {"receive_id_type": receive_id_type}
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer %s" % self.get_tenant_access_token(),
            }
            m_log.info(f"BotSendMessageApi::send_message::query_params::{query_params}::payload::{payload}")
            response = self.client_request.retry_a_post_request(
                url=self.url_send_message,
                params=query_params,
                headers=headers,
                payload=payload,
            )
            m_log.info(f"BotSendMessageApi::send_message::response::{response.json()}")
            return response
        except Exception as e:
            m_log.info(f"BotSendMessageApi::send_message::error::{str(e)}")
            return False

    def send_messages_in_batches(self, payload):
        """
        Lark Docs: https://open.larksuite.com/document/server-docs/im-v1/batch_message/send-messages-in-batches
        """

        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer %s" % self.get_tenant_access_token(),
            }
            m_log.info(f"BotSendMessageApi::send_messages_in_batches::payload::{payload}")
            response = self.client_request.retry_a_post_request(
                url=self.url_send_message_in_batches,
                headers=headers,
                payload=payload,
            )
            m_log.info(f"BotSendMessageApi::send_messages_in_batches::response::{response.json()}")
            return response
        except Exception as e:
            m_log.info(f"BotSendMessageApi::send_messages_in_batches::error::{str(e)}")
            return False


if __name__ == "__main__":
    client = BotSendMessageApi()

    # payload = {
    #     # "open_ids": ["ou_029e840785e7d1a042be142850ad9dec"],
    #     "department_ids": ["od-399f6d9eda761bd24cf96b3c6d8183d2"],
    #     "msg_type": "interactive",
    #     "card": {
    #         "config": {"wide_screen_mode": True},
    #         "elements": [
    #             {
    #                 "tag": "div",
    #                 "fields": [
    #                     {
    #                         "is_short": True,
    #                         "text": {"tag": "lark_md", "content": "** Ticket source: **\nEvent and repair reports"},
    #                     },
    #                     {"is_short": True, "text": {"tag": "lark_md", "content": "**Ticket type: **\nAgent ticket"}},
    #                 ],
    #             }
    #         ],
    #         "header": {
    #             "template": "turquoise",
    #             "title": {"content": "Push message for BACKEND department", "tag": "plain_text"},
    #         },
    #     },
    # }
    # client.send_messages_in_batches(payload)

    import json

    c = {
        "config": {"wide_screen_mode": True},
        "elements": [
            {
                "alt": {"content": "", "tag": "plain_text"},
                "img_key": "img_7ea74629-9191-4176-998c-2e603c9c5e8g",
                "tag": "img",
            },
            {
                "tag": "div",
                "text": {
                    "content": "Have you ever had a spiritual resonance because of a book and started to understand life?\\ nWhich favorite books that you would strongly recommend to others?\\ n\\nJoin **4/23 Lark Reading Festival**, share your **favorite books** and **reading notes** to **win a reading gift worth one thousand CNY**!\\ n\\n Complete the questionnaire and show us your favorite books\\n Want to know what books others have recommended? View them now by [joining the group](https://open.larksuite.com/)\\nUse the [reading note template](https://open.larksuite.com/) (Open it in the desktop app) to record your experience\\nSpecial guests will join us from April 12",
                    "tag": "lark_md",
                },
            },
            {
                "actions": [
                    {
                        "tag": "button",
                        "text": {"content": "Recommend a good book now", "tag": "plain_text"},
                        "type": "primary",
                        "url": "https://open.larksuite.com/",
                    },
                    {
                        "tag": "button",
                        "text": {"content": "View the activity guide", "tag": "plain_text"},
                        "type": "default",
                        "url": "https://open.larksuite.com/",
                    },
                ],
                "tag": "action",
            },
        ],
        "header": {
            "template": "turquoise",
            "title": {"content": "Ladder send group message :P", "tag": "plain_text"},
        },
    }

    payload = {
        "receive_id": "ou_029e840785e7d1a042be142850ad9dec",
        "content": json.dumps(c),
        "msg_type": "interactive",
    }

    client.send_message(receive_id_type=SendMessageReceiveIdTypeEnum.OPEN_ID, payload=payload)
