from mobio.libs.logging import MobioLogging

from src.libs.lark_docs_sdk.oapi.base import BaseApi
from src.libs.lark_docs_sdk.oapi.drive_permission_member import DrivePermissionMember


class BitableApi(BaseApi):
    base_url = "https://open.larksuite.com/open-apis/bitable/v1/apps"

    # Const
    ADD_PERMISSIONS_TYPE = "bitable"

    def __init__(self):
        super().__init__()
        # App
        self.url_create_app = self.base_url
        self.url_copy_app = self.base_url + "/{app_token}/copy"

        # Table
        self.url_create_table = self.base_url + "/{app_token}/tables"
        self.url_update_table = self.base_url + "/{app_token}/tables/{table_id}"
        self.url_list_table = self.base_url + "/{app_token}/tables"
        self.url_delete_table = self.base_url + "/{app_token}/tables/{table_id}"

        # Record
        self.url_create_records = self.base_url + "/{app_token}/tables/{table_id}/records/batch_create"
        self.url_delete_records = self.base_url + "/{app_token}/tables/{table_id}/records/batch_delete"
        self.url_get_list_record = self.base_url + "/{app_token}/tables/{table_id}/records"
        self.url_get_list_record_search = self.base_url + "/{app_token}/tables/{table_id}/records/search"
        self.url_update_batch_records = self.base_url + "/{app_token}/tables/{table_id}/records/batch_update"

    def create_app(self, name, folder_token):
        url = self.url_create_app
        body = {
            "name": name,
            "folder_token": folder_token,
        }
        response = self.client_request.retry_a_post_request(
            url=url,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            payload=body,
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def copy_app(self, app_token, name, folder_token, without_content=False):
        url = self.url_copy_app.format(app_token=app_token)
        body = {
            "name": name,
            "folder_token": folder_token,
            "without_content": without_content,
        }
        response = self.client_request.retry_a_post_request(
            url=url,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            payload=body,
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def add_permissions(self, app_token, user_open_id, perm, need_notification=False):
        response = DrivePermissionMember(app_token).add_permissions(
            type=self.ADD_PERMISSIONS_TYPE,
            user_open_id=user_open_id,
            perm=perm,
            need_notification=need_notification,
        )
        return response

    # ================================================== Table ================================================== #
    def create_table(self, app_token, table_data):
        url = self.url_create_table.format(app_token=app_token)
        body = {"table": table_data}
        response = self.client_request.retry_a_post_request(
            url=url, headers={"Authorization": "Bearer " + self.get_tenant_access_token()}, payload=body
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def delete_table(self, app_token, table_id):
        url = self.url_delete_table.format(app_token=app_token, table_id=table_id)
        response = self.client_request.retry_a_delete_request(
            url=url,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def update_table(self, app_token, table_id, data):
        url = self.url_update_table.format(app_token=app_token, table_id=table_id)
        body = data
        response = self.client_request.retry_a_patch_request(
            url=url, headers={"Authorization": "Bearer " + self.get_tenant_access_token()}, json=body
        )
        response_json = response.json()
        MobioLogging().info(f"url::{url}::response_json::{response_json}")
        if response.status_code == 200:
            return response_json.get("data")
        return {}

    def get_list_table(self, app_token, per_page):
        url = self.url_list_table.format(app_token=app_token)
        response = self.client_request.retry_a_get_request(
            url=url,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            params={"per_page": per_page},
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    # ================================================== Records ================================================== #
    def get_list_record(self, app_token, table_id):
        url = self.url_get_list_record.format(app_token=app_token, table_id=table_id)
        response = self.client_request.retry_a_get_request(
            url=url, headers={"Authorization": "Bearer " + self.get_tenant_access_token()}
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def get_list_record_by_filter(self, app_token, table_id, filter, per_page, after_token):
        url = self.url_get_list_record_search.format(app_token=app_token, table_id=table_id)
        response = self.client_request.retry_a_post_request(
            url=url,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            payload={"filter": filter},
            params={"page_size": per_page, "page_token": after_token},
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def delete_records(self, app_token, table_id, records_ids):
        url = self.url_delete_records.format(app_token=app_token, table_id=table_id)
        body = {"records": records_ids}

        response = self.client_request.retry_a_post_request(
            url=url, headers={"Authorization": "Bearer " + self.get_tenant_access_token()}, payload=body
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def create_records(self, app_token, table_id, records):
        url = self.url_create_records.format(app_token=app_token, table_id=table_id)
        body = {"records": records}
        response = self.client_request.retry_a_post_request(
            url=url, headers={"Authorization": "Bearer " + self.get_tenant_access_token()}, payload=body
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}

    def update_batch_records(self, app_token, table_id, records):
        url = self.url_update_batch_records.format(app_token=app_token, table_id=table_id)
        body = {"records": records}
        response = self.client_request.retry_a_post_request(
            url=url, headers={"Authorization": "Bearer " + self.get_tenant_access_token()}, payload=body
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}
