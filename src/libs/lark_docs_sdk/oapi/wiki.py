from src.libs.lark_docs_sdk.oapi.base import BaseApi


class WikiApi(BaseApi):

    def __init__(self):
        super().__init__()
        self.base_url = f"{self.domain_api}/open-apis/wiki/v2/"
        self.url_get_node = self.base_url + "spaces/get_node?obj_type=wiki&token={wiki_token}"

    def get_node(self, wiki_token):
        url = self.url_get_node.format(wiki_token=wiki_token)
        response = self.client_request.retry_a_get_request(
            url=url,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            verify=False,
        )
        if response and response.status_code == 200:
            response_json = response.json()
            return response_json.get("data")
        return {}
