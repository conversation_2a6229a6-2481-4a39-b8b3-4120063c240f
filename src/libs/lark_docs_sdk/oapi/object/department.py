#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 23/01/2025
"""

from src.libs.lark_docs_sdk.oapi.base import BaseApi


class LarkObjectDepartmentApi(BaseApi):
    """
    Docs: https://open.larksuite.com/document/server-docs/contact-v3/department/field-overview
    """

    def __init__(self):
        super().__init__()

        self.get_bulk_department_information_url = f"{self.domain_api}/open-apis/contact/v3/departments/batch"
        self.get_single_department_information_url = "{domain}/open-apis/contact/v3/departments/{department_id}"

    def get_department_information(self, department_id, department_id_type="department_id"):
        headers = {"Authorization": "Bearer " + self.get_tenant_access_token()}
        params = {"department_id_type": department_id_type}
        response = self.client_request.retry_a_get_request(
            url=self.get_single_department_information_url.format(domain=self.domain_api, department_id=department_id),
            headers=headers,
            params=params,
        )
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data.get("department")
            return None

    def get_bulk_department_information(
        self,
        department_ids,
        department_id_type="department_id",
        user_id_type="user_id",
    ):
        if not department_ids:
            return None
        headers = {"Authorization": "Bearer " + self.get_tenant_access_token()}
        params = {
            "department_ids": department_ids,
            "department_id_type": department_id_type,
            "user_id_type": user_id_type,
        }
        response = self.client_request.retry_a_get_request(
            url=self.get_bulk_department_information_url,
            headers=headers,
            params=params,
        )
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data["items"]
            return None

    def get_all_department_can_access(self):
        # Get list department can access
        from src.libs.lark_docs_sdk.oapi.object.scope import LarkObjectScopeApi

        departments = []
        perm_scope = LarkObjectScopeApi().get_contacts_permission_scope()

        # Get department info
        if perm_scope:
            department_ids = perm_scope.get("department_ids", [])
            departments = self.get_bulk_department_information(department_ids)
        return departments


if __name__ == "__main__":
    bulk = LarkObjectDepartmentApi().get_all_department_can_access()
    print(bulk)
    single = LarkObjectDepartmentApi().get_department_information("a65ga351325d9a48")
    print(single)
