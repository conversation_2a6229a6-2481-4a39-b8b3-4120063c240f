#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 23/01/2025
"""

from src.libs.lark_docs_sdk.oapi.base import BaseApi


class LarkObjectCompanyApi(BaseApi):
    def __init__(self):
        super().__init__()

    def get_company_information(self):
        tenant_access_token = self.get_tenant_access_token()
        headers = {"Authorization": "Bearer " + tenant_access_token}
        api_url = f"{self.domain_api}/open-apis/tenant/v2/tenant/query"
        response = self.client_request.retry_a_get_request(url=api_url, headers=headers, verify=False, params={})
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data
            return None
        return None
