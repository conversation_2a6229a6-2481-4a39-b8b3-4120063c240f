#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 23/01/2025
"""

from src.libs.lark_docs_sdk.oapi.base import BaseApi


class LarkObjectScopeApi(BaseApi):
    """
    Docs: https://open.larksuite.com/document/server-docs/contact-v3/scope/overview
    """

    def __init__(self):
        super().__init__()
        self.get_contacts_permission_scope_url = f"{self.domain_api}/open-apis/contact/v3/scopes"

    def get_contacts_permission_scope(self, user_id_type="user_id", department_id_type="department_id"):
        headers = {"Authorization": "Bearer " + self.get_tenant_access_token()}
        params = {"user_id_type": user_id_type, "department_id_type": department_id_type}
        response = self.client_request.retry_a_get_request(url=self.get_contacts_permission_scope_url, headers=headers, params=params)
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data
            return None
        return None

