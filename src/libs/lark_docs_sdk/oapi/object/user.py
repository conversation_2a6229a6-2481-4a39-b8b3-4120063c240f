#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 23/01/2025
"""

from src.libs.lark_docs_sdk.oapi.base import BaseApi


class LarkObjectUserApi(BaseApi):
    def __init__(self):
        super().__init__()

        self.get_list_users_of_department_url = f"{self.domain_api}/open-apis/contact/v3/users/find_by_department"

    def get_token_user_by_code(self, code):
        tenant_access_token = self.get_tenant_access_token()
        body_request = {"grant_type": "authorization_code", "code": code}
        headers = {"Authorization": "Bearer " + tenant_access_token}
        api_url = f"{self.domain_api}/open-apis/authen/v1/access_token"

        self.logger.info(f"get_token_user_by_code :: api_url :: {api_url}")

        response = self.client_request.retry_a_post_request(
            url=api_url, headers=headers, payload=body_request, verify=False, times=5
        )
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data
            return response.json()
        return None

    def get_list_users_of_department(
        self, department_id, department_id_type="department_id", page_token="", page_size=50
    ):
        headers = {"Authorization": "Bearer " + self.get_tenant_access_token()}
        params = {
            "department_id": department_id,
            "department_id_type": department_id_type,
            "page_size": page_size,
            "page_token": page_token,
        }
        response = self.client_request.retry_a_get_request(
            url=self.get_list_users_of_department_url,
            headers=headers,
            params=params,
            verify=False,
        )
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data
            return response.json()
        return None

    def get_list_all_users_of_department(self, department_id, department_id_type="department_id"):
        list_users = []
        page_token = ""
        
        while True:
            response = self.get_list_users_of_department(
                department_id=department_id,
                department_id_type=department_id_type,
                page_token=page_token,
            )
            
            if not response:
                break
                
            items = response.get("items", [])
            if items:
                list_users.extend(items)
            
            page_token = response.get("page_token")
            if not page_token or not response.get("has_more", False):
                break
        
        return list_users


if __name__ == "__main__":
    list_user = LarkObjectUserApi().get_list_all_users_of_department(department_id="a65ga351325d9a48")
    print(list_user)
