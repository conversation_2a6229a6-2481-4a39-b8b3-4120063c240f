#!/usr/bin/env python
# -*- coding: utf-8 -*-

from src.libs.lark_docs_sdk.oapi.base import BaseApi


class LarkObjectAdminApi(BaseApi):
    """
    Docs: https://open.larksuite.com/document/server-docs/application-v6/admin/obtain-the-apps-installed-by-an-organization
    """

    def __init__(self):
        super().__init__()

        self.admin_scope_get_url = f"{self.domain_api}/open-apis/contact/v1/user/admin_scope/get"
        self.admin_list_url = f"{self.domain_api}/open-apis/user/v4/app_admin_user/list"

    def get_admin_scope(self):
        headers = {"Authorization": "Bearer " + self.get_tenant_access_token()}
        params = {
            "employee_id": "44af3adb",
        }
        response = self.client_request.retry_a_get_request(
            url=self.admin_scope_get_url.format(domain=self.domain_api),
            headers=headers,
            params=params,
        )
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data
            return None

    def get_admin_list(self):
        headers = {"Authorization": "Bearer " + self.get_tenant_access_token()}
        response = self.client_request.retry_a_get_request(
            url=self.admin_list_url.format(domain=self.domain_api),
            headers=headers,
        )
        if response:
            response_json = response.json()
            code = response_json.get("code")
            data = response_json.get("data")
            if code == 0:
                return data
            return None
