from mobio.libs.logging import MobioLogging
from src.libs.lark_docs_sdk.oapi.base import BaseApi


class DrivePermissionMember(BaseApi):
    base_url = "https://open.larksuite.com/open-apis/drive/v1/permissions/{token}/members"

    def __init__(self, token):
        super().__init__()
        self.base_url = self.base_url.format(token=token)

        self.url_add_permissions = self.base_url

    def add_permissions(self, type, user_open_id, perm, need_notification=False):
        url = self.url_add_permissions
        query_params = {
            "type": type,
            "need_notification": need_notification,
        }
        body = {
            "member_type": "openid",
            "member_id": user_open_id,
            "perm": perm
        }
        response = self.client_request.retry_a_post_request(
            url=url,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            params=query_params,
            payload=body,
            verify=False,
        )
        if response and response.status_code == 200:
            response_json = response.json()
            MobioLogging().info(f"url::{url}::response_json::{response_json}")
            return response_json.get("data")
        return {}
