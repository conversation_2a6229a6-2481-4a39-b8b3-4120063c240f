from src.libs.lark_docs_sdk.oapi.base import Base<PERSON>pi
from src.libs.lark_docs_sdk.oapi.drive_permission_member import DrivePermissionMember


class FolderApi(BaseApi):
    base_url = "https://open.larksuite.com/open-apis/drive/explorer/v2"

    # Const
    ADD_PERMISSIONS_TYPE = "folder"

    def __init__(self):
        super().__init__()
        self.url_get_root_folder_meta = f"{self.base_url}/root_folder/meta"
        self.url_create_folder = self.base_url + "/folder/"

    def get_root_folder_meta(self):
        response = self.client_request.retry_a_get_request(
            url=self.url_get_root_folder_meta,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            verify=False,
        )
        response_json = response.json()
        data = response_json.get("data")
        return data.get("token")

    def create_folder(self, folder_token, folder_name):
        body = {"title": folder_name}
        response = self.client_request.retry_a_post_request(
            url=self.url_create_folder + folder_token,
            headers={"Authorization": "Bearer " + self.get_tenant_access_token()},
            payload=body,
            verify=False,
        )
        response_json = response.json()
        if response.status_code == 200:
            return response_json.get("data")
        return False

    def add_permissions(self, folder_token, user_open_id, perm, need_notification=False):
        response = DrivePermissionMember(folder_token).add_permissions(
            type=self.ADD_PERMISSIONS_TYPE,
            user_open_id=user_open_id,
            perm=perm,
            need_notification=need_notification,
        )
        return response
