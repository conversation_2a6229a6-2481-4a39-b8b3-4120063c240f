from mobio.libs.logging import Mo<PERSON>Logging

from src.controllers.setting import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.libs.requests_retry import RequestR<PERSON>ry<PERSON>dapter
from src.redis.caching.base_caching import BaseCachingRedis


class BaseApi:
    def __init__(self):
        self.setting = SettingController().get_setting()
        self.app_id = self.setting.get("app_id")
        self.app_secret = self.setting.get("app_secret")
        self.domain_api = self.setting.get("domain_api")
        self.client_request = RequestRetryAdapter()
        self.logger = MobioLogging()

    def get_tenant_access_token(self):
        cache_key = f"tenant_access_token#{self.app_id}"
        cached_token = BaseCachingRedis().get_value_by_key_not_hash(cache_key)
        if cached_token:
            self.logger.info("Returning cached tenant access token")
            return cached_token.decode("utf-8") if isinstance(cached_token, bytes) else cached_token

        body = {"app_id": self.app_id, "app_secret": self.app_secret}
        api_url = f"{self.domain_api}/open-apis/auth/v3/tenant_access_token/internal"
        response = self.client_request.retry_a_post_request(
            url=api_url,
            headers={},
            payload=body,
            verify=False,
        )
        response_json = response.json()
        if response_json.get("code") == 0:
            tenant_access_token = response_json["tenant_access_token"]
            expire_time = response_json["expire"] - 1
            BaseCachingRedis().set_value_by_key_not_hash(cache_key, tenant_access_token, expiration=expire_time)
            self.logger.info(f"Cached tenant access token with expiration: {expire_time} seconds")
            return tenant_access_token
        else:
            self.logger.error(f"Failed to get tenant access token: {response_json.get('msg')}")
            raise Exception(f"Failed to get tenant access token: {response_json.get('msg')}")

    def get_tenant_access_token_without_cache(self):
        body = {"app_id": self.app_id, "app_secret": self.app_secret}
        api_url = f"{self.domain_api}/open-apis/auth/v3/tenant_access_token/internal"
        response = self.client_request.retry_a_post_request(
            url=api_url,
            headers={},
            payload=body,
            verify=False,
        )
        response_json = response.json()
        tenant_access_token = response_json["tenant_access_token"]
        return tenant_access_token


if __name__ == "__main__":
    token = BaseApi().get_tenant_access_token()
