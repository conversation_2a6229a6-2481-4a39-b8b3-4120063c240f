from src.libs.lark_docs_sdk.oapi.bitable import BitableApi


class BitableAppBuilder:

    class Permissions:
        VIEW = "view"
        EDIT = "edit"
        FULL_ACCESS = "full_access"

    def __init__(self, folder_token):
        self.lark_bitable_service = BitableApi()
        self.folder_token = folder_token

    def create_app(
        self,
        name,
    ):
        bitable_info = self.lark_bitable_service.create_app(name, self.folder_token)
        return bitable_info

    def copy_app_with_permissions(
        self, app_token, name, without_content, list_user_access_data, need_notification_add_perm=False
    ):
        """Copy app and grant perm

        Args:
            list_user_access_data (ListDict): list_user_access_data = [{
                "user_open_id": "",
                "perm": "view"
            }]

        Returns:
            bitable_app_info
        """
        bitable_info = self.lark_bitable_service.copy_app(app_token, name, self.folder_token, without_content)

        # Grant permission
        for user_data in list_user_access_data:
            oid = user_data["user_open_id"]
            perm = user_data["perm"]
            if perm not in [
                BitableAppBuilder.Permissions.EDIT,
                BitableAppBuilder.Permissions.VIEW,
                BitableAppBuilder.Permissions.FULL_ACCESS,
            ]:
                raise ValueError("Pem %s not allow" % perm)

            # Add perm for user
            self.lark_bitable_service.add_permissions(
                app_token=app_token,
                user_open_id=oid,
                perm=perm,
                need_notification=need_notification_add_perm
            )

        return bitable_info

    def create_app_with_permissions(self, name, list_user_access_data, need_notification_add_perm=False):
        """Copy app and grant perm

        Args:
            list_user_access_data (ListDict): list_user_access_data = [{
                "user_open_id": "",
                "perm": "view"
            }]

        Returns:
            bitable_app_info
        """
        bitable_info = self.lark_bitable_service.create_app(name, self.folder_token)
        app_token = bitable_info["app"]["app_token"]
        # Grant permission
        for user_data in list_user_access_data:
            oid = user_data["user_open_id"]
            perm = user_data["perm"]
            if perm not in [
                BitableAppBuilder.Permissions.EDIT,
                BitableAppBuilder.Permissions.VIEW,
                BitableAppBuilder.Permissions.FULL_ACCESS,
            ]:
                raise ValueError("perm %s not allow" % perm)

            # Add perm for user
            self.lark_bitable_service.add_permissions(
                app_token=app_token,
                user_open_id=oid,
                perm=perm,
                need_notification=need_notification_add_perm
            )
        return bitable_info


class BitableTableBuilder:

    class Permissions:
        VIEW = "view"
        EDIT = "edit"
        FULL_ACCESS = "full_access"

    def __init__(self, app_token):
        self.app_token = app_token
        self.lark_bitable_service = BitableApi()

    def create_table(self, table_name, default_view_name, fields):
        table_data = {
            "name": table_name,
            "default_view_name": default_view_name,
            "fields": fields,
        }
        create_table_response = self.lark_bitable_service.create_table(self.app_token, table_data)
        return create_table_response

    def get_list_tables(self, per_page):
        list_bitable_response = self.lark_bitable_service.get_list_table(self.app_token, per_page)
        return list_bitable_response

    # def update_bitable(self, table_id, data):
    #     update_response = self.lark_bitable_service.update_table(self.app_token, table_id, data)
    #     return update_response

    def delete_table(self, table_id):
        delete_table_response = self.lark_bitable_service.delete_table(self.app_token, table_id=table_id)
        return delete_table_response


class BitableTableRecordsBuilder:

    def __init__(self, app_token, table_id):
        self.table_id = table_id
        self.app_token = app_token
        self.lark_bitable_service = BitableApi()

    def clear_all_records(self):
        records = self.lark_bitable_service.get_list_record(app_token=self.app_token, table_id=self.table_id)
        if records and records.get("items"):
            record_ids = [r["record_id"] for r in records["items"]]
            self.lark_bitable_service.delete_records(
                app_token=self.app_token, table_id=self.table_id, records_ids=record_ids
            )

    def create_batch_record(self, records):
        res = self.lark_bitable_service.create_records(
            app_token=self.app_token, table_id=self.table_id, records=records
        )
        return res