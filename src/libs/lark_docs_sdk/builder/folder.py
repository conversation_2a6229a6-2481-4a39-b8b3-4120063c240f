# Lark docs: https://open.larksuite.com/document/server-docs/docs/drive-v1/permission/permission-member/create

from src.libs.lark_docs_sdk.oapi.folder import FolderApi


class FolderBuilder:

    class Permissions:
        VIEW = "view"
        EDIT = "edit"
        FULL_ACCESS = "full_access"

    def __init__(self):
        self.lark_folder_service = FolderApi()
        self.folder_root_token = self.lark_folder_service.get_root_folder_meta()

    def create_folder_with_permissions(self, folder_name, list_user_access_data, need_notification=False):
        """Create folder and grant perm

        Args:
            list_user_access_data (ListDict): list_user_access_data = [{
                "user_open_id": "",
                "perm": "view"
            }]

        Returns:
            Folder info
        """

        # Create folder
        folder_info = self.lark_folder_service.create_folder(self.folder_root_token, folder_name)
        folder_token = folder_info["token"]

        # Add permission
        for user_data in list_user_access_data:
            oid = user_data["user_open_id"]
            perm = user_data["perm"]
            if perm not in [
                FolderBuilder.Permissions.EDIT,
                FolderBuilder.Permissions.VIEW,
                FolderBuilder.Permissions.FULL_ACCESS,
            ]:
                raise ValueError("Pem %s not allow" % perm)
            # Add perm for user
            self.lark_folder_service.add_permissions(
                folder_token=folder_token,
                user_open_id=oid,
                perm=perm,
                need_notification=need_notification,
            )

        return folder_info
