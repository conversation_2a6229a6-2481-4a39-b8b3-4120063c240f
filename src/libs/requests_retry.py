#!/usr/bin/python
# -*- coding: utf8 -*-


import requests
from mobio.libs.logging import Mo<PERSON>Logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class RequestRetryAdapter(object):

    def __init__(self):
        self.logging = MobioLogging()

    @staticmethod
    def requests_retry_session(
        retries=3, backoff_factor=0.3, status_forcelist=(500, 501, 502, 503, 429, 504, 400, 401, 404), session=None
    ):
        session = session or requests.Session()
        retry = Retry(
            total=retries,
            read=retries,
            connect=retries,
            backoff_factor=backoff_factor,
            status_forcelist=status_forcelist,
        )
        adapter = HTTPAdapter(max_retries=retry)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    @classmethod
    def _convert_curl_from_request(cls, response):
        command = "curl -X {method} -H {headers} -d '{data}' '{uri}'"
        method = response.request.method
        uri = response.request.url
        data = response.request.body
        headers = ['"{0}: {1}"'.format(k, v) for k, v in response.request.headers.items()]
        headers = " -H ".join(headers)
        return command.format(method=method, headers=headers, data=data, uri=uri)

    def retry_a_post_request(self, url, headers, payload, params=None, times=3, timeout=10, verify=None):
        curl = None
        try:

            self.logging.info("RequestRetryAdapter::retry_a_post_request: payload: {}".format(payload))
            response = self.requests_retry_session(retries=times).post(
                url, params=params, json=payload, headers=headers, timeout=timeout
            )
            response.raise_for_status()
            self.logging.info("RequestRetryAdapter::retry_a_post_request: Request Info: curl-done :: {}".format(curl))
            return response
        except Exception as error:
            self.logging.error(
                "RequestRetryAdapter::retry_a_post_request: Request Info: {} ==== Error: {}".format(payload, error)
            )
            return None

    def retry_a_get_request(self, url, headers, params={}, times=5, timeout=3, verify=None):
        try:
            response = self.requests_retry_session(retries=times).get(
                url, params=params, headers=headers, timeout=timeout, verify=verify
            )
            response.raise_for_status()
            return response
        except Exception as error:
            log = error
            self.logging.error(
                "RequestRetryAdapter::retry_a_get_request: Request Info: {} ==== Error: {}".format(params, error)
            )
            return None

    def retry_a_delete_request(self, url, headers, params=None, times=3, payload=None, save_log_db=True):
        try:
            response = self.requests_retry_session(retries=times).delete(
                url, params=params, headers=headers, json=payload
            )
            response.raise_for_status()
            return response
        except Exception as error:
            self.logging.error(
                "RequestRetryAdapter::retry_a_delete_request: Request Info: {} ==== Error: {}".format(params, error)
            )
            return None

    def retry_a_put_request(self, url, headers, payload=None, times=3, save_log_db=True):
        try:
            response = self.requests_retry_session(retries=times).put(url, json=payload, headers=headers)
            self.logging.info("response_text: %s" % response.text)
            response.raise_for_status()
            return response
        except Exception as error:
            # curl = self._convert_curl_from_request(response)
            self.logging.error(
                "RequestRetryAdapter::retry_a_put_request: Request Info: {} ==== Error: {}".format(payload, error)
            )
            return None
        return response
