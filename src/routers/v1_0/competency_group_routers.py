from fastapi import APIRouter, Depends
from starlette.requests import Request

from src.auth.authentication import check_authentication
from src.controllers.competency_group_controller import CompetencyGroupController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponsePagingNumberSchema, BaseResponseSchema
from src.schemas.pydantic.competency_group_schema import (
    AddCompetencyGroupRequestSchema,
    UpdateCompetencyGroupRequestSchema,
)

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    path=URI.COMPETENCY_GROUP.COMPETENCY_GROUP,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def add_competency_group(request: Request, payload: AddCompetencyGroupRequestSchema):
    return await CompetencyGroupController().add_competency_group(request, payload)


@routers.get(
    path=URI.COMPETENCY_GROUP.COMPETENCY_GROUP,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponsePagingNumberSchema,
)
async def competency_groups(request: Request):
    return await CompetencyGroupController().get_competency_groups(request)


@routers.put(
    path=URI.COMPETENCY_GROUP.DETAIL_COMPETENCY_GROUP,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def update_competency_group(request: Request, competency_group_id, payload: UpdateCompetencyGroupRequestSchema):
    return await CompetencyGroupController().update_competency_group(request, competency_group_id, payload)


@routers.delete(
    path=URI.COMPETENCY_GROUP.DETAIL_COMPETENCY_GROUP,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def delete_competency_group(request: Request, competency_group_id):
    return await CompetencyGroupController().delete_competency_group(request, competency_group_id)


@routers.get(
    path=URI.COMPETENCY_GROUP.GET_DEFAULT_APPLY,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def get_default_apply(request: Request):
    return await CompetencyGroupController().get_default_apply(request)
