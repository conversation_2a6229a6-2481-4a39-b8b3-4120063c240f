from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from starlette.requests import Request

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.competency_framework_controller import (
    CompetencyFrameworkController,
)
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponsePagingNumberSchema, BaseResponseSchema
from src.schemas.pydantic.competency_framework_schema import (
    AddCompetencyFrameworkRequestSchema,
    ChangeStatusRequestSchema,
    CompetencyFrameworkFiltersRequestSchema,
    SaveDraftCompetencyFrameworkActiveRequestSchema,
    SaveDraftCompetencyFrameworkRequestSchema,
    UpdateCompetencyFrameworkRequestSchema,
)

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    path=URI.COMPETENCY_FRAMEWORK.COMPETENCY_FRAMEWORK,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def add_competency_framework(
    request: Request, payload: AddCompetencyFrameworkRequestSchema, session: Session = Depends(get_db)
):
    return await CompetencyFrameworkController().add_competency_framework(request, payload, session)


@routers.patch(
    path=URI.COMPETENCY_FRAMEWORK.DETAIL_COMPETENCY_FRAMEWORK,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def update_competency_framework(
    request: Request,
    competency_framework_id,
    payload: UpdateCompetencyFrameworkRequestSchema,
    session: Session = Depends(get_db),
):
    return await CompetencyFrameworkController().update_competency_framework(
        request, competency_framework_id, payload, session
    )


@routers.get(
    path=URI.COMPETENCY_FRAMEWORK.DETAIL_COMPETENCY_FRAMEWORK,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def detail_competency_framework(request: Request, competency_framework_id):
    return await CompetencyFrameworkController().detail_competency_framework(request, competency_framework_id)


@routers.delete(
    path=URI.COMPETENCY_FRAMEWORK.DETAIL_COMPETENCY_FRAMEWORK,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def delete_competency_framework(request: Request, competency_framework_id):
    return await CompetencyFrameworkController().delete_competency_framework(request, competency_framework_id)


@routers.post(
    path=URI.COMPETENCY_FRAMEWORK.FILTERS_COMPETENCY_FRAMEWORK,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponsePagingNumberSchema,
)
async def lst_competency_framework(
    request: Request, filters: CompetencyFrameworkFiltersRequestSchema, session: Session = Depends(get_db)
):
    return await CompetencyFrameworkController().lst_competency_framework(request, filters, session)


@routers.put(
    path=URI.COMPETENCY_FRAMEWORK.SAVE_DRAFT,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def save_draft_competency_framework(
    request: Request, payload: SaveDraftCompetencyFrameworkRequestSchema, session: Session = Depends(get_db)
):
    return await CompetencyFrameworkController().save_draft_competency_framework(request, payload, session)


@routers.post(
    path=URI.COMPETENCY_FRAMEWORK.SAVE_DRAFT_COMPETENCY_FRAMEWORK_ACTIVE,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def save_draft_competency_framework_active(
    request: Request,
    competency_framework_id: str,
    payload: SaveDraftCompetencyFrameworkActiveRequestSchema,
    session: Session = Depends(get_db),
):
    return await CompetencyFrameworkController().save_draft_competency_framework_active(
        request, competency_framework_id, payload, session
    )


@routers.post(
    path=URI.COMPETENCY_FRAMEWORK.GET_STATUS,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def get_status_competency_framework(
    request: Request, filters: CompetencyFrameworkFiltersRequestSchema, session: Session = Depends(get_db)
):
    return await CompetencyFrameworkController().get_status_competency_framework(request, filters, session)


@routers.patch(
    path=URI.COMPETENCY_FRAMEWORK.CHANGE_STATUS,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def change_status_competency_framework(
    competency_framework_id, request: Request, payload: ChangeStatusRequestSchema, session: Session = Depends(get_db)
):
    return await CompetencyFrameworkController().change_status_competency_framework(
        competency_framework_id, request, payload, session
    )
