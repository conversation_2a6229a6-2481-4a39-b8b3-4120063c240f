from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.user_controller import UserController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema
from src.schemas.pydantic.user_schemas import UserFiltersRequestSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    path=URI.ORG_CHART.USERS,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def get_users(request: Request, filters: UserFiltersRequestSchema, session: Session = Depends(get_db)):
    return await UserController(request, session).get_users_for_org_chart(filters)


@routers.get(
    path=URI.ORG_CHART.DETAIL_USER,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def get_users(request: Request, user_id: str, session: Session = Depends(get_db)):
    return await UserController(request, session).get_detail_user(request, user_id, session, "org-chart")
