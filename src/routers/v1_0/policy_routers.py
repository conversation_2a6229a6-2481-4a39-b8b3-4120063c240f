#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 04/01/2024
"""
from typing import Dict

from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.admin_auth import admin_auth
from src.controllers.policy_controller import PolicyController
from src.middleware.handle_response import CustomHandleResponseRoute

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(
    path="/policies",
    dependencies=[Depends(admin_auth)],
)
async def get_policies(request: Request, session: Session = Depends(get_db)):
    """Get all policies"""
    return await PolicyController().get_policies(request, session)


@routers.get(
    path="/policies/{policy_id}",
    dependencies=[Depends(admin_auth)],
)
async def get_policy(request: Request, policy_id: str, session: Session = Depends(get_db)):
    """Get a policy by ID"""
    return await <PERSON>Controller().get_policy(request, policy_id, session)


@routers.post(
    path="/policies",
    dependencies=[Depends(admin_auth)],
)
async def create_policy(request: Request, payload: Dict, session: Session = Depends(get_db)):
    """Create a new policy"""
    return await PolicyController().create_policy(request, payload, session)


@routers.put(
    path="/policies/{policy_id}",
    dependencies=[Depends(admin_auth)],
)
async def update_policy(request: Request, policy_id: str, payload: Dict, session: Session = Depends(get_db)):
    """Update an existing policy"""
    return await PolicyController().update_policy(request, policy_id, payload, session)


@routers.delete(
    path="/policies/{policy_id}",
    dependencies=[Depends(admin_auth)],
)
async def delete_policy(request: Request, policy_id: str, session: Session = Depends(get_db)):
    """Delete a policy"""
    return await PolicyController().delete_policy(request, policy_id, session)


@routers.post(
    path="/users/{user_id}/policies",
    dependencies=[Depends(admin_auth)],
)
async def attach_policy_to_user(request: Request, user_id: str, payload: Dict, session: Session = Depends(get_db)):
    """Attach a policy to a user"""
    return await PolicyController().attach_policy_to_user(request, user_id, payload, session)


@routers.post(
    path="/users/{user_id}/policies/detach",
    dependencies=[Depends(admin_auth)],
)
async def detach_policy_from_user(request: Request, user_id: str, payload: Dict, session: Session = Depends(get_db)):
    """Detach a policy from a user"""
    return await PolicyController().detach_policy_from_user(request, user_id, payload, session)


@routers.post(
    path="/roles/{role_id}/policies",
    dependencies=[Depends(admin_auth)],
)
async def attach_policy_to_role(request: Request, role_id: str, payload: Dict, session: Session = Depends(get_db)):
    """Attach a policy to a role"""
    return await PolicyController().attach_policy_to_role(request, role_id, payload, session)


@routers.post(
    path="/roles/{role_id}/policies/detach",
    dependencies=[Depends(admin_auth)],
)
async def detach_policy_from_role(request: Request, role_id: str, payload: Dict, session: Session = Depends(get_db)):
    """Detach a policy from a role"""
    return await PolicyController().detach_policy_from_role(request, role_id, payload, session)


@routers.post(
    path="/users/{user_id}/evaluate-policy",
    dependencies=[Depends(admin_auth)],
)
async def evaluate_policy(request: Request, user_id: str, payload: Dict, session: Session = Depends(get_db)):
    """Evaluate if a user has permission for an action on a resource"""
    return await PolicyController().evaluate_policy(request, user_id, payload, session)


@routers.post(
    path="/policies/create-defaults",
    dependencies=[Depends(admin_auth)],
)
async def create_default_policies(request: Request, session: Session = Depends(get_db)):
    """Create all default policies in the system"""
    return await PolicyController().create_default_policies(request, session)
