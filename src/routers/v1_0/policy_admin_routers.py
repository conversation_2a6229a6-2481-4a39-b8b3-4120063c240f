#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: Current Date
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.admin_auth import admin_auth
from src.controllers.policy_admin_controller import PolicyAdminController
from src.middleware.handle_response import CustomHandleResponseRoute

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(
    path="/policy-admin",
)
async def policy_admin_ui(request: Request):
    """Serve the policy admin UI homepage"""
    return await PolicyAdminController().serve_admin_ui(request)


@routers.get(
    path="/policy-admin/dashboard",
    dependencies=[Depends(admin_auth)],
)
async def policy_admin_dashboard(request: Request):
    """Serve the policy admin dashboard page"""
    return await PolicyAdminController().serve_dashboard(request)


@routers.get(
    path="/policy-admin/{file_path:path}",
)
async def policy_admin_static(request: Request, file_path: str):
    """Serve static files for the policy admin UI"""
    return await PolicyAdminController().serve_static_file(request, file_path)


@routers.get(
    path="/policy-admin-api/users",
    dependencies=[],
)
async def get_users_for_admin(request: Request, session: Session = Depends(get_db)):
    """Get all users for the policy admin UI"""
    return await PolicyAdminController().get_users(request, session)


@routers.get(
    path="/policy-admin-api/users/{user_id}",
    dependencies=[],
)
async def get_user_for_admin(request: Request, user_id: str, session: Session = Depends(get_db)):
    """Get user details for the policy admin UI"""
    return await PolicyAdminController().get_user(request, user_id, session)


@routers.get(
    path="/policy-admin-api/roles",
    dependencies=[],
)
async def get_roles_for_admin(request: Request, session: Session = Depends(get_db)):
    """Get all roles for the policy admin UI"""
    return await PolicyAdminController().get_roles(request, session)


@routers.get(
    path="/policy-admin-api/roles/{role_id}",
    dependencies=[],
)
async def get_role_for_admin(request: Request, role_id: str, session: Session = Depends(get_db)):
    """Get role details for the policy admin UI"""
    return await PolicyAdminController().get_role(request, role_id, session)
