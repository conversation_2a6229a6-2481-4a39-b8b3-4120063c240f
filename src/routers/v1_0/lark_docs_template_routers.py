from fastapi import APIRouter, Depends, Request

from src.auth.authentication import check_authentication
from src.controllers.lark_docs_template_controller import LarkDocsTemplateController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema
from src.schemas.pydantic.lark_docs_template_schema import (
    CreateLarkDocsTemplateSchema,
    ListLarkDocsTemplateResponseSchema,
)

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    path=URI.LarkDocsTemplate.DOCS_TEMPLATE,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def create_template(request: Request, body: CreateLarkDocsTemplateSchema):
    return await LarkDocsTemplateController(request).create_template(body=body)


@routers.get(
    path=URI.LarkDocsTemplate.DOCS_TEMPLATE,
    dependencies=[Depends(check_authentication)],
    response_model=ListLarkDocsTemplateResponseSchema,
)
async def list_template(request: Request):
    return await LarkDocsTemplateController(request).list_template()
