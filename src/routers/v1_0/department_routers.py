from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.department_controller import DepartmentController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponsePagingNumberSchema, BaseResponseSchema
from src.schemas.pydantic.department_schemas import ChangeOrderDepartmentsRequestSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(
    path=URI.DEPARTMENT.DEPARTMENT,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponsePagingNumberSchema,
)
async def get_departments(request: Request, session: Session = Depends(get_db)):
    return await DepartmentController().get_departments(request, session)


@routers.get(
    path=URI.ORG_CHART.DEPARTMENTS,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponsePagingNumberSchema,
)
async def get_departments(request: Request, session: Session = Depends(get_db)):
    return await DepartmentController().get_departments_for_org_chart(request, session)


@routers.patch(
    path=URI.DEPARTMENT.CHANGE_ORDER,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def get_department_by_id(
    request: Request, payload: ChangeOrderDepartmentsRequestSchema, session: Session = Depends(get_db)
):
    return await DepartmentController().update_order_for_departments(request, payload, session)


@routers.get(
    path=URI.DEPARTMENT.DEPARTMENTS_OWNERS,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def get_department_owners(request: Request, session: Session = Depends(get_db)):
    return await DepartmentController().get_department_owners(request, session)
