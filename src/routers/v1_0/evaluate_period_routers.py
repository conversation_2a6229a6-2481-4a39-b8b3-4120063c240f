from fastapi import APIRouter, Depends, Request

from src.auth.authentication import check_authentication
from src.controllers.evaluate_period_controller import EvaluatePeriodController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema
from src.schemas.pydantic.evaluate_period_schemas import (
    AddEvaluatePeriodTemplateRequestSchema,
)

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    URI.EVALUATE_PERIOD.EVALUATE_PERIOD_TEMPLATE,
    dependencies=[],
    response_model=BaseResponseSchema,
)
async def add_evaluate_period_template(request: Request, payload: AddEvaluatePeriodTemplateRequestSchema):
    return await EvaluatePeriodController().add_evaluate_period_template(request, payload)


@routers.get(
    URI.EVALUATE_PERIOD.EVALUATE_PERIOD, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema
)
async def get_evaluate_periods(request: Request):
    return await EvaluatePeriodController().get_evaluate_periods(request)
