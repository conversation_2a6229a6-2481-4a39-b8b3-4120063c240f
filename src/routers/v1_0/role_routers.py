from fastapi import APIRouter, Depends, Request

from src.auth.authentication import check_authentication
from src.controllers.role_controller import <PERSON><PERSON><PERSON>roller
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(URI.ROLE.ROLE, response_model=BaseResponseSchema, dependencies=[Depends(check_authentication)])
async def get_roles(request: Request):
    return await RoleController().get_roles(request)
