from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.job_title_controller import JobTitleController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema
from src.schemas.pydantic.job_title_schemas import AddJobTitleLevelSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(
    path=URI.JOB_TITLE.JOB_TITLE_IN_DEPARTMENT,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def get_lst_job_title(request: Request, department_id, session: Session = Depends(get_db)):
    return await JobTitleController().get_lst_job_title(request, department_id, session)


@routers.put(
    path=URI.JOB_TITLE.JOB_TITLE_LEVEL, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema
)
async def upsert_job_title_levels(
    request: Request, department_id, payload: AddJobTitleLevelSchema, session: Session = Depends(get_db)
):
    return await JobTitleController().upsert_job_title_levels(request, department_id, payload, session)


@routers.delete(
    path=URI.JOB_TITLE.DETAIL_JOB_TITLE_LEVEL,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def delete_job_titles_levels(request: Request, department_id, job_title_id, session: Session = Depends(get_db)):
    return await JobTitleController().delete_job_titles_levels(request, department_id, job_title_id, session)
