from fastapi import <PERSON><PERSON>out<PERSON>, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.competency_controller import CompetencyFrameworkController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponsePagingNumberSchema, BaseResponseSchema
from src.schemas.pydantic.competency_schemas import (
    AddCompetencyRequestSchema,
    DeleteCompetencyRequestSchema,
    UpdateCompetencyRequestSchema,
    UpdateGroupsRequestSchema,
)

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    path=URI.COMPETENCY.COMPETENCY,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def add_competency(request: Request, payload: AddCompetencyRequestSchema, session: Session = Depends(get_db)):
    return await CompetencyFrameworkController().add_competency(request, payload, session)


@routers.get(
    path=URI.COMPETENCY.DETAIL_COMPETENCY,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def detail_competency(request: Request, competency_id):
    return await CompetencyFrameworkController().detail_competency(request, competency_id)


@routers.get(
    path=URI.COMPETENCY.COMPETENCY,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponsePagingNumberSchema,
)
async def lst_competency(request: Request):
    return await CompetencyFrameworkController().lst_competency(request)


@routers.put(
    path=URI.COMPETENCY.DETAIL_COMPETENCY,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def update_competency(
    request: Request, competency_id, payload: UpdateCompetencyRequestSchema, session: Session = Depends(get_db)
):
    return await CompetencyFrameworkController().update_competency(request, competency_id, payload, session)


@routers.post(
    path=URI.COMPETENCY.DELETE_COMPETENCY,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def delete_competency(request: Request, payload: DeleteCompetencyRequestSchema):
    return await CompetencyFrameworkController().delete_competency(request, payload)


@routers.post(
    path=URI.COMPETENCY.UPDATE_GROUPS,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponseSchema,
)
async def update_groups(request: Request, payload: UpdateGroupsRequestSchema):
    return await CompetencyFrameworkController().update_groups(request, payload)
