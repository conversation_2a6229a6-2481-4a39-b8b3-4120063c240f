from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication, check_leader_admin_role
from src.auth.permissions import can_list_users, can_update_user, can_view_user
from src.controllers.user_controller import UserController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponsePagingNumberSchema, BaseResponseSchema
from src.schemas.pydantic.role_schemas import AddRoleRequestSchema
from src.schemas.pydantic.user_schemas import (
    UpdateJobTitleLevelUsersSchema,
    UserFiltersRequestSchema,
)

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(
    path=URI.USER.USERS,
    dependencies=[Depends(check_authentication), Depends(can_list_users)],
    response_model=BaseResponsePagingNumberSchema,
)
async def get_users(request: Request, filters: UserFiltersRequestSchema, session: Session = Depends(get_db)):
    return await UserController().get_users(request, filters, session)


@routers.get(
    path=URI.USER.DETAIL_USER,
    dependencies=[Depends(check_authentication), Depends(can_view_user)],
    response_model=BaseResponseSchema,
)
async def get_detail_user(request: Request, user_id, session: Session = Depends(get_db)):
    return await UserController().get_detail_user(request, user_id, session)


@routers.patch(
    path=URI.USER.CHANGE_JOB_TITLE_LEVEL,
    dependencies=[Depends(check_authentication), Depends(check_leader_admin_role)],
    response_model=BaseResponseSchema,
)
async def change_job_title_level_of_users(
    request: Request, payload: UpdateJobTitleLevelUsersSchema, session: Session = Depends(get_db)
):
    return await UserController().change_job_title_level_of_users(request, payload, session)


@routers.patch(
    URI.USER.CHANGE_ROLES,
    response_model=BaseResponseSchema,
    dependencies=[Depends(check_authentication), Depends(can_update_user())],
)
async def add_role_for_user(request: Request, payload: AddRoleRequestSchema, session: Session = Depends(get_db)):
    return await UserController().add_roles_for_user(request, payload, session)


@routers.post(
    URI.PUSH_NOTIFICATION.TRIGGER_PUSH_NOTIFICATION,
    response_model=BaseResponseSchema,
    dependencies=[],
)
async def trigger_push_notification(request: Request):
    return await UserController().trigger_push_notification(request)
