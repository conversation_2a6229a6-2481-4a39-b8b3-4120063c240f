from fastapi import APIRouter, Depends, Request

from src.auth.authentication import check_authentication
from src.controllers.task_performance_controller import TaskPerformanceController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(
    URI.TASK_PERFORMANCE.TASK_PERFORMANCE_BY_USER,
    response_model=BaseResponseSchema,
    dependencies=[Depends(check_authentication)],
)
async def get_task_performance_by_user(request: Request, user_id: str):
    return await TaskPerformanceController(request).get_task_performance_by_user(user_id, request)


@routers.post(
    URI.TASK_PERFORMANCE.AGGREGATE_TASK_PERFORMANCE,
    response_model=BaseResponseSchema,
)
async def aggregate_task_performance(request: Request):
    return await TaskPerformanceController(request).aggregate_task_performance(request)
