from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.evaluate_controller import EvaluateController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponsePagingNumberSchema, BaseResponseSchema
from src.schemas.pydantic.evaluate_schemas import (
    AddEvaluateRequestSchema,
    EvaluateFiltersManagedTeamRequestSchema,
    EvaluateFiltersRequestSchema,
    ListEvaluateByUserIdRequestSchema,
    SaveDraftEvaluateRequestSchema,
    UpdateEvaluateRequestSchema,
)

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.post(URI.EVALUATE.EVALUATE, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema)
async def add_evaluate(request: Request, payload: AddEvaluateRequestSchema, session: Session = Depends(get_db)):
    return await EvaluateController().add_evaluate(request, payload, session)


@routers.patch(
    URI.EVALUATE.DETAIL_EVALUATE, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema
)
async def update_evaluate(
    request: Request, evaluate_id, payload: UpdateEvaluateRequestSchema, session: Session = Depends(get_db)
):
    return await EvaluateController().update_evaluate(request, evaluate_id, payload, session)


@routers.get(URI.EVALUATE.CURRENT_USER, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema)
async def get_evaluate(request: Request, session: Session = Depends(get_db)):
    return await EvaluateController().get_evaluate_for_current_user(request, session)


@routers.post(URI.EVALUATE.FILTERS, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema)
async def get_evaluates(request: Request, filters: EvaluateFiltersRequestSchema, session: Session = Depends(get_db)):
    return await EvaluateController().get_evaluates(request, filters, session)


@routers.post(
    URI.EVALUATE.FILTERS_MANAGED_TEAM,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponsePagingNumberSchema,
)
async def get_evaluates_managed_team(
    request: Request, filters: EvaluateFiltersManagedTeamRequestSchema, session: Session = Depends(get_db)
):
    return await EvaluateController().get_evaluates_managed_team(request, filters, session)


@routers.post(
    URI.EVALUATE.LIST_EVALUATE_BY_USER_ID,
    dependencies=[Depends(check_authentication)],
    response_model=BaseResponsePagingNumberSchema,
)
async def get_evaluates_by_user_id(
    request: Request, user_id, filters: ListEvaluateByUserIdRequestSchema, session: Session = Depends(get_db)
):
    return await EvaluateController().list_evaluate_by_user_id(request, user_id, filters, session)


@routers.get(URI.EVALUATE.GET_STATUS, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema)
async def get_status_evaluate(request: Request, session: Session = Depends(get_db)):
    return await EvaluateController().get_status_evaluate(request, session)


@routers.put(URI.EVALUATE.SAVE_DRAFT, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema)
async def save_draft(request: Request, payload: SaveDraftEvaluateRequestSchema, session: Session = Depends(get_db)):
    return await EvaluateController().save_draft(request, payload, session)


@routers.get(
    URI.EVALUATE.DETAIL_EVALUATE, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema
)
async def detail_evaluate(request: Request, evaluate_id, session: Session = Depends(get_db)):
    return await EvaluateController().detail_evaluate(request, evaluate_id, session)


@routers.get(URI.EVALUATE.EXPORT_EVALUATE, dependencies=[Depends(check_authentication)])
async def export_evaluate(request: Request, evaluate_id, session: Session = Depends(get_db)):
    return await EvaluateController().export_evaluate(request, evaluate_id, session)
