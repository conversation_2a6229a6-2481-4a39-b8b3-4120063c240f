#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 10/01/2024
"""

# from typing import Annotated, Any, Dict

# from fastapi import APIRouter, Depends
# from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
# from fastapi.openapi.utils import get_openapi
# from fastapi.responses import HTMLResponse

# from configs import ApplicationConfig
# from src.routers.uri import URI
# from src.utils.dependency import basic_auth

# routers = APIRouter()


# # DEFAULT ROUTER
# @routers.get(URI.INDEX, summary="default", include_in_schema=False)
# async def default(user: Annotated[str, Depends(basic_auth)]):
#     data = {
#         "docs": URI.DOCUMENTATION.DOCS,
#         "redoc": URI.DOCUMENTATION.REDOC,
#         "postman_collection_json": URI.DOCUMENTATION.OPENAPI_JSON,
#     }
#     return data


# # DOCUMENTATION ROUTER
# @routers.get(
#     URI.DOCUMENTATION.OPENAPI_JSON,
#     include_in_schema=False,
# )
# async def openapi(user: Annotated[str, Depends(basic_auth)]) -> Dict[str, Any]:
#     from app_web import app

#     return get_openapi(title=app.title, version=app.version, routes=app.routes)


# @routers.get(URI.DOCUMENTATION.DOCS, include_in_schema=False)
# async def get_swagger_documentation(
#     user: Annotated[str, Depends(basic_auth)],
# ) -> HTMLResponse:
#     from app_web import app

#     return get_swagger_ui_html(
#         title=app.title,
#         openapi_url="/api/openapi.json",
#         swagger_favicon_url=ApplicationConfig.FAVICON_URL,
#     )


# @routers.get(
#     URI.DOCUMENTATION.REDOC,
#     include_in_schema=False,
# )
# async def get_redoc(user: Annotated[str, Depends(basic_auth)]) -> HTMLResponse:
#     from app_web import app

#     return get_redoc_html(
#         title=app.title,
#         openapi_url=URI.DOCUMENTATION.OPENAPI_JSON,
#         redoc_favicon_url=ApplicationConfig.FAVICON_URL,
#     )

from fastapi import APIRouter

routers = APIRouter()
