#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: tungdd
Date created: 04/01/2024
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.auth_controller import AuthController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(URI.LOGIN.LOGIN, response_model=BaseResponseSchema)
async def login(request: Request):
    return await AuthController().login(request)


@routers.post(URI.LOGIN.LOGOUT, response_model=BaseResponseSchema)
async def logout(request: Request):
    return await AuthController().logout(request)


@routers.get(URI.LOGIN.LOGIN_CALLBACK, response_model=BaseResponseSchema)
async def login_callback(request: Request, session: Session = Depends(get_db)):
    return await AuthController().callback_refactor(request, session)


@routers.get(URI.ROLE.ROLE, response_model=BaseResponseSchema, dependencies=[Depends(check_authentication)])
async def get_roles(request: Request, session: Session = Depends(get_db)):
    return await AuthController().get_roles(request)
