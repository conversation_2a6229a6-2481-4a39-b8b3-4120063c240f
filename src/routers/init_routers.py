#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: tungdd
Date created: 04/01/2024
"""
from fastapi import APIRouter

from src.routers.base.base_routers import routers as base_routers
from src.routers.login import sso_router
from src.routers.v1_0 import (
    company_routers,
    competency_framework_routers,
    competency_group_routers,
    competency_routers,
    department_routers,
    evaluate_period_routers,
    evaluate_routers,
    job_title_routers,
    lark_docs_template_routers,
    org_chart_routers,
    ping_routers,
    policy_admin_routers,
    policy_routers,
    role_routers,
    task_performance_routers,
    user_routers,
)

# ================================================== Base ================================================== #
base_api = APIRouter(prefix="/api")
base_api.include_router(router=base_routers, tags=["Base"])
base_api.include_router(router=sso_router.routers, tags=["Login"])


# ================================================== V1.0 ================================================== #
v1_0_prefix = "/api/v1.0"
api_v1_0 = APIRouter(prefix=v1_0_prefix)

api_v1_0.include_router(ping_routers.routers, tags=["Ping"])

# api_v1_0.include_router(branch_routers.routers, tags=["Branch"])
api_v1_0.include_router(user_routers.routers, tags=["User"])
api_v1_0.include_router(org_chart_routers.routers, tags=["Org-Chart"])
api_v1_0.include_router(role_routers.routers, tags=["Role"])
api_v1_0.include_router(policy_routers.routers, tags=["Policy"])
api_v1_0.include_router(policy_admin_routers.routers, tags=["Policy-Admin"])
api_v1_0.include_router(competency_routers.routers, tags=["Competency"])
api_v1_0.include_router(competency_group_routers.routers, tags=["Competency-Group"])
api_v1_0.include_router(department_routers.routers, tags=["Department"])
api_v1_0.include_router(job_title_routers.routers, tags=["Job-Title"])
api_v1_0.include_router(competency_framework_routers.routers, tags=["Competency-Framework"])
api_v1_0.include_router(company_routers.routers, tags=["Company"])
api_v1_0.include_router(lark_docs_template_routers.routers, tags=["Bitable-Template"])
api_v1_0.include_router(evaluate_period_routers.routers, tags=["Evaluate-Period"])
api_v1_0.include_router(evaluate_routers.routers, tags=["Evaluate"])
api_v1_0.include_router(task_performance_routers.routers, tags=["Task-Performance"])
