#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: tungdd
Date created: 04/01/2024
"""


class URI:
    INDEX = "/"
    PING = "/ping"

    class DOCUMENTATION:
        DOCS = "/docs"
        REDOC = "/redoc"
        OPENAPI_JSON = "/openapi.json"

    class LOGIN:
        LOGIN = "/login"
        LOGOUT = "/logout"
        LOGIN_CALLBACK = "/login/callback"

    class USER:
        DETAIL_USER = "/users/{user_id}"
        DETAIL_USER_EMAIL = "/users/{email}"
        USERS = "/users"
        JOB_TITLE_USER = "/users/job-title"
        USERS_IN_JOB_TITLE_DISPLAY = "/users/job-title-levels"
        CHANGE_JOB_TITLE_LEVEL = "/users/actions/change-job-title-levels"
        CHANGE_ROLES = "/users/actions/change-roles"

    class BRANCH:
        DETAIL_BRANCH = "/branches/{branch_id}"
        ADD_BRANCH = "/branches"
        LIST_BRANCH = "/branches"

    class COMPETENCY:
        COMPETENCY = "/competencies"
        DETAIL_COMPETENCY = "/competencies/{competency_id}"
        DELETE_COMPETENCY = "/competencies/actions/delete"
        UPDATE_GROUPS = "/competencies/actions/update-groups"

    class JOB_TITLE:
        JOB_TITLE_IN_DEPARTMENT = "/departments/{department_id}/job-titles"
        JOB_TITLE_LEVEL = "/departments/{department_id}/job-titles/levels"
        DETAIL_JOB_TITLE_LEVEL = "/departments/{department_id}/job-titles/{job_title_id}/levels"

    class ROLE:
        ROLE = "/roles"

    class COMPETENCY_GROUP:
        COMPETENCY_GROUP = "/competency-groups"
        GET_DEFAULT_APPLY = "/competency-groups/actions/get-default-apply"
        DETAIL_COMPETENCY_GROUP = "/competency-groups/{competency_group_id}"

    class DEPARTMENT:
        DEPARTMENT = "/departments"
        DEPARTMENTS_OWNERS = "/departments/owners"

        DETAIL_DEPARTMENT = "/departments/{department_id}"
        CHANGE_ORDER = "/departments/actions/change-order"

    class COMPETENCY_FRAMEWORK:
        COMPETENCY_FRAMEWORK = "/competency-frameworks"
        FILTERS_COMPETENCY_FRAMEWORK = "/competency-frameworks/filters"
        DETAIL_COMPETENCY_FRAMEWORK = "/competency-frameworks/{competency_framework_id}"
        SAVE_DRAFT = "/competency-frameworks/actions/save-draft"
        GET_STATUS = "/competency-frameworks/counts/status"
        CHANGE_STATUS = "/competency-frameworks/{competency_framework_id}/actions/change-status"
        SAVE_DRAFT_COMPETENCY_FRAMEWORK_ACTIVE = "/competency-frameworks/{competency_framework_id}/actions/save-draft"

    class COMPANY:
        COMPANY = "/company"
        SYNC_LARK_DATA = "/company/actions/sync-lark-data"

    class CallBack:
        TEST = "/test/callback"

    class LarkDocsTemplate:
        DOCS_TEMPLATE = "/docs-template/"

    class EVALUATE:
        EVALUATE = "/evaluates"
        DETAIL_EVALUATE = "/evaluates/{evaluate_id}"
        EXPORT_EVALUATE = "/evaluates/{evaluate_id}/actions/export"
        GET_STATUS = "/evaluates/status"
        CURRENT_USER = "/evaluates/current/user"
        SAVE_DRAFT = "/evaluates/actions/save-draft"
        FILTERS = "/evaluates/filters"
        FILTERS_MANAGED_TEAM = "/evaluates/users/actions/filters/managed-team/evaluations"
        # Lấy danh sách bản đánh giá của 1 nhân viên: /evaluates/users/<user_id>/actions/details/evaluations
        LIST_EVALUATE_BY_USER_ID = "/evaluates/users/{user_id}/actions/details/evaluations"

    class EVALUATE_PERIOD:
        EVALUATE_PERIOD = "/evaluate-periods"
        EVALUATE_PERIOD_TEMPLATE = "/evaluate-periods/templates"

    class ORG_CHART:
        ORG_CHART = "/org-charts"
        USERS = "/org-charts/users"
        DETAIL_USER = "/org-charts/users/{user_id}"
        DEPARTMENTS = "/org-charts/departments"

    class POLICY:
        POLICIES = "/policies"
        DETAIL_POLICY = "/policies/{policy_id}"
        ATTACH_USER_POLICY = "/users/{user_id}/policies"
        DETACH_USER_POLICY = "/users/{user_id}/policies/detach"
        ATTACH_ROLE_POLICY = "/roles/{role_id}/policies"
        DETACH_ROLE_POLICY = "/roles/{role_id}/policies/detach"
        EVALUATE_POLICY = "/users/{user_id}/evaluate-policy"
        CREATE_DEFAULT_POLICIES = "/policies/create-defaults"

    class TASK_PERFORMANCE:
        TASK_PERFORMANCE = "/task-performances"
        TASK_PERFORMANCE_BY_USER = "/task-performances/actions/get-by-user-id/{user_id}"
        AGGREGATE_TASK_PERFORMANCE = "/task-performances/actions/aggregate"

    class PUSH_NOTIFICATION:
        TRIGGER_PUSH_NOTIFICATION = "/push-notifications/actions/trigger"
