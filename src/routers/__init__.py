#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: tungdd
Date created: 04/01/2024
"""

from typing import Any, Dict, List

from pydantic import BaseSettings


class AppSettings(BaseSettings):
    debug: bool = True
    title: str = "Mobio Ladder"
    version: str = "0.1"
    allowed_hosts: List[str] = ["*"]

    @property
    def fastapi_kwargs(self) -> Dict[str, Any]:
        return {"title": self.title, "debug": self.debug, "version": self.version}
