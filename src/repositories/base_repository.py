from sqlalchemy.orm import Session


class BaseRepository:
    def __init__(
        self,
        db: Session,
    ) -> None:
        self.db = db

    async def get_one(self, entity, **kwargs):
        return self.db.query(entity).filter_by(**kwargs).first()

    async def insert_one(self, entity, data_insert):
        data = entity(**data_insert.dict())
        self.db.add(data)
        self.db.commit()
        self.db.refresh(data)
        return data

    async def delete_one(self, entity, **kwargs):
        data = self.get_one(entity, **kwargs)
        if data:
            self.db.delete(data)
            self.db.commit()

    async def delete(self, entity, **kwargs):
        data = self.get_one(entity, **kwargs)
        if data:
            self.db.delete(data)
            self.db.commit()

    async def update_one(self, entity, **kwargs):
        data = self.get_one(entity, **kwargs)
        if data:
            self.db.query(entity).filter_by(**kwargs).update(kwargs)
            self.db.commit()
        return data

    async def count_items(self, entity, **kwargs):
        return self.db.query(entity).filter_by(**kwargs).count()

    async def filter_by(self, entity, **kwargs):
        return self.db.query(entity).filter_by(**kwargs)

    async def query_items(self, entity, **kwargs):
        return self.db.query(entity).filter_by(**kwargs).all()
