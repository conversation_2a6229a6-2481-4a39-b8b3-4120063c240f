from src.models.postgres.base_model import RoleModel, UserRoleModel
from src.repositories.base_repository import BaseRepository


class RoleRepository(BaseRepository):
    async def get_role_by_ids(self, role_ids):
        return self.db.query(RoleModel).filter(RoleModel.role_id.in_(role_ids)).all()

    async def get_role_by_name(self, name):
        return self.db.query(RoleModel).filter_by(lower_case_name=name.lower()).first()

    async def get_roles_by_names(self, names):
        return self.db.query(RoleModel).filter(RoleModel.name.in_(names)).all()

    async def get_all_roles(self):
        return await self.filter_by(RoleModel)

    async def get_user_roles(self, user_id):
        """
        Get all roles associated with a user

        Args:
            user_id: The ID of the user

        Returns:
            List of role models
        """
        user_roles = self.db.query(UserRoleModel).filter(UserRoleModel.user_id == user_id).all()
        role_ids = [user_role.role_id for user_role in user_roles]

        if not role_ids:
            return []

        return await self.get_role_by_ids(role_ids)
