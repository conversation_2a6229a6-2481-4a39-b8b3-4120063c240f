from sqlalchemy import asc, desc, func

from src.common.choices import StatusChoice
from src.common.common import CommonKey
from src.models.postgres.base_model import DepartmentModel
from src.repositories.base_repository import BaseRepository
from src.schemas.pydantic.department_schemas import AddDepartmentSchema


class DepartmentRepository(BaseRepository):
    async def add_department(self, data: AddDepartmentSchema):
        department_model: DepartmentModel = DepartmentModel()
        data_insert = await self.insert_one(DepartmentModel, data_insert=data)

    async def count_departments(self, **kwargs):
        return await self.count_items(DepartmentModel, status=StatusChoice.ACTIVATE.value)

    async def get_open_department_ids(self):
        return await self.db.query(DepartmentModel.open_department_id).distinct()

    async def get_departments(
        self, search="", department_ids=[], company_id="", sort="updated_time", order=-1, page=1, per_page=20, display=1
    ):

        query = self.db.query(DepartmentModel).filter(
            DepartmentModel.status == StatusChoice.ACTIVATE.value, DepartmentModel.company_id == company_id
        )
        _order = desc if order == -1 else asc
        sort_column = getattr(DepartmentModel, sort)
        query = query.order_by(_order(sort_column))
        if display is not None:
            query = query.filter(DepartmentModel.display == display)
        if search:
            query = query.filter(func.lower(DepartmentModel.name).like(f"%{search.lower()}%"))
        if department_ids:
            query = query.filter(DepartmentModel.department_id.in_(department_ids))

        if page == -1:
            return query.all(), {}

        query = query.offset((page - 1) * per_page).limit(per_page)
        count = query.count()
        return query.all(), {
            CommonKey.PAGE: page,
            CommonKey.PER_PAGE: per_page,
            CommonKey.TOTAL_COUNT: count,
            CommonKey.TOTAL_PAGE: count // per_page + 1,
        }

    async def get_all_departments(self, department_ids=None):
        query = self.db.query(DepartmentModel).filter(DepartmentModel.status == StatusChoice.ACTIVATE.value)

        if department_ids:
            query = query.filter(DepartmentModel.department_id.in_(department_ids))

        return query.all()

    async def get_all_departments_by_company_id(self, company_id, department_ids=None):
        query = self.db.query(DepartmentModel).filter_by(status=StatusChoice.ACTIVATE.value, company_id=company_id)
        if department_ids:
            query = query.filter(DepartmentModel.department_id.in_(department_ids))
        return query.all()

    async def get_department_by_id(self, department_id):
        return self.db.query(DepartmentModel).filter_by(department_id=department_id).first()

    async def get_owners(self, company_id, department_ids):
        return (
            self.db.query(DepartmentModel)
            .filter(DepartmentModel.department_id.in_(department_ids), DepartmentModel.company_id == company_id)
            .distinct(DepartmentModel.owners)
            .all()
        )

    async def get_departments_by_owner(self, user_id):
        return self.db.query(DepartmentModel).filter(DepartmentModel.owners.any(user_id)).all()

    async def get_departments_by_ids(self, company_id, department_ids):
        return (
            self.db.query(DepartmentModel)
            .filter(DepartmentModel.company_id == company_id, DepartmentModel.department_id.in_(department_ids))
            .all()
        )

    async def get_department_owners(self, company_id, department_ids):
        if not department_ids:
            return self.db.query(DepartmentModel).filter(DepartmentModel.company_id == company_id).all()

        return (
            self.db.query(DepartmentModel)
            .filter(DepartmentModel.company_id == company_id, DepartmentModel.department_id.in_(department_ids))
            .all()
        )

    async def get_department_ids_by_lark_department_ids(self, lark_department_ids):
        return self.db.query(DepartmentModel).filter(DepartmentModel.lark_department_id.in_(lark_department_ids)).all()

    async def update_order_for_department(self, company_id, department_id, order):

        self.db.query(DepartmentModel).filter(
            DepartmentModel.company_id == company_id, DepartmentModel.department_id == department_id
        ).update({"order": order})
        self.db.commit()

    async def get_bod_department(self, company_id):
        return self.db.query(DepartmentModel).filter_by(company_id=company_id, lower_case_name="bod").first()
