from typing import Type

from sqlalchemy.orm import joinedload

from src.models.postgres.base_model import DepartmentModel, UserDepartmentModel
from src.repositories.base_repository import BaseRepository


class UserDepartmentRepository(BaseRepository):

    async def get_department_by_user_id(self, user_id: str) -> Type[DepartmentModel] | None:
        """
        Lấy thông tin department của một user dựa trên user_id

        Args:
            user_id: ID của user cần lấy thông tin department

        Returns:
            DepartmentModel: Thông tin department hoặc None nếu không tìm thấy
        """
        # <PERSON><PERSON><PERSON> bản ghi từ bảng user_department với user_id tương ứng
        query = (
            self.db.query(UserDepartmentModel)
            .options(joinedload(UserDepartmentModel.department))
            .filter(UserDepartmentModel.user_id == user_id)
        )

        user_department = query.first()

        # Trả về thông tin department nếu tìm thấy
        return user_department.department if user_department else None

    async def get_user_departments_by_user_id(self, user_id) -> list[Type[UserDepartmentModel]]:
        data = await self.filter_by(UserDepartmentModel, user_id=user_id)
        return data
