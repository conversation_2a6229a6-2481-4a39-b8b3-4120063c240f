from src.models.postgres.base_model import JobTitleLevelModel
from src.repositories.base_repository import BaseRepository


class JobTitleLevelRepository(BaseRepository):
    async def add_job_title_level(self, data):
        data_insert = await self.insert_one(JobTitleLevelModel, data_insert=data)

    async def get_job_title_level_by_id(self, job_title_level_id):
        return await self.get_one(JobTitleLevelModel, job_title_level_id=job_title_level_id)

    async def get_job_title_level_by_ids(self, job_title_level_ids):
        return (
            self.db.query(JobTitleLevelModel)
            .filter(JobTitleLevelModel.job_title_level_id.in_(job_title_level_ids))
            .all()
        )
