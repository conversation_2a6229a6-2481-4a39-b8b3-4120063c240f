from typing import Type

from sqlalchemy import asc, desc, text

from src.common.choices import OperatorC<PERSON><PERSON>, RoleChoice, StatusChoice
from src.common.common import <PERSON><PERSON><PERSON>, DepartmentKey
from src.models.postgres.base_model import (
    DepartmentModel,
    EmploymentTypeModel,
    RoleModel,
    UserModel,
)
from src.repositories.base_repository import BaseRepository
from src.schemas.pydantic.user_schemas import UserInsertDataSchema
from src.utils import utf8_to_ascii


class UserRepository(BaseRepository):
    async def get_one_by_primary_email(self, primary_email) -> Type[UserModel] | None:
        data = await self.get_one(UserModel, primary_email=primary_email)
        return data

    def add_user(self, data: UserInsertDataSchema) -> UserModel:
        data.employee_code = UserModel().generate_employee_code(self.db)
        data_insert = self.insert_one(UserModel, data_insert=data)
        return data_insert.to_dict(_hide=[UserModel.password.key])

    async def update_user_job_title_level_id(self, user_id, job_title_level_id):
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        user.job_title_level_id = job_title_level_id
        self.db.commit()
        return True

    async def count_users(self):
        return await self.count_items(UserModel, status=StatusChoice.ACTIVATE.value)

    async def get_users(
        self,
        company_id,
        search="",
        user_filters=[],
        user_ids=[],
        sort="updated_time",
        order=-1,
        page=1,
        per_page=20,
        roles=[],
    ):
        query = self.db.query(UserModel).filter(
            UserModel.company_id == company_id, UserModel.status == StatusChoice.ACTIVATE.value
        )
        if search:
            search = utf8_to_ascii(search.lower())
            query = query.filter(UserModel.unsigned_name.like(f"%{search}%"))

        if user_ids:
            query = query.filter(UserModel.user_id.in_(user_ids))

        _order = desc if order == -1 else asc
        sort_column = getattr(UserModel, sort)

        for user_filter in user_filters:
            if user_filter.field == DepartmentKey.DEPARTMENT_ID:
                query = query.filter(UserModel.departments.any(DepartmentModel.department_id.in_(user_filter.value)))
            elif user_filter.field == "roles":
                query = query.filter(UserModel.roles.any(RoleModel.name.in_(user_filter.value)))
            else:
                if user_filter.operator == OperatorChoice.OP_IS_IN.value:
                    query = query.filter(getattr(UserModel, user_filter.field).in_(user_filter.value))
                elif user_filter.operator == OperatorChoice.OP_IS_EQ.value:
                    query = query.filter(getattr(UserModel, user_filter.field) == user_filter.value)
                elif user_filter.operator == OperatorChoice.OP_IS_RE.value:
                    query = query.filter(getattr(UserModel, user_filter.field).like(f"%{user_filter.value}%"))
                elif user_filter.operator == OperatorChoice.OP_IS_LT.value:
                    query = query.filter(getattr(UserModel, user_filter.field) < user_filter.value)
                elif user_filter.operator == OperatorChoice.OP_IS_GE.value:
                    query = query.filter(getattr(UserModel, user_filter.field) >= user_filter.value)
                elif user_filter.operator == OperatorChoice.OP_IS_PE.value:
                    query = query.filter(
                        getattr(UserModel, user_filter.field).between(user_filter.value[0], user_filter.value[1])
                    )
        if page == -1:
            return query.all(), {}

        query = query.order_by(_order(sort_column))
        skip = (page - 1) * per_page
        query = query.offset(skip).limit(per_page)
        count = query.count()

        return query.all(), {
            CommonKey.PAGE: page,
            CommonKey.PER_PAGE: per_page,
            CommonKey.TOTAL_COUNT: count,
            CommonKey.TOTAL_PAGE: count // per_page + 1,
        }

    async def get_leader_user_ids(self, user_ids):
        return self.db.query(UserModel.leader_user_id).distinct().filter(UserModel.user_id.in_(user_ids)).all()

    async def get_user_by_id(self, user_id):
        return await self.get_one(UserModel, user_id=user_id)

    async def get_user_by_open_user_id(self, open_user_id):
        return await self.get_one(UserModel, open_user_id=open_user_id, status=StatusChoice.ACTIVATE.value)

    async def get_users_by_ids(self, company_id, user_ids):
        return (
            self.db.query(UserModel).filter(UserModel.company_id == company_id, UserModel.user_id.in_(user_ids)).all()
        )

    async def get_user_type(self, type_id):
        return await self.get_one(EmploymentTypeModel, employment_type_id=type_id)

    async def update_users(self, data_update):
        self.db.bulk_update_mappings(UserModel, data_update)
        self.db.commit()
        return

    async def get_user_by_job_title_level_ids(self, job_title_level_ids):
        return self.db.query(UserModel).filter(UserModel.job_title_level_id.in_(job_title_level_ids)).all()

    async def add_roles_to_user(self, user_id, roles):
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        user.roles = roles
        self.db.commit()
        return True

    async def get_users_for_org_chart(self, company_id, search="", user_filters=[], sort="updated_time", order=-1):
        query = self.db.query(UserModel).filter(
            UserModel.company_id == company_id, UserModel.status == StatusChoice.ACTIVATE.value
        )
        if search:
            query = query.filter(UserModel.name.like(f"%{search}%"))
        _order = desc if order == -1 else asc
        sort_column = getattr(UserModel, sort)
        for user_filter in user_filters:
            query = query.filter(UserModel.departments.any(DepartmentModel.department_id.in_(user_filter.value)))
        query = query.order_by(_order(sort_column))
        return query.all()

    async def get_users_by_job_title_ids(self, job_title_ids):
        return self.db.query(UserModel).filter(UserModel.job_title_id.in_(job_title_ids)).all()

    async def get_list_admin(self):
        list_admin = (
            self.db.query(UserModel).filter(UserModel.roles.any(RoleModel.name == RoleChoice.ADMIN.value)).all()
        )
        return list_admin

    async def get_users_by_departments(self, department_ids):
        return (
            self.db.query(UserModel)
            .filter(UserModel.departments.any(DepartmentModel.department_id.in_(department_ids)))
            .all()
        )

    async def get_users_by_leader_id(self, leader_id):
        return self.db.query(UserModel).filter(UserModel.leader_user_id == leader_id).all()

    async def get_all_user_by_company_id(self, company_id):
        return self.db.query(UserModel).filter(UserModel.company_id == company_id)

    async def get_detail_user_by_id(self, user_id):
        query = text(
            """
            SELECT u.*, jt.name as job_title_name
            FROM "user" u
            JOIN job_title jt ON u.job_title_id = jt.job_title_id
            WHERE u.user_id = :user_id
        """
        )
        result = self.db.execute(query, {"user_id": user_id})
        return result.fetchone()

    async def get_detail_user_by_ids(self, user_ids):
        query = text(
            """
            SELECT u.*, jt.name as job_title_name
            FROM "user" u
            JOIN job_title jt ON u.job_title_id = jt.job_title_id
            WHERE u.user_id IN :user_ids
        """
        )
        result = self.db.execute(query, {"user_ids": user_ids})
        return result.fetchall()

    async def get_user_in_bod(self, company_id):
        bod_department = self.db.query(DepartmentModel).filter_by(lower_case_name="bod", company_id=company_id).first()
        return (
            self.db.query(UserModel)
            .filter(UserModel.departments.any(DepartmentModel.department_id.in_([bod_department.department_id])))
            .all()
        )
