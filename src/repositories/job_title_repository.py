from sqlalchemy import asc, desc

from src.common.choices import StatusChoice
from src.common.common import <PERSON><PERSON><PERSON>, JobTitleLevelKey
from src.models.postgres.base_model import JobTitleLevelModel, JobTitleModel
from src.repositories.base_repository import BaseRepository


class JobTitleRepository(BaseRepository):
    async def get_job_titles(
        self, department_id, job_title_ids=[], search="", sort="order", order=-1, page=0, per_page=20
    ):
        query = self.db.query(JobTitleModel).filter(
            JobTitleModel.department_id == department_id, JobTitleModel.status == StatusChoice.ACTIVATE.value
        )
        if job_title_ids:
            query = query.filter(JobTitleModel.job_title_id.in_(job_title_ids))

        if search:
            query = query.filter(JobTitleModel.name.like(f"%{search}%"))

        _order = desc if order == -1 else asc
        sort_column = getattr(JobTitleModel, sort)
        query = query.order_by(_order(sort_column))

        if page == -1:
            return query.all()
        skip = page * per_page
        return query.offset(skip).limit(per_page).all()

    async def get_job_title_by_id(self, job_title_id):
        return await self.get_one(JobTitleModel, job_title_id=job_title_id)

    async def get_all_job_titles(self):
        return await self.query_items(JobTitleModel, status=StatusChoice.ACTIVATE.value)

    async def count_job_title_levels(self, job_title_id):
        return await self.count_items(JobTitleLevelModel, job_title_id=job_title_id, status=StatusChoice.ACTIVATE.value)

    async def get_job_title_level(self, **kwargs):
        return await self.get_one(JobTitleLevelModel, **kwargs)

    async def get_job_title_levels(self, job_title_id):
        return await self.query_items(JobTitleLevelModel, job_title_id=job_title_id, status=StatusChoice.ACTIVATE.value)

    async def get_all_job_title_levels(self):
        return await self.query_items(JobTitleLevelModel, status=StatusChoice.ACTIVATE.value)

    async def add_job_title_level(self, company_id, job_title_id, name, level, account_id, time_now):
        model_insert = JobTitleLevelModel(
            company_id=company_id,
            job_title_id=job_title_id,
            name=name,
            level=level,
            created_by=account_id,
            updated_by=account_id,
            created_time=time_now,
            updated_time=time_now,
        )
        self.db.add(model_insert)
        self.db.commit()

    async def update_job_title_level(self, job_title_id, level, name, status, account_id, time_now):
        self.db.query(JobTitleLevelModel).filter_by(job_title_id=job_title_id, level=level).update(
            {
                JobTitleLevelKey.NAME: name,
                JobTitleLevelKey.LEVEL: level,
                JobTitleLevelKey.LOWER_CASE_NAME: name.lower(),
                JobTitleLevelKey.STATUS: status,
                CommonKey.UPDATED_BY: account_id,
                CommonKey.UPDATED_TIME: time_now,
            }
        )
        self.db.commit()
        return

    async def delete_job_titles_levels(self, job_title_level_ids):
        self.db.query(JobTitleLevelModel).filter(JobTitleLevelModel.job_title_level_id.in_(job_title_level_ids)).delete(
            synchronize_session=False
        )
        self.db.commit()
        return

    async def delete_job_title_levels_by_levels(self, job_title_id, levels):
        self.db.query(JobTitleLevelModel).filter(
            JobTitleLevelModel.job_title_id == job_title_id, JobTitleLevelModel.level.in_(levels)
        ).update(
            {
                JobTitleLevelKey.STATUS: 0,
            }
        )
        self.db.commit()
        return

    async def get_job_title_by_department_id(self, department_id):
        return self.db.query(JobTitleModel).filter(JobTitleModel.department_id == department_id).all()

    async def get_job_title_levels_by_names(self, names):
        return self.db.query(JobTitleLevelModel).filter(JobTitleLevelModel.name.in_(names)).all()
