from src.models.postgres.base_model import CompanyModel
from src.repositories.base_repository import BaseRepository


class CompanyRepository(BaseRepository):
    async def get_company(self, **kwargs):
        return self.db.query(CompanyModel).filter_by(**kwargs).first()

    async def get_all_company(self, company_ids=[]):
        if company_ids:
            return self.db.query(CompanyModel).filter(CompanyModel.company_id.in_(company_ids)).all()

        return self.db.query(CompanyModel).all()
