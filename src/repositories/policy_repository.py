#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 14/03/2025
"""

from ast import Dict
from typing import List, Optional

from src.models.postgres.base_model import PolicyModel, RoleModel, UserModel
from src.repositories.base_repository import BaseRepository


class PolicyRepository(BaseRepository):
    async def get_policy_by_id(self, policy_id: str) -> Optional[PolicyModel]:
        return await self.get_one(PolicyModel, policy_id=policy_id)

    async def get_policies(self, company_id: str) -> List[PolicyModel]:
        return await self.filter_by(PolicyModel, company_id=company_id)

    async def get_user_policies(self, user_id: str) -> List[Dict]:
        """
        Get all policies directly attached to a user

        Args:
            user_id: The ID of the user

        Returns:
            List of policy documents
        """
        # Find the user
        user = await self.db.query(UserModel).filter(UserModel.user_id == user_id).first()
        if not user:
            return []

        # Get all policies attached to the user
        policies = []
        for policy in user.policies:
            policies.append({"policy_id": policy.policy_id, "name": policy.name, "document": policy.document})

        return policies

    async def get_role_policies(self, role_id: str) -> List[Dict]:
        """
        Get all policies attached to a role

        Args:
            role_id: The ID of the role

        Returns:
            List of policy documents
        """
        # Find the role
        role = await self.db.query(RoleModel).filter(RoleModel.role_id == role_id).first()
        if not role:
            return []

        # Get all policies attached to the role
        policies = []
        for policy in role.policies:
            policies.append({"policy_id": policy.policy_id, "name": policy.name, "document": policy.document})

        return policies
