#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from typing import Any, Dict, Optional

from fastapi import Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import Authentication
from src.common.custom_exception import AccessDenied, Unauthorized
from src.helpers.policy_evaluator import PolicyEvaluator


async def authorize(
    request: Request,
    action: str,
    resource: str,
    context: Optional[Dict[str, Any]] = None,
    session: Session = Depends(get_db),
) -> bool:
    """
    Authorize a request to perform an action on a resource.

    Args:
        request: The FastAPI request object
        action: The action being performed (e.g., "user:List")
        resource: The resource ARN being accessed (e.g., "arn:app:user:*")
        context: Additional context for condition evaluation
        session: Database session

    Returns:
        bool: True if authorized, raises exception if not
    """
    try:
        user, _ = await Authentication().get_user_and_roles(request)

        # Default context if not provided
        if not context:
            context = {}

        # Add request data to context
        context.update(
            {
                "request_method": request.method,
                "request_path": request.url.path,
                "request_query": dict(request.query_params),
            }
        )

        # Check if action is allowed
        evaluator = PolicyEvaluator(session)
        is_allowed = await evaluator.is_allowed(user_id=user.user_id, action=action, resource=resource, context=context)

        if not is_allowed:
            raise AccessDenied(message=f"Access denied for {action} on {resource}")

        return True

    except Unauthorized as e:
        raise e
    except Exception as e:
        raise AccessDenied(message=str(e))
