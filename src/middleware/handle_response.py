#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 15/01/2024
"""
from typing import Callable

from fastapi import Request, Response
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.routing import APIRoute

from src.common.custom_exception import (
    AccessDenied,
    ConflictMessage,
    CustomBaseException,
    CustomError,
    InputParamError,
    ServerErrorMessage,
    Unauthorized,
)


def http_conflict_message_handler(request: Request, exc: ConflictMessage):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.message, "code": exc.status_code},
    )


def http_authorized_handler(request: Request, exc: Unauthorized):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.message, "code": exc.status_code},
    )


def http_access_denied_handler(request: Request, exc: AccessDenied):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.message, "code": exc.status_code},
    )


def http_custom_error_handler(request: Request, exc: CustomError):
    content = {"message": exc.message, "code": exc.status_code}
    if exc.data:
        content.update({"data": exc.data})
    return JSONResponse(
        status_code=exc.status_code,
        content=content,
    )


def http_input_param_handler(request: Request, exc: InputParamError):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.message, "code": exc.status_code},
    )


def http_server_error_handler(request: Request, exc: ServerErrorMessage):
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.message, "code": exc.status_code},
    )


class CustomHandleResponseRoute(APIRoute):
    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            try:
                response: Response = await original_route_handler(request)
            except CustomError as ex:
                CustomBaseException().logger.error("Error to::", exc_info=ex)
                raise CustomError(str(ex.message), ex.data)
            except ConflictMessage as ce:
                CustomBaseException().logger.error("Error to::", exc_info=ce)
                raise ConflictMessage(str(ce.message))
            except Unauthorized as ue:
                CustomBaseException().logger.error("Error to::", exc_info=ue)
                raise Unauthorized(str(ue.message))
            except AccessDenied as ad:
                CustomBaseException().logger.error("Error to::", exc_info=ad)
                raise AccessDenied(str(ad.message))
            except InputParamError as ie:
                CustomBaseException().logger.error("Error to::", exc_info=ie)
                raise InputParamError(str(ie.message))
            except ServerErrorMessage as se:
                CustomBaseException().logger.error("Error to::", exc_info=se)
                raise ServerErrorMessage(str(se.message))
            except RequestValidationError as re:
                CustomBaseException().logger.error("Error to::", exc_info=re)
                raise InputParamError(re.errors())
            except Exception as ex:
                CustomBaseException().logger.error("Error to::", exc_info=ex)
                raise ServerErrorMessage("Có lỗi phát sinh trong server!")
            return response

        return custom_route_handler
