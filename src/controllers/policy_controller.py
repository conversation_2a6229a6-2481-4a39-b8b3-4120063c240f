import datetime
from typing import Dict

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from sqlalchemy.orm import Session

from src.helpers.policy_evaluator import PolicyEvaluator
from src.models.postgres.base_model import PolicyModel
from src.repositories.policy_repository import PolicyRepository
from src.repositories.role_repository import RoleRepository
from src.repositories.users_repository import UserRepository
from src.utils.default_policies import DEFAULT_POLICIES


class PolicyController:
    def __init__(self):
        pass

    async def get_policies(self, request: Request, session: Session) -> Dict:
        """Get all policies"""
        # Use a fixed company_id for admin interface or get from all companies
        company_id = "4302feda-826d-4419-b1f5-c9a8291ffb51"  # Example company ID or None for all
        policy_repo = PolicyRepository(session)

        policies = await policy_repo.get_policies(company_id)

        result = []
        for policy in policies:
            result.append(
                {
                    "policy_id": policy.policy_id,
                    "name": policy.name,
                    "description": policy.description,
                    "policy_type": policy.policy_type,
                    "document": policy.document,
                    "created_time": policy.created_time.isoformat(),
                    "updated_time": policy.updated_time.isoformat(),
                    "created_by": policy.created_by,
                    "updated_by": policy.updated_by,
                }
            )

        return {"data": result}

    async def get_policy(self, request: Request, policy_id: str, session: Session) -> Dict:
        """Get a policy by ID"""
        policy_repo = PolicyRepository(session)

        policy = await policy_repo.get_policy_by_id(policy_id)
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        return {
            "data": {
                "policy_id": policy.policy_id,
                "name": policy.name,
                "description": policy.description,
                "policy_type": policy.policy_type,
                "document": policy.document,
                "created_time": policy.created_time.isoformat(),
                "updated_time": policy.updated_time.isoformat(),
                "created_by": policy.created_by,
                "updated_by": policy.updated_by,
            }
        }

    async def create_policy(self, request: Request, payload: Dict, session: Session) -> Dict:
        """Create a new policy"""
        # Use a fixed company_id and admin user_id for policy admin interface
        company_id = "4302feda-826d-4419-b1f5-c9a8291ffb51"  # Example company ID
        admin_user_id = "admin"  # Special admin ID

        # Create policy model
        policy = PolicyModel(
            name=payload["name"],
            description=payload.get("description", ""),
            policy_type=payload.get("policy_type", "IDENTITY"),
            document=payload["document"],
            company_id=company_id,
            created_by=admin_user_id,
            updated_by=admin_user_id,
        )

        # Save to database
        session.add(policy)
        session.commit()

        return {
            "data": {
                "policy_id": policy.policy_id,
                "name": policy.name,
                "description": policy.description,
                "created_time": policy.created_time.isoformat(),
            }
        }

    async def update_policy(self, request: Request, policy_id: str, payload: Dict, session: Session) -> Dict:
        """Update an existing policy"""
        admin_user_id = "admin"  # Special admin ID for policy admin interface
        policy_repo = PolicyRepository(session)

        # Get existing policy
        policy = await policy_repo.get_policy_by_id(policy_id)
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        # Update fields
        if "name" in payload:
            policy.name = payload["name"]

        if "description" in payload:
            policy.description = payload["description"]

        if "document" in payload:
            policy.document = payload["document"]

        policy.updated_by = admin_user_id
        policy.updated_time = datetime.datetime.utcnow()

        # Save changes
        session.add(policy)
        session.commit()

        return {
            "data": {
                "policy_id": policy.policy_id,
                "name": policy.name,
                "description": policy.description,
                "updated_time": policy.updated_time.isoformat(),
            }
        }

    async def delete_policy(self, request: Request, policy_id: str, session: Session) -> Dict:
        """Delete a policy"""
        policy_repo = PolicyRepository(session)

        # Get existing policy
        policy = await policy_repo.get_policy_by_id(policy_id)
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        # Delete the policy
        session.delete(policy)
        session.commit()

        return {"data": {"message": "Policy deleted successfully"}}

    async def attach_policy_to_user(self, request: Request, user_id: str, payload: Dict, session: Session) -> Dict:
        """Attach a policy to a user"""
        user_repo = UserRepository(session)
        policy_repo = PolicyRepository(session)

        # Get user and policy
        user = await user_repo.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        policy = await policy_repo.get_policy_by_id(payload["policy_id"])
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        # Attach policy to user
        user.policies.append(policy)
        session.add(user)
        session.commit()

        return {"data": {"message": "Policy attached to user successfully"}}

    async def detach_policy_from_user(self, request: Request, user_id: str, payload: Dict, session: Session) -> Dict:
        """Detach a policy from a user"""
        user_repo = UserRepository(session)
        policy_repo = PolicyRepository(session)

        # Get user and policy
        user = await user_repo.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        policy = await policy_repo.get_policy_by_id(payload["policy_id"])
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        # Detach policy from user
        if policy in user.policies:
            user.policies.remove(policy)
            session.add(user)
            session.commit()

        return {"data": {"message": "Policy detached from user successfully"}}

    async def attach_policy_to_role(self, request: Request, role_id: str, payload: Dict, session: Session) -> Dict:
        """Attach a policy to a role"""
        role_repo = RoleRepository(session)
        policy_repo = PolicyRepository(session)

        # Get role and policy
        role = await role_repo.get_role_by_ids([role_id])
        if not role or len(role) == 0:
            raise HTTPException(status_code=404, detail="Role not found")

        policy = await policy_repo.get_policy_by_id(payload["policy_id"])
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        # Attach policy to role
        role[0].policies.append(policy)
        session.add(role[0])
        session.commit()

        return {"data": {"message": "Policy attached to role successfully"}}

    async def detach_policy_from_role(self, request: Request, role_id: str, payload: Dict, session: Session) -> Dict:
        """Detach a policy from a role"""
        role_repo = RoleRepository(session)
        policy_repo = PolicyRepository(session)

        # Get role and policy
        role = await role_repo.get_role_by_ids([role_id])
        if not role or len(role) == 0:
            raise HTTPException(status_code=404, detail="Role not found")

        policy = await policy_repo.get_policy_by_id(payload["policy_id"])
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        # Detach policy from role
        if policy in role[0].policies:
            role[0].policies.remove(policy)
            session.add(role[0])
            session.commit()

        return {"data": {"message": "Policy detached from role successfully"}}

    async def evaluate_policy(self, request: Request, user_id: str, payload: Dict, session: Session) -> Dict:
        """Evaluate if a user has permission for an action on a resource"""
        # Create policy evaluator
        evaluator = PolicyEvaluator(session)

        # Check permission
        result = await evaluator.is_allowed(
            user_id=user_id, action=payload["action"], resource=payload["resource"], context=payload.get("context", {})
        )

        return {
            "data": {
                "allowed": result,
                "evaluated_for": {"user_id": user_id, "action": payload["action"], "resource": payload["resource"]},
            }
        }

    async def create_default_policies(self, request: Request, session: Session) -> Dict:
        """Create all default policies in the system"""
        # Use fixed company_id and admin_user_id for policy admin interface
        company_id = "4302feda-826d-4419-b1f5-c9a8291ffb51"  # Example company ID
        admin_user_id = "admin"  # Special admin ID

        created_policies = []

        for policy_name, policy_data in DEFAULT_POLICIES.items():
            # Check if policy already exists
            existing_policy = session.query(PolicyModel).filter(PolicyModel.name == policy_name).first()
            if existing_policy:
                continue

            # Create the policy
            policy = PolicyModel(
                name=policy_data["name"],
                description=policy_data["description"],
                policy_type=policy_data["policy_type"],
                document=policy_data["document"],
                company_id=company_id,
                created_by=admin_user_id,
                updated_by=admin_user_id,
            )

            session.add(policy)
            created_policies.append(policy_data["name"])

        session.commit()

        return {"data": {"created_policies": created_policies, "total_created": len(created_policies)}}
