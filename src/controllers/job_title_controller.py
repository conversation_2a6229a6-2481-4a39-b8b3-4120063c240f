from mobio.libs.logging import MobioLogging
from sqlalchemy.orm import Session
from starlette.requests import Request

from src.common.choices import LanguageChoice, QueryParamChoice
from src.common.common import Common<PERSON>ey, JobTitleKey, JobTitleLevelKey
from src.common.custom_exception import InputParamError
from src.common.message import ErrorsMessage, SuccessMessage
from src.controllers.base_controller import BaseController
from src.repositories.department_repository import DepartmentRepository
from src.repositories.job_title_repository import JobTitleRepository
from src.repositories.users_repository import UserRepository
from src.schemas.pydantic.job_title_schemas import AddJobTitleLevelSchema
from src.utils.time_helper import get_time_now


class JobTitleController(BaseController):

    async def get_lst_job_title(self, request: Request, department_id, session: Session):
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        search = request.query_params.get(QueryParamChoice.SEARCH, "")
        job_title_ids = request.query_params.get(QueryParamChoice.JOB_TITLE_IDS, "")
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.ORDER)
        order = request.query_params.get(QueryParamChoice.ORDER, 1)
        page = request.query_params.get(QueryParamChoice.SKIP, 0)
        per_page = request.query_params.get(QueryParamChoice.LIMIT, 20)
        if job_title_ids:
            job_title_ids = job_title_ids.split(",")

        job_titles = await job_title_repo.get_job_titles(
            department_id, job_title_ids, search, sort, order, page, per_page
        )

        results = []
        for job_title in job_titles:
            job_title_levels = await job_title_repo.get_job_title_levels(job_title_id=job_title.job_title_id)

            results.append(
                {
                    JobTitleKey.JOB_TITLE_ID: job_title.job_title_id,
                    JobTitleKey.NAME: job_title.name,
                    JobTitleKey.DESCRIPTION: job_title.description,
                    JobTitleKey.ORDER: job_title.order,
                    JobTitleKey.JOB_TITLE_LEVELS: [
                        {
                            JobTitleLevelKey.NAME: job_title_level.name,
                            JobTitleLevelKey.LEVEL: job_title_level.level,
                            JobTitleLevelKey.JOB_TITLE_LEVEL_ID: job_title_level.job_title_level_id,
                        }
                        for job_title_level in job_title_levels
                    ],
                }
            )

        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
                CommonKey.DATA: results,
            }
        )

    async def upsert_job_title_levels(
        self, request: Request, department_id, payload: AddJobTitleLevelSchema, session: Session
    ):
        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)
        time_now = get_time_now()

        department_repo: DepartmentRepository = DepartmentRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        if not await department_repo.get_department_by_id(department_id):
            raise InputParamError(self.get_lang(lang).get(ErrorsMessage.DEPARTMENT_IS_NOT_EXIST), 404)

        for job_title in payload.job_titles:
            job_title_levels_in_system = await job_title_repo.get_job_title_levels(job_title.job_title_id)

            levels_in_system = []
            for job_title_level in job_title_levels_in_system:
                levels_in_system.append(job_title_level.level)

            if not await job_title_repo.get_job_title_by_id(job_title.job_title_id):
                raise InputParamError(
                    self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_IS_NOT_EXIST).get(CommonKey.MESSAGE), 404
                )

            for job_title_level in job_title.job_title_levels:
                if job_title_level.level in levels_in_system:
                    levels_in_system.remove(job_title_level.level)

                if not await job_title_repo.get_job_title_level(
                    job_title_id=job_title.job_title_id, level=job_title_level.level
                ):
                    await job_title_repo.add_job_title_level(
                        company_id=company_id,
                        job_title_id=job_title.job_title_id,
                        level=job_title_level.level,
                        name=job_title_level.name,
                        account_id=account_id,
                        time_now=time_now,
                    )
                    MobioLogging().debug(
                        "add_job_title_levels :: job_title_id: {} :: job_title_level :: name :: {} :: level :: {} :: created".format(
                            job_title.job_title_id, job_title_level.name, job_title_level.level
                        )
                    )
                else:
                    await job_title_repo.update_job_title_level(
                        job_title_id=job_title.job_title_id,
                        level=job_title_level.level,
                        name=job_title_level.name,
                        status=1,
                        account_id=account_id,
                        time_now=time_now,
                    )
                    MobioLogging().debug(
                        "add_job_title_levels :: job_title_id: {} :: job_title_level :: name :: {} :: level :: {} :: updated".format(
                            job_title.job_title_id, job_title_level.name, job_title_level.level
                        )
                    )
            if levels_in_system:
                await job_title_repo.delete_job_title_levels_by_levels(job_title.job_title_id, levels_in_system)
        MobioLogging().debug("add_job_title_levels :: upsert success")

        # Response data
        response_data = {}
        response_data["job_titles"] = {}
        for job_title in payload.job_titles:
            job_title_levels_in_system = await job_title_repo.get_job_title_levels(job_title.job_title_id)
            response_data["job_titles"][job_title.job_title_id] = {}
            response_data["job_titles"][job_title.job_title_id][JobTitleLevelKey.JOB_TITLE_ID] = job_title.job_title_id
            response_data["job_titles"][job_title.job_title_id]["job_title_levels"] = []

            for job_title_level in job_title_levels_in_system:
                response_data["job_titles"][job_title.job_title_id]["job_title_levels"].append(
                    {
                        JobTitleLevelKey.JOB_TITLE_LEVEL_ID: job_title_level.job_title_level_id,
                        JobTitleLevelKey.NAME: job_title_level.name,
                        JobTitleLevelKey.LEVEL: job_title_level.level,
                        JobTitleLevelKey.STATUS: job_title_level.status,
                    }
                )
        response_data["job_titles"] = list(response_data["job_titles"].values())
        return await self.json_encoder(
            {CommonKey.MESSAGE: SuccessMessage.UPSERT_SUCCESS, CommonKey.DATA: response_data}
        )

    async def delete_job_titles_levels(self, request: Request, department_id, job_title_id, session: Session):
        func_name = self.delete_job_titles_levels.__name__

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        user_repo: UserRepository = UserRepository(session)
        department_repo: DepartmentRepository = DepartmentRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        job_title_level_ids = request.query_params.get(QueryParamChoice.JOB_TITLE_LEVEL_IDS, "")
        if job_title_level_ids:
            job_title_level_ids = job_title_level_ids.split(",")

        if not await department_repo.get_department_by_id(department_id):
            raise InputParamError(
                self.get_lang(lang).get(ErrorsMessage.DEPARTMENT_IS_NOT_EXIST).get(CommonKey.MESSAGE), 404
            )

        if not await job_title_repo.get_job_title_by_id(job_title_id):
            raise InputParamError(
                self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_IS_NOT_EXIST).get(CommonKey.MESSAGE), 404
            )

        if await user_repo.get_user_by_job_title_level_ids(job_title_level_ids):
            raise InputParamError(
                self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_LEVELS_IS_USED).get(CommonKey.MESSAGE), 400
            )

        await job_title_repo.delete_job_titles_levels(job_title_level_ids)
        self.logger.debug("{} :: job_title_level_ids :: {} :: deleted".format(func_name, job_title_level_ids))
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.DELETE_SUCCESS})
