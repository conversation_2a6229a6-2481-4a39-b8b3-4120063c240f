from starlette.requests import Request

from src.auth.encrypt import decode_oauth2_token
from src.common.common import AuthorizationK<PERSON>, <PERSON><PERSON><PERSON>, UserKey
from src.controllers.base_controller import BaseController


class RoleController(BaseController):

    async def get_roles(self, request: Request):
        authorization = request.headers.get(AuthorizationKey.AUTHORIZATION)
        if authorization.startswith(f"{AuthorizationKey.BEARER} "):
            authorization = authorization.replace(f"{AuthorizationKey.BEARER} ", "")

        payload_decode = await decode_oauth2_token(authorization)
        roles = payload_decode.get(UserKey.ROLES)
        permissions = payload_decode.get(UserKey.PERMISSIONS)

        return await self.json_encoder({CommonKey.DATA: {UserKey.ROLES: roles, UserKey.PERMISSIONS: permissions}})
