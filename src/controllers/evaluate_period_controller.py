import datetime

from fastapi import Request

from configs.database import get_db
from src.common.choices import QueryParamChoice, RoleChoice
from src.common.common import CommonKey, EvaluatePeriodEnum
from src.common.message import SuccessMessage
from src.controllers.base_controller import BaseController
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.repositories.user_department_repository import UserDepartmentRepository
from src.schemas.pydantic.evaluate_period_schemas import (
    AddEvaluatePeriodTemplateRequestSchema,
)


class EvaluatePeriodController(BaseController):
    async def get_evaluate_periods(self, request: Request):
        session = next(get_db())
        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        evaluate_period_ids = request.query_params.get(QueryParamChoice.EVALUATE_PERIOD_IDS, "")
        start_time = request.query_params.get(QueryParamChoice.START_TIME, "")
        end_time = request.query_params.get(QueryParamChoice.END_TIME, "")
        sort = request.query_params.get(QueryParamChoice.SORT, "start_time")
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        if evaluate_period_ids:
            evaluate_period_ids = evaluate_period_ids.split(",")

        user_department_repo = UserDepartmentRepository(session)

        roles, _ = await self.get_roles_and_permissions_from_token(request)
        department_ids = await self.get_department_ids_by_permission_user(roles, user_department_repo, account_id)

        # filter_type_default = request.query_params.get(QueryParamChoice.FILTER_TYPE_DEFAULT, "")

        filter_type_default = None

        if not start_time and not end_time:
            filter_type_default = datetime.datetime.now(datetime.timezone.utc)

        evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
        results = await evaluate_period_model.get_evaluate_periods(
            company_id, evaluate_period_ids, start_time, end_time, sort, order, department_ids, filter_type_default
        )

        return {
            CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
            CommonKey.DATA: await self.json_encoder(results),
        }

    async def get_department_ids_by_permission_user(self, roles, user_department_repo, account_id):
        department_ids = []
        if RoleChoice.ADMIN.value in roles:
            return []
        if RoleChoice.LEADER.value in roles or RoleChoice.USER.value in roles:
            user_departments = await user_department_repo.get_user_departments_by_user_id(account_id)
            for user_department in user_departments:
                department_ids.append(user_department.department_id)
        return department_ids

    async def add_evaluate_period_template(self, request: Request, payload: AddEvaluatePeriodTemplateRequestSchema):
        from scripts import ensure_default_evaluate_period
        from src.cronjobs.handler_system_auto_generate_evaluate_cronjob import (
            HandlerAutoGenerateEvaluate,
        )

        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        payload.created_by = payload.updated_by = account_id
        template = payload.dict()
        template["repeat_type"] = EvaluatePeriodEnum.RepeatType.ONCE
        template[CommonKey.COMPANY_ID] = company_id
        await ensure_default_evaluate_period.ensure_default_evaluate_period([template])
        await HandlerAutoGenerateEvaluate().async_owner_do()
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.ADD_SUCCESS})
