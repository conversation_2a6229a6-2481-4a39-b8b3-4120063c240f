#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 11/08/2024
"""

from src.common.common import RedisSubscriberChannel
from src.redis.pubsub import publisher
from src.redis.pubsub.subscribers.send_bot_message_subscriber import (
    SendBotMessageSubscriber,
)


class SendNotiToUserController:

    @staticmethod
    def send_message_alert_lark(user, message_text):
        message = {
            "send_type": SendBotMessageSubscriber.SendType.SEND_BATCH_MESSAGES,
            "payload": {
                "open_ids": [user.open_user_id],
                "msg_type": "interactive",
                "card": {
                    "config": {"wide_screen_mode": True},
                    "elements": [
                        {
                            "tag": "div",
                            "text": {"content": f"{message_text}", "tag": "plain_text"},
                        }
                    ],
                    "header": {
                        "template": "blue",
                        "title": {"content": "Ladder", "tag": "plain_text"},
                    },
                },
            },
        }
        publisher.publish_message(channel=RedisSubscriberChannel.SEND_BOT_MESSAGE, message=message)
