import time
import uuid

from bson import ObjectId
from fastapi import Request
from sqlalchemy.orm import Session

from src.common.choices import (
    CompetencyFrameworkStatusChoice,
    LanguageChoice,
    QueryParamChoice,
    RoleChoice,
    StatusChoice,
    TypeCompetencyFrameworkChoice,
)
from src.common.common import (
    <PERSON><PERSON>ey,
    CompetencyFramework<PERSON>ey,
    <PERSON>luateKey,
    <PERSON>luatePeriod<PERSON>ey,
    JobTitleKey,
    JobTitleLevelKey,
    RedisSubscriberChannel,
)
from src.common.custom_exception import ConflictMessage, CustomError
from src.common.message import ErrorsMessage, SuccessMessage
from src.controllers.base_controller import BaseController
from src.models.mongo.competency_framework_model import CompetencyFrameworkModel
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.redis.pubsub import publisher
from src.repositories.department_repository import DepartmentRepository
from src.repositories.job_title_repository import JobTitleRepository
from src.repositories.users_repository import UserRepository
from src.schemas.pydantic.competency_framework_schema import (
    AddCompetencyFrameworkRequestSchema,
    ChangeStatusRequestSchema,
    CompetencyFrameworkFiltersRequestSchema,
    SaveDraftCompetencyFrameworkRequestSchema,
    UpdateCompetencyFrameworkRequestSchema,
)
from src.utils.attrs import generate_uuid_without_dash
from src.utils.time_helper import get_time_now, to_as_timezone, to_time_offset_aware


class CompetencyFrameworkController(BaseController):
    async def add_need_data_in_competency_groups(self, competency_groups):
        for group in competency_groups:
            competency_group_id = group.get(CompetencyFrameworkKey.COMPETENCY_GROUP_ID)
            lst_competency = group.get(CompetencyFrameworkKey.LST_COMPETENCY, [])
            if not competency_group_id:
                competency_group_id = generate_uuid_without_dash()

            for competency in lst_competency:
                competency_id = competency.get(CompetencyFrameworkKey.COMPETENCY_ID)
                if not competency_id:
                    competency_id = generate_uuid_without_dash()
                behavior_expressions = competency.get(CompetencyFrameworkKey.BEHAVIOR_EXPRESSIONS, [])
                min_item = min(behavior_expressions, key=lambda item: item["point_min"])
                # Lấy giá trị point_min từ dictionary đó
                min_point_key = min_item["point_min"]
                competency.update({CompetencyFrameworkKey.COMPETENCY_ID: competency_id, "point_min": min_point_key})
            group.update(
                {
                    CompetencyFrameworkKey.COMPETENCY_GROUP_ID: competency_group_id,
                    CompetencyFrameworkKey.LST_COMPETENCY: lst_competency,
                }
            )

        return competency_groups

    async def check_overlap_time_of_competency_framework(self, start_time, end_time, competency_frameworks):
        start_time = to_time_offset_aware(start_time)
        if end_time:
            end_time = to_time_offset_aware(end_time)
        for competency_framework in competency_frameworks:
            start_time_compare = to_time_offset_aware(competency_framework.get(CompetencyFrameworkKey.START_TIME))
            end_time_compare = competency_framework.get(CompetencyFrameworkKey.END_TIME)
            if end_time_compare:
                end_time_compare = to_time_offset_aware(end_time_compare)

            if not end_time:
                if not end_time_compare:
                    return True
                else:
                    if start_time_compare <= start_time <= end_time_compare:
                        return True
            else:
                if not end_time_compare and start_time_compare <= start_time <= end_time:
                    return True

                if start_time_compare <= start_time and end_time <= end_time_compare:
                    return True
                elif start_time_compare <= start_time <= end_time_compare <= end_time:
                    return True
                elif start_time <= start_time_compare <= end_time <= end_time_compare:
                    return True

        return False

    async def get_lst_job_title_level_by_job_title_ids(self, job_title_repo: JobTitleRepository, job_title_ids):
        lst_job_title_level = []
        for job_title_id in job_title_ids:
            job_title_levels = await job_title_repo.get_job_title_levels(job_title_id)
            for job_title_level in job_title_levels:
                lst_job_title_level.append(
                    {
                        JobTitleLevelKey.JOB_TITLE_LEVEL_ID: job_title_level.job_title_level_id,
                        JobTitleLevelKey.JOB_TITLE_LEVEL_NAME: job_title_level.name,
                    }
                )
        return lst_job_title_level

    async def get_competency_framework_data_to_push_notification(self, competency_groups):
        competency_framework_data = []

        for competency_group in competency_groups:
            competency_group_name = competency_group.get(CompetencyFrameworkKey.NAME, "")
            lst_competency = competency_group.get(CompetencyFrameworkKey.LST_COMPETENCY, [])
            for competency in lst_competency:
                data_insert = {}

                competency_name = competency.get(CompetencyFrameworkKey.NAME, "")
                behavior_expressions = competency.get(CompetencyFrameworkKey.BEHAVIOR_EXPRESSIONS, [])
                weight = competency.get(CompetencyFrameworkKey.WEIGHT, 0)
                job_title_levels = competency.get(CompetencyFrameworkKey.JOB_TITLE_LEVELS, [])

                behavior_expression_mapping = {}
                for behavior_expression in behavior_expressions:
                    level = behavior_expression.get(CompetencyFrameworkKey.LEVEL, 0)
                    point_min = behavior_expression.get(CompetencyFrameworkKey.POINT_MIN, 0)
                    is_activate = behavior_expression.get(CompetencyFrameworkKey.IS_ACTIVATE, False)

                    if is_activate:
                        behavior_expression_mapping.update({level: f"Mức {level} ({point_min} điểm)"})

                for job_title_level in job_title_levels:
                    job_title_level_id = job_title_level.get(CompetencyFrameworkKey.JOB_TITLE_LEVEL_ID, "")
                    behavior_expression_level = job_title_level.get(CompetencyFrameworkKey.BEHAVIOR_EXPRESSION_LEVEL, 0)

                    data_insert.update(
                        {job_title_level_id: behavior_expression_mapping.get(behavior_expression_level, "")}
                    )

                data_insert.update(
                    {
                        "competency_name": competency_name,
                        "competency_group_name": competency_group_name,
                        "weight": weight,
                    }
                )

                competency_framework_data.append(data_insert)
        return competency_framework_data

    async def add_competency_framework(
        self, request: Request, payload: AddCompetencyFrameworkRequestSchema, session: Session
    ):
        session_id = str(uuid.uuid4())
        func_name = self.add_competency_framework.__name__

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        company_id = await self.get_company_id_from_token(request)

        payload.created_by = payload.updated_by = await self.get_user_id_from_token(request)
        payload.created_time = payload.updated_time = get_time_now()

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        department_repo: DepartmentRepository = DepartmentRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        payload.start_time = to_as_timezone(payload.start_time)
        if payload.end_time:
            payload.end_time = to_as_timezone(payload.end_time)
            if payload.start_time >= payload.end_time:
                raise ConflictMessage(
                    self.get_lang(lang).get(ErrorsMessage.START_TIME_GREATER_THAN_END_TIME).get(CommonKey.MESSAGE)
                )

        competency_frameworks_active_of_exit_job_title = (
            await competency_framework_model.get_competency_frameworks_of_exist_job_title(
                company_id, payload.job_title_ids
            )
        )

        if await self.check_overlap_time_of_competency_framework(
            payload.start_time, payload.end_time, competency_frameworks_active_of_exit_job_title
        ):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.OVERLAPPING_APPLICATION_PERIOD_AND_TITLE).get(CommonKey.MESSAGE)
            )

        department = await department_repo.get_department_by_id(payload.department_id)
        if not department:
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.DEPARTMENT_IS_NOT_EXIST).get(CommonKey.MESSAGE))

        job_titles_in_department = await job_title_repo.get_job_titles(payload.department_id, page=-1)
        job_titles_ids = [job_title.job_title_id for job_title in job_titles_in_department]
        self.logger.info("{} :: job_titles_ids: {}".format(func_name, job_titles_ids))

        if set(payload.job_title_ids).difference(set(job_titles_ids)):
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_IS_NOT_EXIST).get(CommonKey.MESSAGE))

        payload = payload.dict()
        competency_groups = payload.pop(CompetencyFrameworkKey.COMPETENCY_GROUPS, [])
        status = payload.get(CompetencyFrameworkKey.STATUS)
        name = payload.get(CompetencyFrameworkKey.NAME)
        job_title_ids = payload.get(CompetencyFrameworkKey.JOB_TITLE_IDS, [])
        department_id = payload.get(CompetencyFrameworkKey.DEPARTMENT_ID)

        if status == CompetencyFrameworkStatusChoice.ACTIVE.value:
            await self.check_exit_job_tile_competency_framework(
                session,
                job_title_ids,
                payload.get(CompetencyFrameworkKey.START_TIME),
                payload.get(CompetencyFrameworkKey.END_TIME),
                None,
            )

        competency_groups = await self.add_need_data_in_competency_groups(competency_groups)
        if competency_groups:
            competency_groups = self.set_competency_groups_default_by_condition(competency_groups)
        payload.update(
            {
                CompetencyFrameworkKey.COMPETENCY_GROUPS: competency_groups,
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
                CommonKey.COMPANY_ID: company_id,
                CompetencyFrameworkKey.SESSION_ID: session_id,
            }
        )
        inserted_id = await competency_framework_model.insert_competency_framework(payload)
        self.logger.info("{} :: inserted".format(func_name))

        if status == CompetencyFrameworkStatusChoice.ACTIVE.value:

            lst_job_title_level = await self.get_lst_job_title_level_by_job_title_ids(job_title_repo, job_title_ids)
            competency_framework_data = await self.get_competency_framework_data_to_push_notification(competency_groups)

            message = {
                "list_job_title_level": lst_job_title_level,
                "department_id": department_id,
                "competency_framework_id": str(inserted_id),
                "competency_framework_name": name,
                "competency_framework_data": competency_framework_data,
            }
            publisher.publish_message(RedisSubscriberChannel.SYNC_BITABLE_COMPETENCY_FRAMEWORK_CHANNEL, message)

        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.ADD_SUCCESS,
                CommonKey.DATA: await self.json_encoder(
                    {
                        CompetencyFrameworkKey.ID: str(inserted_id),
                        CompetencyFrameworkKey.NAME: payload.get(CompetencyFrameworkKey.NAME),
                        CompetencyFrameworkKey.DEPARTMENT_ID: department.department_id,
                        CompetencyFrameworkKey.DEPARTMENT_NAME: department.name,
                        CompetencyFrameworkKey.JOB_TITLES: [
                            job_title.name
                            for job_title in await job_title_repo.get_job_titles(
                                payload.get(CompetencyFrameworkKey.DEPARTMENT_ID),
                                payload.get(CompetencyFrameworkKey.JOB_TITLE_IDS),
                            )
                        ],
                        CompetencyFrameworkKey.START_TIME: payload.get(CompetencyFrameworkKey.START_TIME),
                        CompetencyFrameworkKey.END_TIME: payload.get(CompetencyFrameworkKey.END_TIME),
                        CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
                        CompetencyFrameworkKey.SESSION_ID: session_id,
                    },
                ),
            }
        )

    async def update_competency_framework(
        self,
        request: Request,
        competency_framework_id,
        payload: UpdateCompetencyFrameworkRequestSchema,
        session: Session,
    ):

        func_name = self.add_competency_framework.__name__
        company_id = await self.get_company_id_from_token(request)

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        time_now = get_time_now()

        payload.updated_by = await self.get_user_id_from_token(request)
        payload.updated_time = time_now
        payload.start_time = to_as_timezone(payload.start_time)

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        department_repo: DepartmentRepository = DepartmentRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        detail_competency_framework = await competency_framework_model.get_one_competency_framework_by_id(
            competency_framework_id
        )

        if not detail_competency_framework:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        if payload.end_time:
            payload.end_time = to_as_timezone(payload.end_time)

            if payload.start_time >= payload.end_time:
                raise ConflictMessage(
                    self.get_lang(lang).get(ErrorsMessage.START_TIME_GREATER_THAN_END_TIME).get(CommonKey.MESSAGE)
                )

        department = await department_repo.get_department_by_id(payload.department_id)
        if not department:
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.DEPARTMENT_IS_NOT_EXIST).get(CommonKey.MESSAGE))

        job_titles_in_department = await job_title_repo.get_job_titles(payload.department_id, page=-1)
        job_titles_ids = [job_title.job_title_id for job_title in job_titles_in_department]

        if set(payload.job_title_ids).difference(set(job_titles_ids)):
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_IS_NOT_EXIST).get(CommonKey.MESSAGE))

        draft_id = detail_competency_framework.get(CompetencyFrameworkKey.DRAFT_ID)
        competency_framework_type = detail_competency_framework.get(CompetencyFrameworkKey.TYPE)

        payload = payload.dict()
        competency_groups = payload.pop(CompetencyFrameworkKey.COMPETENCY_GROUPS, [])
        status = payload.get(CompetencyFrameworkKey.STATUS)
        job_title_ids = payload.get(CompetencyFrameworkKey.JOB_TITLE_IDS, [])
        name = payload.get(CompetencyFrameworkKey.NAME)
        department_id = payload.get(CompetencyFrameworkKey.DEPARTMENT_ID)
        competency_groups = await self.add_need_data_in_competency_groups(competency_groups)
        if competency_groups:
            competency_groups = self.set_competency_groups_default_by_condition(competency_groups)

        payload.update({CompetencyFrameworkKey.COMPETENCY_GROUPS: competency_groups})

        if status == CompetencyFrameworkStatusChoice.ACTIVE.value:
            new_session_id = str(uuid.uuid4())

            payload.update(
                {
                    CompetencyFrameworkKey.TYPE: TypeCompetencyFrameworkChoice.ORIGIN.value,
                    CompetencyFrameworkKey.DRAFT_ID: None,
                    CompetencyFrameworkKey.EXIST_DRAFT: False,
                    CompetencyFrameworkKey.SESSION_ID: new_session_id,
                }
            )

            # Kiểm tra xem khung năng lực có phải là bản nháp không?

            competency_framework_type = detail_competency_framework.get(CompetencyFrameworkKey.TYPE)
            if competency_framework_type == TypeCompetencyFrameworkChoice.DRAFT.value:
                new_session_id = str(uuid.uuid4())

                # Đi tìm thằng chính
                original_id = detail_competency_framework.get("original_id")

                # Thay thế bằng thằng chính
                competency_framework_id = original_id
                # Xong thì xoá bản nháp
                await competency_framework_model.delete_competency_framework(draft_id)

            await self.check_exit_job_tile_competency_framework(
                session,
                job_title_ids,
                payload.get(CompetencyFrameworkKey.START_TIME),
                payload.get(CompetencyFrameworkKey.END_TIME),
                competency_framework_id,
            )

            lst_job_title_level = await self.get_lst_job_title_level_by_job_title_ids(job_title_repo, job_title_ids)
            competency_framework_data = await self.get_competency_framework_data_to_push_notification(competency_groups)

            message = {
                "list_job_title_level": lst_job_title_level,
                "department_id": department_id,
                "competency_framework_id": competency_framework_id,
                "competency_framework_name": name,
                "competency_framework_data": competency_framework_data,
            }
            publisher.publish_message(RedisSubscriberChannel.SYNC_BITABLE_COMPETENCY_FRAMEWORK_CHANNEL, message)

        matched_count = await competency_framework_model.update_competency_framework(competency_framework_id, payload)
        self.logger.debug("{} :: matched_count :: {}".format(func_name, matched_count))
        return {
            CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS,
            CommonKey.DATA: await self.json_encoder(
                {
                    CompetencyFrameworkKey.ID: competency_framework_id,
                    CompetencyFrameworkKey.NAME: payload.get(CompetencyFrameworkKey.NAME),
                    CompetencyFrameworkKey.DEPARTMENT_ID: department.department_id,
                    CompetencyFrameworkKey.DEPARTMENT_NAME: department.name,
                    CompetencyFrameworkKey.JOB_TITLES: [
                        job_title.name
                        for job_title in await job_title_repo.get_job_titles(
                            payload.get(CompetencyFrameworkKey.DEPARTMENT_ID),
                            payload.get(CompetencyFrameworkKey.JOB_TITLE_IDS),
                        )
                    ],
                    CompetencyFrameworkKey.START_TIME: payload.get(CompetencyFrameworkKey.START_TIME),
                    CompetencyFrameworkKey.END_TIME: payload.get(CompetencyFrameworkKey.END_TIME),
                    CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
                },
            ),
        }

    async def delete_competency_framework(self, request: Request, competency_framework_id):
        func_name = self.delete_competency_framework.__name__

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        company_id = await self.get_company_id_from_token(request)

        evaluate_model: EvaluateModel = EvaluateModel()
        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()

        competency_framework = await competency_framework_model.get_one_competency_framework_by_id(
            competency_framework_id
        )
        if not competency_framework:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        if await evaluate_model.get_evaluates_by_competency_framework(company_id, competency_framework_id):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_USED).get(CommonKey.MESSAGE)
            )

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()

        await competency_framework_model.delete_competency_framework(competency_framework_id)
        self.logger.debug("{} :: competency_framework :: {} :: deleted".format(func_name, competency_framework))
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.DELETE_SUCCESS})

    async def detail_competency_framework(self, request: Request, competency_framework_id):
        func_name = self.detail_competency_framework.__name__

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()

        competency_framework = await competency_framework_model.get_one_competency_framework_by_id(
            competency_framework_id
        )
        self.logger.info("{} :: competency_framework :: {}".format(func_name, competency_framework))
        if not competency_framework:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        if not competency_framework.get(CompetencyFrameworkKey.SESSION_ID):
            competency_framework[CompetencyFrameworkKey.SESSION_ID] = str(uuid.uuid4())

        return await self.json_encoder(
            {CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: competency_framework}
        )

    async def lst_competency_framework(
        self, request: Request, filters: CompetencyFrameworkFiltersRequestSchema, session: Session
    ):

        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = request.query_params.get(QueryParamChoice.ORDER, -1)
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        roles, _ = await self.get_roles_and_permissions_from_token(request)

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        # evaluate_time_model: EvaluateTimeModel = EvaluateTimeModel()
        department_repo: DepartmentRepository = DepartmentRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        user_repo: UserRepository = UserRepository(session)
        evaluate_model: EvaluateModel = EvaluateModel()

        user = await user_repo.get_user_by_id(account_id)

        lst_competency_framework, paging = await competency_framework_model.get_lst_competency_framework_in_system(
            company_id,
            filters.search,
            filters.competency_framework_filters,
            sort,
            order,
            page,
            per_page,
            roles,
            user.departments[0].department_id,
        )

        results = []

        time_now = get_time_now()

        for competency_framework in lst_competency_framework:
            time_start = time.time()
            department_name = ""
            job_title_names = []
            if not competency_framework.get(CompetencyFrameworkKey.SESSION_ID):
                competency_framework[CompetencyFrameworkKey.SESSION_ID] = str(uuid.uuid4())

            department_id = competency_framework.get(CompetencyFrameworkKey.DEPARTMENT_ID, "")
            job_title_ids = competency_framework.get(CompetencyFrameworkKey.JOB_TITLE_IDS, [])

            if department_id:
                department = await department_repo.get_department_by_id(department_id)
                if department:
                    department_name = department.name

            time_start = time.time()
            if job_title_ids:
                job_titles = await job_title_repo.get_job_titles(department_id, job_title_ids)
                if job_titles:
                    job_title_names = [job_title.name for job_title in job_titles]

            time_start = time.time()
            evaluate_time_start = None
            evaluate_time_end = None
            competency_framework_status = competency_framework.get(CompetencyFrameworkKey.STATUS)
            if competency_framework_status != StatusChoice.EVALUATE_DRAFT.value:
                # get evaluate in range time by department_id

                evaluate_time = await evaluate_model.get_evaluate_by_department_id(company_id, department_id, time_now)
                if evaluate_time:
                    evaluate_time_id = evaluate_time.get(EvaluateKey.EVALUATE_PERIOD_ID)
                    if evaluate_time_id:
                        evaluate_period = await EvaluatePeriodModel().find_one(
                            {EvaluatePeriodKey.ID: ObjectId(evaluate_time_id)}
                        )
                        if evaluate_period:
                            evaluate_time_start = evaluate_period.get(EvaluatePeriodKey.START_TIME)
                            evaluate_time_end = evaluate_period.get(EvaluatePeriodKey.END_TIME)

            item = {
                CompetencyFrameworkKey.ID: str(competency_framework.get(CompetencyFrameworkKey.ID)),
                CompetencyFrameworkKey.NAME: competency_framework.get(CompetencyFrameworkKey.NAME),
                CompetencyFrameworkKey.DEPARTMENT_ID: department_id,
                CompetencyFrameworkKey.DEPARTMENT_NAME: department_name,
                CompetencyFrameworkKey.JOB_TITLES: job_title_names,
                CompetencyFrameworkKey.START_TIME: competency_framework.get(CompetencyFrameworkKey.START_TIME),
                CompetencyFrameworkKey.END_TIME: competency_framework.get(CompetencyFrameworkKey.END_TIME),
                CompetencyFrameworkKey.STATUS: competency_framework.get(CompetencyFrameworkKey.STATUS),
                CompetencyFrameworkKey.EXIST_DRAFT: competency_framework.get(CompetencyFrameworkKey.EXIST_DRAFT, False),
                CompetencyFrameworkKey.SESSION_ID: competency_framework.get(CompetencyFrameworkKey.SESSION_ID),
                CompetencyFrameworkKey.DRAFT_ID: str(competency_framework.get(CompetencyFrameworkKey.DRAFT_ID)),
                CompetencyFrameworkKey.EVALUATE_TIME_START: evaluate_time_start,
                CompetencyFrameworkKey.EVALUATE_TIME_END: evaluate_time_end,
            }
            if not competency_framework.get(CompetencyFrameworkKey.SESSION_ID):
                competency_framework[CompetencyFrameworkKey.SESSION_ID] = str(uuid.uuid4())

            results.append(item)
        return {
            CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
            CommonKey.DATA: await self.json_encoder(results),
            CommonKey.PAGING: paging,
        }

    def set_competency_groups_default_by_condition(self, competency_groups):
        for competency_group in competency_groups:
            if competency_group.get("is_default"):
                continue
            lst_competency = competency_group.get("lst_competency")
            if lst_competency:
                for competency in lst_competency:
                    if competency.get("competency_reference_id"):
                        competency["is_default"] = True
                    if competency.get("competency_default_id") and competency.get("is_default_apply"):
                        competency["competency_reference_id"] = competency.get("competency_default_id")

        return competency_groups

    async def save_draft_competency_framework(
        self, request: Request, payload: SaveDraftCompetencyFrameworkRequestSchema, session: Session
    ):
        func_name = self.save_draft_competency_framework.__name__

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        payload.updated_by = account_id
        payload.updated_time = get_time_now()

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        department_repo: DepartmentRepository = DepartmentRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        competency_framework_current = await competency_framework_model.get_one_competency_framework_by_id(
            payload.competency_framework_id
        )
        self.logger.info("{} :: competency_framework :: {}".format(func_name, competency_framework_current))

        department_name = ""
        job_title_names = []

        json_payload = payload.dict(exclude_none=True, exclude={"competency_framework_id"})

        competency_groups = json_payload.get("competency_groups")
        if competency_groups:
            json_payload["competency_groups"] = self.set_competency_groups_default_by_condition(competency_groups)

        data_in_payload = {
            **json_payload,
            CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT.value,
            CommonKey.COMPANY_ID: company_id,
        }

        if competency_framework_current:
            await competency_framework_model.update_competency_framework(
                payload.competency_framework_id,
                data_in_payload,
            )
            self.logger.debug(
                "{} :: payload :: {} :: updated".format(
                    func_name,
                    data_in_payload,
                )
            )
        else:
            payload.created_by = payload.updated_by
            payload.created_time = payload.updated_time
            inserted_id = await competency_framework_model.insert_competency_framework(data_in_payload)
            payload.competency_framework_id = str(inserted_id)

        if payload.department_id:
            department = await department_repo.get_department_by_id(payload.department_id)
            if not department:
                raise ConflictMessage(
                    self.get_lang(lang).get(ErrorsMessage.DEPARTMENT_IS_NOT_EXIST).get(CommonKey.MESSAGE)
                )
            department_name = department.name

        if payload.job_title_ids:
            job_titles = await job_title_repo.get_job_titles(payload.department_id, payload.job_title_ids)
            if not job_titles:
                raise ConflictMessage(
                    self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_IS_NOT_EXIST).get(CommonKey.MESSAGE)
                )
            job_title_names.extend([job_title.name for job_title in job_titles])

        self.logger.debug("{} :: payload :: {} :: inserted".format(func_name, payload))
        return {
            CommonKey.MESSAGE: SuccessMessage.ADD_SUCCESS,
            CommonKey.DATA: await self.json_encoder(
                {
                    CompetencyFrameworkKey.ID: payload.competency_framework_id,
                    CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT.value,
                    CompetencyFrameworkKey.NAME: payload.name if payload.name else "",
                    CompetencyFrameworkKey.DEPARTMENT_ID: payload.department_id if payload.department_id else "",
                    CompetencyFrameworkKey.DEPARTMENT_NAME: department_name,
                    CompetencyFrameworkKey.JOB_TITLES: job_title_names,
                    CompetencyFrameworkKey.START_TIME: payload.start_time if payload.start_time else "",
                    CompetencyFrameworkKey.END_TIME: payload.end_time if payload.end_time else "",
                }
            ),
        }

    async def save_draft_competency_framework_active(
        self,
        request: Request,
        competency_framework_id: str,
        payload: SaveDraftCompetencyFrameworkRequestSchema,
        session: Session,
    ):
        func_name = self.save_draft_competency_framework.__name__

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        payload.updated_by = account_id
        payload.updated_time = get_time_now()

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        department_repo: DepartmentRepository = DepartmentRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        competency_framework_current = await competency_framework_model.get_one_competency_framework_by_id(
            competency_framework_id
        )

        if not competency_framework_current:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        if (
            competency_framework_current.get(CompetencyFrameworkKey.STATUS)
            != CompetencyFrameworkStatusChoice.ACTIVE.value
        ):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_ACTIVE).get(CommonKey.MESSAGE)
            )

        draft_id = competency_framework_current.get(CompetencyFrameworkKey.DRAFT_ID)

        self.logger.info("{} :: competency_framework :: {}".format(func_name, competency_framework_current))

        department_name = ""
        job_title_names = []

        new_session_id = str(uuid.uuid4())

        # Cập nhật thông tin vào bản nháp, nếu không có thì tạo bản nháp
        json_payload = payload.dict(exclude_none=True, exclude={"competency_framework_id"})

        competency_groups = json_payload.get("competency_groups")
        if competency_groups:
            json_payload["competency_groups"] = self.set_competency_groups_default_by_condition(competency_groups)

        data_in_payload = {
            **json_payload,
            CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT.value,
            CommonKey.COMPANY_ID: company_id,
            CompetencyFrameworkKey.SESSION_ID: new_session_id,
            CompetencyFrameworkKey.TYPE: TypeCompetencyFrameworkChoice.DRAFT.value,
            "original_id": competency_framework_id,
        }

        if draft_id:
            await competency_framework_model.update_competency_framework(
                draft_id,
                data_in_payload,
            )
            self.logger.debug(
                "{} :: payload :: {} :: updated".format(
                    func_name,
                    data_in_payload,
                )
            )
        else:
            payload.created_by = payload.updated_by
            payload.created_time = payload.updated_time
            draft_id = await competency_framework_model.insert_competency_framework(data_in_payload)
        if payload.department_id:
            department = await department_repo.get_department_by_id(payload.department_id)
            if not department:
                raise ConflictMessage(
                    self.get_lang(lang).get(ErrorsMessage.DEPARTMENT_IS_NOT_EXIST).get(CommonKey.MESSAGE)
                )
            department_name = department.name

        if payload.job_title_ids:
            job_titles = await job_title_repo.get_job_titles(payload.department_id, payload.job_title_ids)
            if not job_titles:
                raise ConflictMessage(
                    self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_IS_NOT_EXIST).get(CommonKey.MESSAGE)
                )
            job_title_names.extend([job_title.name for job_title in job_titles])

        self.logger.debug("{} :: payload :: {} :: inserted".format(func_name, payload))

        # Cập nhật thông tin vào bản chính
        data_update_original = {
            CompetencyFrameworkKey.DRAFT_ID: draft_id,
            CompetencyFrameworkKey.EXIST_DRAFT: True,
            CompetencyFrameworkKey.SESSION_ID: new_session_id,
            CompetencyFrameworkKey.UPDATED_TIME: get_time_now(),
        }

        status_original = await competency_framework_model.update_competency_framework(
            competency_framework_id,
            data_update_original,
        )
        self.logger.debug("{} :: status_original :: {}".format(func_name, status_original))

        return {
            CommonKey.MESSAGE: SuccessMessage.ADD_SUCCESS,
            CommonKey.DATA: await self.json_encoder(
                {
                    CompetencyFrameworkKey.ID: competency_framework_id,
                    CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT.value,
                    CompetencyFrameworkKey.NAME: payload.name if payload.name else "",
                    CompetencyFrameworkKey.DEPARTMENT_ID: payload.department_id if payload.department_id else "",
                    CompetencyFrameworkKey.DEPARTMENT_NAME: department_name,
                    CompetencyFrameworkKey.JOB_TITLES: job_title_names,
                    CompetencyFrameworkKey.START_TIME: payload.start_time if payload.start_time else "",
                    CompetencyFrameworkKey.END_TIME: payload.end_time if payload.end_time else "",
                    CompetencyFrameworkKey.DRAFT_ID: draft_id,
                    CompetencyFrameworkKey.EXIST_DRAFT: True,
                    CompetencyFrameworkKey.SESSION_ID: new_session_id,
                }
            ),
        }

    async def get_status_competency_framework(
        self, request: Request, filters: CompetencyFrameworkFiltersRequestSchema, session: Session
    ):
        func_name = self.get_status_competency_framework.__name__

        roles, _ = await self.get_roles_and_permissions_from_token(request)
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        user_repo: UserRepository = UserRepository(session)
        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()

        department_id = ""
        if roles[0] in [RoleChoice.LEADER.value, RoleChoice.USER.value] and RoleChoice.ADMIN.value not in roles:
            user = await user_repo.get_user_by_id(account_id)
            department_id = user.departments[0].department_id

        results = [
            {
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
            },
            {
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT.value,
            },
            {
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.INACTIVE.value,
            },
        ]

        for result in results:
            status = result[CompetencyFrameworkKey.STATUS]

            query = {
                CompetencyFrameworkKey.STATUS: status,
                CommonKey.COMPANY_ID: company_id,
            }
            if status == CompetencyFrameworkStatusChoice.DRAFT.value:
                query[CompetencyFrameworkKey.TYPE] = {"$ne": TypeCompetencyFrameworkChoice.DRAFT.value}

            data_result, _ = await competency_framework_model.get_lst_competency_framework_in_system(
                company_id,
                filters.search,
                filters.competency_framework_filters,
                page=-1,
                roles=roles,
                department_id=department_id,
                query=query,
            )
            result[CompetencyFrameworkKey.COUNT] = len(data_result) if data_result else 0

        self.logger.info("{} :: lst_competency_framework :: {}".format(func_name, results))
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: results})

    async def change_status_competency_framework(
        self, competency_framework_id, request: Request, payload: ChangeStatusRequestSchema, session: Session
    ):
        func_name = self.change_status_competency_framework.__name__
        account_id = await self.get_user_id_from_token(request)
        body = payload.dict(exclude_none=True)

        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        payload.updated_time = get_time_now()
        payload.updated_by = account_id

        competency_framework = await competency_framework_model.get_one_competency_framework_by_id(
            competency_framework_id
        )
        self.logger.info("{} :: competency_framework :: {}".format(func_name, competency_framework))
        if not competency_framework:
            raise ConflictMessage(
                self.get_lang(LanguageChoice.VI.value)
                .get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_EXIST)
                .get(CommonKey.MESSAGE)
            )

        job_title_ids = competency_framework.get(JobTitleKey.JOB_TITLE_IDS)
        start_time = competency_framework.get("start_time")
        end_time = competency_framework.get("end_time")
        if job_title_ids and body.get("status") and body.get("status") == CompetencyFrameworkStatusChoice.ACTIVE.value:
            await self.check_exit_job_tile_competency_framework(
                session, job_title_ids, start_time, end_time, competency_framework_id
            )

        await competency_framework_model.update_competency_framework(
            competency_framework_id, payload.dict(exclude_none=True)
        )
        self.logger.info("{} :: payload :: {}".format(func_name, payload))
        return {CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS}

    async def check_exit_job_tile_competency_framework(
        self, session, job_title_ids, start_time, end_time, competency_framework_id
    ):
        start_time = to_as_timezone(start_time)
        end_time = to_as_timezone(end_time)
        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()
        competency_frameworks = (
            await competency_framework_model.get_competency_frameworks_by_job_title_ids_diff_cf_current(
                job_title_ids, start_time, end_time, competency_framework_id
            )
        )
        for competency_framework in competency_frameworks:
            # name = competency_framework.get("name")
            st = to_as_timezone(competency_framework.get("start_time"))
            et = to_as_timezone(competency_framework.get("end_time"))

            if ((not end_time and not et) and (start_time > st)) or (et and start_time <= et):

                job_title_repo: JobTitleRepository = JobTitleRepository(session)
                job_title_names = ""
                job_titles = await job_title_repo.get_job_titles(
                    competency_framework.get("department_id"), job_title_ids
                )
                if job_titles:
                    job_title_names = [job_title.name for job_title in job_titles]
                data_response = {
                    "name": competency_framework.get("name"),
                    "start_time": competency_framework.get("start_time"),
                    "end_time": competency_framework.get("end_time"),
                    "job_titles": job_title_names,
                }
                raise CustomError(
                    message="Đã tồn tại Khung năng lực với chức danh tương ứng",
                    data=await self.json_encoder(data_response),
                )
        return
