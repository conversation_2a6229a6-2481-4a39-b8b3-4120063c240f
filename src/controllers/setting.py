#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 19/08/2024
"""

import asyncio

import nest_asyncio

from src.models.mongo.setting_model import SettingModel


class SettingController:

    def get_setting(self):
        return self._async_get_setting()

    def _async_get_setting(self):
        setting_model: SettingModel = SettingModel()
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:  # no event loop running:
            loop = asyncio.new_event_loop()
            return loop.run_until_complete(setting_model.find_one({}))
        else:

            nest_asyncio.apply(loop)
            return asyncio.run(setting_model.find_one({}))
