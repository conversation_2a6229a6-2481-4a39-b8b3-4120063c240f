import datetime
import json
import os

from bson import ObjectId
from fastapi import Request
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from configs import ApplicationConfig
from src.common.choices import (
    CompetencyFrameworkStatusChoice,
    EvaluateStatusChoice,
    EvaluateStatusFilterChoice,
    LanguageChoice,
    QueryParamChoice,
    RoleChoice,
    StatusChoice,
    UserEvaluateTypeChoice,
)
from src.common.common import (
    <PERSON><PERSON><PERSON>,
    Competency<PERSON><PERSON><PERSON>ork<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>luatePeriod<PERSON><PERSON>,
    JobLevelStatusKey,
    MessagePushSyncEvaluateKey,
    ParamKey,
    RedisSubscriberChannel,
    UserKey,
)
from src.common.constants import ConstantTypeOfReview
from src.common.custom_exception import AccessDenied, ConflictMessage, CustomError
from src.common.message import ErrorsMessage, SuccessMessage
from src.controllers.base_controller import BaseController
from src.helpers.export.process_export_excel import gen_data_export_evaluate_to_excel
from src.models.mongo.competency_framework_model import CompetencyFrameworkModel
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.task_performance_model import TaskPerformanceModel
from src.redis.pubsub import publisher
from src.repositories.department_repository import DepartmentRepository
from src.repositories.job_title_level_repository import JobTitleLevelRepository
from src.repositories.job_title_repository import JobTitleRepository
from src.repositories.users_repository import UserRepository
from src.schemas.pydantic.evaluate_schemas import (
    AddEvaluateRequestSchema,
    EvaluateFiltersRequestSchema,
    SaveDraftEvaluateRequestSchema,
    UpdateEvaluateRequestSchema,
)
from src.utils.time_helper import get_time_now, to_as_timezone


class EvaluateController(BaseController):
    async def get_user_filters(self, user_repo, company_id, filters):
        results = []
        if filters.search:
            users, _ = await user_repo.get_users(company_id=company_id, search=filters.search, page=-1)
            if users:
                results.extend([user.user_id for user in users])
        return results

    async def check_update_status_when_leader_submit(self, time_eval_of_users):
        is_completed = False
        for time_eval_of_user in time_eval_of_users:
            user_type = time_eval_of_user.user_type
            submit_time = time_eval_of_user.submit_time

            if user_type == UserEvaluateTypeChoice.LEADER.value and submit_time:
                is_completed = True

        return is_completed

    async def get_evaluate_data_exports(self, competency_groups):
        data_exports = []
        for competency_group in competency_groups:
            competency_group_name = competency_group.get(EvaluateKey.NAME)
            lst_competency = competency_group.get(EvaluateKey.LST_COMPETENCY, [])

            for competency in lst_competency:
                data = {}
                points = competency.get(EvaluateKey.POINTS, [])
                competency_name = competency.get(EvaluateKey.NAME)
                weight = competency.get(EvaluateKey.WEIGHT, 0)
                point_min = competency.get(EvaluateKey.POINT_MIN, 0)

                for point in points:
                    user_type = point.get(EvaluateKey.USER_TYPE)
                    point = point.get(EvaluateKey.POINT, None)

                    if user_type == UserEvaluateTypeChoice.OWNER.value:
                        data.update(
                            {
                                EvaluateKey.USER_POINT: point,
                                EvaluateKey.POINT_ACHIEVED_BY_USER: point * weight if point else None,
                            }
                        )
                    elif user_type == UserEvaluateTypeChoice.LEADER.value:
                        data.update(
                            {
                                EvaluateKey.LEADER_POINT: point,
                                EvaluateKey.POINT_ACHIEVED_BY_LEADER: point * weight if point else None,
                            }
                        )

                point_achieved_by_user = data.get(EvaluateKey.POINT_ACHIEVED_BY_USER, None)
                user_point = data.get(EvaluateKey.USER_POINT, None)
                leader_point = data.get(EvaluateKey.LEADER_POINT, None)
                point_achieved_by_leader = data.get(EvaluateKey.POINT_ACHIEVED_BY_LEADER, None)
                point_min_of_level = weight * point_min

                data_insert = {
                    "Nhóm năng lực": competency_group_name,
                    "Năng lực": competency_name,
                    "Trọng số": weight,
                    "Điểm tối thiểu theo cấp độ": point_min,
                    "Điểm tự đánh giá": user_point,
                    "Điểm quản lí đánh giá": leader_point,
                    "Điểm tối thiểu cần đạt được": point_min_of_level,
                    "Điểm đạt được khi tự đánh giá": point_achieved_by_user,
                    "Điểm đạt được khi leader đánh giá": point_achieved_by_leader,
                    "Điểm GAP": point_achieved_by_leader - point_min_of_level if point_achieved_by_leader else None,
                }

                data_exports.append(data_insert)

        return data_exports

    async def add_user_info_to_evaluate(self, evaluates, user_repo, job_title_repo, job_title_level_repo):
        data_return = []

        for evaluate in evaluates:
            user_id = evaluate.get(EvaluateKey.USER_ID)
            evaluate_id = str(evaluate.get(EvaluateKey.ID))
            evaluate_period_id = evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)
            time_eval_of_users = evaluate.get(EvaluateKey.TIME_EVAL_OF_USERS, [])
            status = evaluate.get(EvaluateKey.STATUS)
            is_draft = evaluate.get(EvaluateKey.IS_DRAFT, False)
            after_job_title_level_id = evaluate.get(EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID)
            before_job_title_level_id = evaluate.get(EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID)

            user = await user_repo.get_user_by_id(user_id)
            leader = await user_repo.get_user_by_id(user.leader_user_id)
            job_title = await job_title_repo.get_job_title_by_id(user.job_title_id)
            after_job_title_level = await job_title_level_repo.get_job_title_level_by_id(after_job_title_level_id)
            before_job_title_level = await job_title_level_repo.get_job_title_level_by_id(before_job_title_level_id)
            job_title_level_status = evaluate.get(EvaluateKey.JOB_TITLE_LEVEL_STATUS)

            # Required field evaluate
            evaluate_data = {
                EvaluateKey.ID: evaluate_id,
                EvaluateKey.EVALUATE_PERIOD_ID: evaluate_period_id,
                EvaluateKey.USER_ID: user_id,
                EvaluateKey.USER_NAME: user.name,
                EvaluateKey.USER_AVATAR: user.thumb_avatar_link,
                EvaluateKey.STATUS: status,
                EvaluateKey.IS_DRAFT: is_draft,
                EvaluateKey.DEPARTMENT_ID: user.departments[0].department_id,
                EvaluateKey.DEPARTMENT_NAME: user.departments[0].name,
                EvaluateKey.JOB_TITLE: job_title.name,
                EvaluateKey.AFTER_JOB_TITLE_LEVEL: after_job_title_level.name if after_job_title_level else "",
                EvaluateKey.BEFORE_JOB_TITLE_LEVEL: before_job_title_level.name if before_job_title_level else "",
                EvaluateKey.TIME_EVAL_OF_USERS: time_eval_of_users,
                EvaluateKey.JOB_TITLE_LEVEL_STATUS: job_title_level_status,
                EvaluateKey.TYPE_OF_REVIEW: evaluate.get(EvaluateKey.TYPE_OF_REVIEW, ConstantTypeOfReview.PERIODIC),
                "evaluate_start_time": (
                    evaluate.get(EvaluateKey.START_TIME).strftime("%Y-%m-%dT%H:%MZ")
                    if evaluate.get(EvaluateKey.START_TIME)
                    else None
                ),
                "evaluate_end_time": (
                    evaluate.get(EvaluateKey.END_TIME).strftime("%Y-%m-%dT%H:%MZ")
                    if evaluate.get(EvaluateKey.END_TIME)
                    else None
                ),
            }

            if leader:
                evaluate_data.update(
                    {
                        EvaluateKey.LEADER_ID: leader.user_id,
                        EvaluateKey.LEADER_NAME: leader.name,
                        EvaluateKey.LEADER_AVATAR: leader.thumb_avatar_link,
                    }
                )
            data_return.append(evaluate_data)
        return data_return

    async def add_user_info_to_evaluates_managed_team(
        self, data_evaluates, user_repo, job_title_repo, job_title_level_repo
    ):
        data_return = []

        for evaluate in data_evaluates:
            user_id = evaluate.get("_id")
            statuses = evaluate.get("statuses")
            number_evaluate_of_complete = 0
            number_evaluate_of_waiting = 0

            for item_status in statuses:
                if item_status.get("status") == EvaluateStatusChoice.COMPLETED.value:
                    number_evaluate_of_complete += item_status.get("count", 0)
                if item_status.get("status") == EvaluateStatusChoice.WAITING.value:
                    number_evaluate_of_waiting += item_status.get("count", 0)

            user = await user_repo.get_user_by_id(user_id)
            leader = await user_repo.get_user_by_id(user.leader_user_id)
            job_title = await job_title_repo.get_job_title_by_id(user.job_title_id)
            job_title_level = await job_title_level_repo.get_job_title_level_by_id(user.job_title_level_id)
            # Required field evaluate
            evaluate_data = {
                EvaluateKey.USER_ID: user_id,
                EvaluateKey.USER_NAME: user.name,
                EvaluateKey.USER_AVATAR: user.thumb_avatar_link,
                EvaluateKey.DEPARTMENT_ID: user.departments[0].department_id,
                EvaluateKey.DEPARTMENT_NAME: user.departments[0].name,
                EvaluateKey.JOB_TITLE: job_title.name,
                "number_evaluate_of_complete": number_evaluate_of_complete,
                "number_evaluate_of_waiting": number_evaluate_of_waiting,
                "job_title_level": job_title_level.name if job_title_level else "",
            }

            if leader:
                evaluate_data.update(
                    {
                        EvaluateKey.LEADER_ID: leader.user_id,
                        EvaluateKey.LEADER_NAME: leader.name,
                        EvaluateKey.LEADER_AVATAR: leader.thumb_avatar_link,
                    }
                )
            data_return.append(evaluate_data)
        return data_return

    async def get_point_min_for_competencies(self, competency_groups, map_job_title_level_id):
        results = {}
        for competency_group in competency_groups:
            lst_competency = competency_group.get(CompetencyFrameworkKey.LST_COMPETENCY, [])
            for competency in lst_competency:
                competency_id = competency.get(CompetencyFrameworkKey.COMPETENCY_ID)
                behaviors_expressions = competency.get(CompetencyFrameworkKey.BEHAVIOR_EXPRESSIONS, [])
                job_title_levels = competency.get(CompetencyFrameworkKey.JOB_TITLE_LEVELS, [])

                for job_title_level in job_title_levels:
                    job_title_level_id = job_title_level.get(CompetencyFrameworkKey.JOB_TITLE_LEVEL_ID)
                    behavior_expression_level = job_title_level.get(CompetencyFrameworkKey.BEHAVIOR_EXPRESSION_LEVEL)

                    if job_title_level_id == map_job_title_level_id:
                        point_min = 0
                        behaviors_expression = next(
                            filter(
                                lambda x: x.get(CompetencyFrameworkKey.LEVEL) == behavior_expression_level,
                                behaviors_expressions,
                            ),
                            None,
                        )
                        if behaviors_expression:
                            point_min = behaviors_expression.get(CompetencyFrameworkKey.POINT_MIN)
                        results.update({competency_id: point_min})
        return results

    async def get_evaluate_data(self, competency_groups):
        evaluate_data = []
        for competency_group in competency_groups:
            for competency in competency_group.lst_competency:
                competency_data = {
                    MessagePushSyncEvaluateKey.NAME: competency.name,
                    MessagePushSyncEvaluateKey.COMPETENCY_GROUP_NAME: competency_group.name,
                    CompetencyFrameworkKey.WEIGHT: competency.weight,
                    CompetencyFrameworkKey.POINT_MIN: competency.point_min,
                    EvaluateKey.POINTS: [
                        {
                            EvaluateKey.USER_ID: eval_point.user_id,
                            EvaluateKey.USER_TYPE: eval_point.user_type,
                            EvaluateKey.POINT: eval_point.point,
                        }
                        for eval_point in competency.points
                    ],
                }
                evaluate_data.append(competency_data)
        return evaluate_data

    async def get_user_ids_for_abac(self, user_repo, roles, account_id):
        user_ids_for_abac = [account_id]
        if RoleChoice.LEADER.value in roles:
            users_of_management = await user_repo.get_users_by_leader_id(account_id)
            user_ids_for_abac.extend([user.user_id for user in users_of_management])
        if RoleChoice.USER.value in roles:
            pass
        if RoleChoice.ADMIN.value in roles:
            user_ids_for_abac = []
        return user_ids_for_abac

    async def add_evaluate(self, request: Request, payload: AddEvaluateRequestSchema, session: Session):
        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        payload.created_by = payload.updated_by = account_id
        payload.created_time = payload.updated_time = get_time_now()

        user_repo: UserRepository = UserRepository(session)
        evaluate_model: EvaluateModel = EvaluateModel()
        evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()

        user = await user_repo.get_user_by_id(payload.user_id)
        if not user:
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.USER_IS_NOT_EXIST).get(CommonKey.MESSAGE))
        self.logger.info("{} :: user name :: {}".format(self.add_evaluate.__name__, user.name))

        evaluate_period = await evaluate_period_model.get_evaluate_period_by_id(payload.evaluate_period_id)
        self.logger.info("{} :: evaluate_period :: {}".format(self.add_evaluate.__name__, evaluate_period))
        if not evaluate_period:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.EVALUATE_PERIOD_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )
        start_time = to_as_timezone(evaluate_period.get(EvaluatePeriodKey.START_TIME))
        end_time = to_as_timezone(evaluate_period.get(EvaluatePeriodKey.END_TIME))

        if not evaluate_period or payload.created_time > end_time or payload.created_time < start_time:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.EVALUATE_PERIOD_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        competency_framework = await competency_framework_model.get_one_competency_framework_by_id(
            payload.competency_framework_id
        )
        self.logger.info("{} :: competency_framework :: {}".format(self.add_evaluate.__name__, competency_framework))
        if not competency_framework:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        competency_framework_status = competency_framework.get(CompetencyFrameworkKey.STATUS)
        if competency_framework_status != CompetencyFrameworkStatusChoice.ACTIVE.value:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_ACTIVE).get(CommonKey.MESSAGE)
            )

        payload = payload.dict()
        payload.update(
            {
                CommonKey.COMPANY_ID: company_id,
                EvaluateKey.DEPARTMENT_ID: user.departments[0].department_id,
                EvaluateKey.STATUS: EvaluateStatusChoice.WAITING.value,
                EvaluateKey.START_TIME: start_time,
                EvaluateKey.END_TIME: end_time,
                EvaluateKey.IS_DRAFT: False,
                EvaluateKey.COMPETENCY_GROUPS: [],
                EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID: user.job_title_level_id,
                EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID: "",
                EvaluateKey.AMOUNT_OF_WORK_NOTE: "",
                EvaluateKey.USER_NOTE: "",
                EvaluateKey.HR_REVIEW: "",
                EvaluateKey.LEADER_REVIEW: "",
                EvaluateKey.WORK_PROCESS_NOTE: "",
                EvaluateKey.STATUS_FILTER: EvaluateStatusFilterChoice.WAITING_USER.value,
            }
        )
        await evaluate_model.insert_evaluate(payload)
        self.logger.debug("{} :: evaluate :: {} :: inserted".format(self.add_evaluate.__name__, payload))
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.ADD_SUCCESS})

    async def calculate_point_after_job_level_id(self, competency_groups, before_job_title_level_id):

        job_title_level_user_evaluate = None
        job_title_level_leader_evaluate = None
        job_title_level_status = None

        mapping_job_title_point = {}
        user_points = 0
        leader_points = 0
        points_job_title_level_current = 0
        for competency_group in competency_groups:
            for competency in competency_group.lst_competency:
                weight = competency.weight

                behavior_expressions = competency.behavior_expressions
                mapping_level_to_point = {}
                for behavior_expression in behavior_expressions:
                    level = behavior_expression.level
                    point_min = behavior_expression.point_min
                    mapping_level_to_point[level] = point_min

                for job_title_level in competency.job_title_levels:
                    job_title_level_id = job_title_level.get("job_title_level_id")
                    behavior_expression_level = job_title_level.get("behavior_expression_level")

                    if behavior_expression_level == -1:
                        continue
                    if mapping_job_title_point.get(job_title_level_id):
                        mapping_job_title_point[job_title_level_id] += (
                            mapping_level_to_point.get(behavior_expression_level, 0) * weight
                        )
                    else:
                        mapping_job_title_point[job_title_level_id] = (
                            mapping_level_to_point.get(behavior_expression_level, 0) * weight
                        )

                # tính điểm của nhân viên
                points = competency.points
                for point in points:
                    user_type = point.user_type
                    point_value = point.point * weight
                    if user_type == 1:
                        user_points += point_value
                    elif user_type == 2:
                        leader_points += point_value

        # Sort mapping_job_title_point theo value giảm dần
        mapping_job_title_point = dict(sorted(mapping_job_title_point.items(), key=lambda item: item[1], reverse=False))
        mapping_job_title_point_title = list(mapping_job_title_point.keys())
        job_title_level_leader_evaluate = mapping_job_title_point_title[0] if mapping_job_title_point_title else None
        job_title_level_user_evaluate = mapping_job_title_point_title[0] if mapping_job_title_point_title else None

        for job_title_level_id, point_value in mapping_job_title_point.items():
            if job_title_level_id == before_job_title_level_id:
                points_job_title_level_current = point_value
            if user_points >= point_value:
                job_title_level_user_evaluate = job_title_level_id
            if leader_points >= point_value:
                job_title_level_leader_evaluate = job_title_level_id
        if leader_points > points_job_title_level_current:
            job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_INCREASE
        elif leader_points < points_job_title_level_current:
            job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_DECREASE
        else:
            job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_SAME

        return job_title_level_user_evaluate, job_title_level_leader_evaluate, job_title_level_status

    async def update_evaluate(
        self, request: Request, evaluate_id: str, payload: UpdateEvaluateRequestSchema, session: Session
    ):
        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        account_id = await self.get_user_id_from_token(request)

        payload.updated_by = account_id
        payload.updated_time = get_time_now()

        user_repo: UserRepository = UserRepository(session)
        evaluate_model: EvaluateModel = EvaluateModel()
        competency_framework_model: CompetencyFrameworkModel = CompetencyFrameworkModel()

        evaluate = await evaluate_model.get_evaluate_current_for_user(evaluate_id)
        self.logger.info("{} :: evaluate :: {}".format(self.update_evaluate.__name__, evaluate))

        if not evaluate:
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.EVALUATE_IS_NOT_EXIST).get(CommonKey.MESSAGE))

        start_time = to_as_timezone(evaluate.get(EvaluateKey.START_TIME))
        end_time = to_as_timezone(evaluate.get(EvaluateKey.END_TIME))
        competency_framework_id = evaluate.get(EvaluateKey.COMPETENCY_FRAMEWORK_ID)
        user_id = evaluate.get(EvaluateKey.USER_ID)
        status = evaluate.get(EvaluateKey.STATUS)
        before_job_title_level_id = evaluate.get(EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID)

        is_completed = await self.check_update_status_when_leader_submit(payload.time_eval_of_users)
        self.logger.info("{} :: is_completed :: {}".format(self.update_evaluate.__name__, is_completed))
        user_evaluate_job_title_level_id = None
        leader_evaluate_job_title_level_id = None

        status_filter_old = evaluate.get(EvaluateKey.STATUS_FILTER, "")

        status_filter = EvaluateStatusFilterChoice.WAITING_LEADER.value
        job_title_level_status = ""
        if is_completed:
            status = EvaluateStatusChoice.COMPLETED.value
            status_filter = EvaluateStatusFilterChoice.COMPLETED.value
            user_evaluate_job_title_level_id, leader_evaluate_job_title_level_id, job_title_level_status = (
                await self.calculate_point_after_job_level_id(payload.competency_groups, before_job_title_level_id)
            )
        time_now = get_time_now()
        if start_time > time_now or end_time < time_now:
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.NOT_EDITED).get(CommonKey.MESSAGE))

        competency_framework = await competency_framework_model.get_one_competency_framework_by_id(
            competency_framework_id
        )
        self.logger.info("{} :: competency_framework :: {}".format(self.update_evaluate.__name__, competency_framework))
        if not competency_framework:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_FRAMEWORK_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        if leader_evaluate_job_title_level_id == before_job_title_level_id:
            job_title_level_status = JobLevelStatusKey.JOB_LEVEL_STATUS_SAME

        await evaluate_model.update_evaluate(
            evaluate_id,
            {
                **payload.dict(exclude_none=True),
                EvaluateKey.IS_DRAFT: False,
                EvaluateKey.STATUS: status,
                EvaluateKey.STATUS_FILTER: status_filter,
                EvaluateKey.USER_EVALUATE_JOB_TITLE_LEVEL_ID: user_evaluate_job_title_level_id,
                EvaluateKey.LEADER_EVALUATE_JOB_TITLE_LEVEL_ID: leader_evaluate_job_title_level_id,
                EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID: leader_evaluate_job_title_level_id,
                EvaluateKey.JOB_TITLE_LEVEL_STATUS: job_title_level_status,
            },
        )
        self.logger.debug("{} :: evaluate :: {} :: updated".format(self.update_evaluate.__name__, payload.dict()))

        user = await user_repo.get_user_by_id(user_id)
        self.logger.info("{} :: user :: {}".format(self.update_evaluate.__name__, user.name))
        leader = await user_repo.get_user_by_id(user.leader_user_id)

        if payload.competency_groups:
            # Sync lark base detail evaluation
            message = {
                "id": evaluate_id,
                "user_name": user.name,
                "user_open_id": user.open_user_id,
                "leader_open_id": leader.open_user_id,
                "evaluation_data": await self.get_evaluate_data(payload.competency_groups),
            }
            publisher.publish_message(RedisSubscriberChannel.SYNC_BITABLE_DETAIL_EVALUATION_CHANNEL, message)

        # Sync lark base all user evaluation
        message = {
            "evaluate_period_id": str(evaluate["evaluate_period_id"]),
            "evaluate_id": str(evaluate_id),
            "status_filter_old": status_filter_old,
            "status_filter_new": status_filter,
            "user_action_id": account_id,
        }
        publisher.publish_message(RedisSubscriberChannel.SYNC_BITABLE_EVALUATION_ALL_USER_CHANNEL, message)

        if status_filter == EvaluateStatusFilterChoice.COMPLETED.value and leader_evaluate_job_title_level_id:
            await user_repo.update_user_job_title_level_id(user_id, leader_evaluate_job_title_level_id)

        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)
        after_job_title_level = await job_title_level_repo.get_job_title_level_by_id(leader_evaluate_job_title_level_id)
        before_job_title_level = await job_title_level_repo.get_job_title_level_by_id(before_job_title_level_id)

        item_return = {
            **payload.dict(),
            EvaluateKey.ID: evaluate_id,
            EvaluateKey.STATUS: status,
            EvaluateKey.IS_DRAFT: False,
            EvaluateKey.STATUS_FILTER: status_filter,
            EvaluateKey.JOB_TITLE_LEVEL_STATUS: job_title_level_status,
            EvaluateKey.AFTER_JOB_TITLE_LEVEL: after_job_title_level.name if after_job_title_level else "",
            EvaluateKey.BEFORE_JOB_TITLE_LEVEL: before_job_title_level.name if before_job_title_level else "",
            EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID: leader_evaluate_job_title_level_id,
            EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID: before_job_title_level_id,
        }

        return {
            CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS,
            CommonKey.DATA: await self.json_encoder(item_return),
        }

    async def get_evaluates(self, request: Request, filters: EvaluateFiltersRequestSchema, session: Session):
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = -1
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        evaluate_model: EvaluateModel = EvaluateModel()
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)

        roles, _ = await self.get_roles_and_permissions_from_token(request)
        user_ids_for_search = await self.get_user_filters(user_repo, company_id, filters)
        self.logger.info("{}::user_ids_for_search::{}".format(self.get_evaluates.__name__, user_ids_for_search))

        user_ids_for_abac = await self.get_user_ids_for_abac(user_repo, roles, account_id)
        self.logger.info("{}::user_ids_for_abac::{}".format(self.get_evaluates.__name__, user_ids_for_abac))

        mapping_field_sort = {
            "after_job_title_level": "after_job_title_level_id",
            "before_job_title_level": "before_job_title_level_id",
            "end_time": "time_eval_of_users.end_time",
        }

        evaluates = await evaluate_model.get_evaluates(
            company_id,
            filters,
            page=page,
            per_page=per_page,
            user_ids_for_search=user_ids_for_search,
            user_ids_for_abac=user_ids_for_abac,
            sort_option=[(mapping_field_sort.get(sort, sort), order), ("department_id", -1)],
        )

        data_return = await self.add_user_info_to_evaluate(evaluates, user_repo, job_title_repo, job_title_level_repo)

        self.logger.info("{}::evaluates::{}".format(self.get_evaluates.__name__, data_return))

        return {
            CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
            CommonKey.DATA: await self.json_encoder(data_return),
        }

    async def get_evaluates_managed_team(
        self, request: Request, filters: EvaluateFiltersRequestSchema, session: Session
    ):
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        evaluate_model: EvaluateModel = EvaluateModel()
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)

        roles, _ = await self.get_roles_and_permissions_from_token(request)
        user_ids_for_search = await self.get_user_filters(user_repo, company_id, filters)
        self.logger.info("{}::user_ids_for_search::{}".format(self.get_evaluates.__name__, user_ids_for_search))

        user_ids_for_abac = await self.get_user_ids_for_abac(user_repo, roles, account_id)
        self.logger.info("{}::user_ids_for_abac::{}".format(self.get_evaluates.__name__, user_ids_for_abac))

        mapping_field_sort = {
            "after_job_title_level": "after_job_title_level_id",
            "before_job_title_level": "before_job_title_level_id",
        }

        evaluates = await evaluate_model.get_evaluates_managed_team(
            company_id,
            filters,
            mapping_field_sort.get(sort, sort),
            order,
            page,
            per_page,
            user_ids_for_search,
            user_ids_for_abac,
        )

        data_evaluates = evaluates.get("data")
        data_return = await self.add_user_info_to_evaluates_managed_team(
            data_evaluates, user_repo, job_title_repo, job_title_level_repo
        )

        self.logger.info("{}::evaluates::{}".format(self.get_evaluates.__name__, data_return))

        return {
            CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
            CommonKey.DATA: await self.json_encoder(data_return),
            CommonKey.PAGING: {
                "page": page,
                "per_page": per_page,
                "total_page": int(evaluates.get("total_pages")),
            },
        }

    async def list_evaluate_by_user_id(self, request: Request, user_id, filters, session: Session):
        company_id = await self.get_company_id_from_token(request)

        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        evaluate_model: EvaluateModel = EvaluateModel()
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)

        roles, _ = await self.get_roles_and_permissions_from_token(request)

        if RoleChoice.ADMIN.value not in roles:
            user_ids_for_abac = await self.get_user_ids_for_abac(user_repo, roles, account_id)
            self.logger.info("{}::user_ids_for_abac::{}".format(self.get_evaluates.__name__, user_ids_for_abac))
            if user_id not in user_ids_for_abac:
                raise AccessDenied(
                    self.get_lang(self.lang).get(ErrorsMessage.NOT_PERMISSION).get(CommonKey.MESSAGE), 403
                )

        mapping_field_sort = {
            "after_job_title_level": "after_job_title_level_id",
            "before_job_title_level": "before_job_title_level_id",
        }

        evaluates = await evaluate_model.get_evaluates(
            company_id,
            filters,
            mapping_field_sort.get(sort, sort),
            order,
            page,
            per_page,
            [user_id],
            [user_id],
        )

        data_return = await self.add_user_info_to_evaluate(evaluates, user_repo, job_title_repo, job_title_level_repo)

        self.logger.info("{}::evaluates::{}".format(self.get_evaluates.__name__, data_return))

        return {
            CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
            CommonKey.DATA: await self.json_encoder(data_return),
            CommonKey.PAGING: {
                "page": page,
                "per_page": per_page,
            },
        }

    async def get_status_evaluate(self, request: Request, session: Session):
        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        account_id = await self.get_user_id_from_token(request)
        roles, _ = await self.get_roles_and_permissions_from_token(request)

        evaluate_model: EvaluateModel = EvaluateModel()
        user_repo: UserRepository = UserRepository(session)

        evaluate_period_id = request.query_params.get(QueryParamChoice.EVALUATE_PERIOD_ID)
        start_time = request.query_params.get(QueryParamChoice.START_TIME)
        end_time = request.query_params.get(QueryParamChoice.END_TIME)

        if start_time:
            start_time = datetime.datetime.strptime(start_time, "%Y-%m-%dT%H:%M:%S.%fZ").replace(
                hour=0, minute=0, second=0
            ) - datetime.timedelta(hours=7)

        if end_time:
            end_time = datetime.datetime.strptime(end_time, "%Y-%m-%dT%H:%M:%S.%fZ").replace(
                hour=23, minute=59, second=59
            ) - datetime.timedelta(hours=7)

        user_ids_for_abac = await self.get_user_ids_for_abac(user_repo, roles, account_id)

        results = [
            {
                EvaluateKey.STATUS: StatusChoice.EVALUATE_WAITING.value,
                EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.OWNER.value,
                "query": {
                    EvaluateKey.STATUS: StatusChoice.EVALUATE_WAITING.value,
                    EvaluateKey.TIME_EVAL_OF_USERS: {
                        "$elemMatch": {
                            EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.OWNER.value,
                            EvaluateKey.SUBMIT_TIME: {"$exists": False},
                        }
                    },
                },
                EvaluateKey.COLOR: "#F37933",
            },
            # {
            #     EvaluateKey.STATUS: StatusChoice.EVALUATE_WAITING.value,
            #     EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.HR.value,
            #     "query": {
            #         EvaluateKey.STATUS: StatusChoice.EVALUATE_WAITING.value,
            #         "$and": [
            #             {
            #                 EvaluateKey.TIME_EVAL_OF_USERS: {
            #                     "$elemMatch": {
            #                         EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.OWNER.value,
            #                         EvaluateKey.SUBMIT_TIME: {"$exists": True},
            #                     }
            #                 },
            #             },
            #             {
            #                 EvaluateKey.TIME_EVAL_OF_USERS: {
            #                     "$elemMatch": {
            #                         EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.HR.value,
            #                         EvaluateKey.SUBMIT_TIME: {"$exists": False},
            #                     }
            #                 },
            #             },
            #         ],
            #     },
            #     EvaluateKey.COLOR: "#36D3D3",
            # },
            {
                EvaluateKey.STATUS: StatusChoice.EVALUATE_WAITING.value,
                EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.LEADER.value,
                "query": {
                    EvaluateKey.STATUS: StatusChoice.EVALUATE_WAITING.value,
                    "$and": [
                        {
                            EvaluateKey.TIME_EVAL_OF_USERS: {
                                "$elemMatch": {
                                    EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.LEADER.value,
                                    EvaluateKey.SUBMIT_TIME: {"$exists": False},
                                }
                            },
                        },
                        {
                            EvaluateKey.TIME_EVAL_OF_USERS: {
                                "$elemMatch": {
                                    EvaluateKey.USER_TYPE: UserEvaluateTypeChoice.OWNER.value,
                                    EvaluateKey.SUBMIT_TIME: {"$exists": True},
                                }
                            },
                        },
                    ],
                },
                EvaluateKey.COLOR: "#F74E8C",
            },
            {
                EvaluateKey.STATUS: StatusChoice.EVALUATE_COMPLETED.value,
                "query": {
                    EvaluateKey.STATUS: StatusChoice.EVALUATE_COMPLETED.value,
                },
                EvaluateKey.COLOR: "#33DA8A",
            },
        ]
        for result in results:
            query = result.pop("query")

            if evaluate_period_id:
                query.update({EvaluateKey.EVALUATE_PERIOD_ID: ObjectId(evaluate_period_id)})
            if user_ids_for_abac:
                query.update({EvaluateKey.USER_ID: {"$in": user_ids_for_abac}})
            if start_time and end_time:
                query.update({EvaluateKey.START_TIME: {"$gte": start_time, "$lte": end_time}})
            elif start_time:
                query.update({EvaluateKey.START_TIME: {"$gte": start_time}})
            elif end_time:
                query.update({EvaluateKey.END_TIME: {"$lte": end_time}})
            result.update(
                {
                    EvaluateKey.COUNT: await evaluate_model.count_by_query(query),
                }
            )

        self.logger.info("{} :: results :: {}".format(self.get_status_evaluate.__name__, results))

        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: results})

    async def get_evaluate_for_current_user(self, request: Request, session: Session):
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        evaluate_model: EvaluateModel = EvaluateModel()
        evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)
        department_repo: DepartmentRepository = DepartmentRepository(session)

        user = await user_repo.get_user_by_id(account_id)
        if not user:
            return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: {}})

        evaluate = await evaluate_model.get_evaluate_current_by_user_id(company_id, user.user_id)
        self.logger.info(f"get_evaluate_for_current_user::evaluate::{evaluate}")
        if not evaluate:
            return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: {}})

        evaluate_period_id = evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)

        evaluate_period = await evaluate_period_model.get_evaluate_period_by_id(evaluate_period_id)
        self.logger.info(f"get_evaluate_for_current_user::evaluate_period::{evaluate_period}")

        job_title = await job_title_repo.get_job_title_by_id(user.job_title_id)
        job_title_level = await job_title_level_repo.get_job_title_level_by_id(user.job_title_level_id)
        department = await department_repo.get_department_by_id(user.departments[0].department_id)

        data_evaluate = await self.json_encoder(
            {
                **evaluate,
                EvaluateKey.JOB_TITLE: job_title.name,
                UserKey.JOB_TITLE_ID: user.job_title_id,
                UserKey.JOB_TITLE_LEVEL_ID: user.job_title_level_id if user.job_title_level_id else "",
                EvaluateKey.JOB_TITLE_LEVEL: job_title_level.name if job_title_level else "",
                EvaluateKey.DEPARTMENT: department.name,
            }
        )

        data_evaluate = await self._calculate_point_min_detail_evaluate(data_evaluate)

        results = {
            EvaluateKey.EVALUATE_PERIOD: await self.json_encoder(evaluate_period),
            EvaluateKey.EVALUATE: data_evaluate,
        }

        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: results})

    async def save_draft(self, request: Request, payload: SaveDraftEvaluateRequestSchema, session: Session):
        account_id = await self.get_user_id_from_token(request)

        evaluate_model: EvaluateModel = EvaluateModel()
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)

        time_now = get_time_now()

        evaluate = await evaluate_model.get_evaluate_by_id(payload.evaluate_id)
        self.logger.info("{} :: evaluate :: {}".format(self.save_draft.__name__, evaluate))
        if evaluate:
            created_by = evaluate.get(CommonKey.CREATED_BY)
            created_time = evaluate.get(CommonKey.CREATED_TIME)
            user_id = evaluate.get(EvaluateKey.USER_ID)
            time_eval_of_users = evaluate.get(EvaluateKey.TIME_EVAL_OF_USERS)
            before_job_title_level_id = evaluate.get(EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID)
            after_job_title_level_id = evaluate.get(EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID)
            status = evaluate.get(EvaluateKey.STATUS)

            payload.created_by = created_by
            payload.created_time = created_time
            payload.updated_by = account_id
            payload.updated_time = time_now

            await evaluate_model.update_evaluate(
                payload.evaluate_id,
                payload={**payload.dict(exclude={"evaluate_id"}, exclude_none=True), EvaluateKey.IS_DRAFT: True},
            )

            user = await user_repo.get_user_by_id(user_id)
            self.logger.info("{} :: name :: {}".format(self.save_draft.__name__, user.name))

            job_title = await job_title_repo.get_job_title_by_id(user.job_title_id)
            job_title_level_before = await job_title_level_repo.get_job_title_level_by_id(before_job_title_level_id)
            job_title_level_after = await job_title_level_repo.get_job_title_level_by_id(after_job_title_level_id)

            data_return = {
                UserKey.NAME: user.name,
                UserKey.DEPARTMENTS: user.departments[0].name,
                EvaluateKey.ID: payload.evaluate_id,
                EvaluateKey.JOB_TITLE: job_title.name,
                EvaluateKey.TIME_EVAL_OF_USERS: time_eval_of_users,
                EvaluateKey.AFTER_JOB_TITLE_LEVEL: job_title_level_after.name if job_title_level_after else "",
                EvaluateKey.BEFORE_JOB_TITLE_LEVEL: job_title_level_before.name if job_title_level_before else "",
                EvaluateKey.IS_DRAFT: True,
                EvaluateKey.STATUS: status,
            }

            self.logger.debug("{} :: evaluate :: {} :: updated".format(self.save_draft.__name__, evaluate))
            return await self.json_encoder(
                {CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS, CommonKey.DATA: await self.json_encoder(data_return)}
            )

        payload.created_by = payload.updated_by = account_id
        payload.created_time = payload.updated_time = time_now
        await evaluate_model.insert_evaluate(payload={**payload.dict(exclude_none=True), EvaluateKey.IS_DRAFT: True})
        self.logger.debug("{} :: evaluate :: {} :: created".format(self.save_draft.__name__, payload))

    async def _calculate_point_min_detail_evaluate(self, evaluate):
        before_job_title_level_id = evaluate.get(EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID)
        competency_groups = evaluate.get(EvaluateKey.COMPETENCY_GROUPS)
        for competency_group in competency_groups:
            for competency in competency_group.get("lst_competency"):
                job_title_levels = competency.get("job_title_levels")
                mapping_behavior_expression_level_point_min = {}
                behavior_expressions = competency.get("behavior_expressions")
                if behavior_expressions:
                    for behavior_expression in behavior_expressions:
                        if behavior_expression.get("is_activate"):
                            mapping_behavior_expression_level_point_min[behavior_expression.get("level")] = (
                                behavior_expression.get("point_min")
                            )
                point_min = 0
                for job_title_level in job_title_levels:
                    if (
                        job_title_level.get("job_title_level_id") == before_job_title_level_id
                        and job_title_level.get("behavior_expression_level") > 0
                    ):
                        behavior_expression_level = job_title_level.get("behavior_expression_level")
                        point_min = mapping_behavior_expression_level_point_min.get(behavior_expression_level, 0)
                competency.update({"point_min": point_min})
        return evaluate

    async def detail_evaluate(self, request: Request, evaluate_id: str, session: Session):
        evaluate_model: EvaluateModel = EvaluateModel()
        evaluate_period_model: EvaluatePeriodModel = EvaluatePeriodModel()
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)
        department_repo: DepartmentRepository = DepartmentRepository(session)

        evaluate = await evaluate_model.get_evaluate_by_id(evaluate_id)
        self.logger.info("{} :: evaluate :: {}".format(self.get_evaluate_for_current_user.__name__, evaluate))
        if not evaluate:
            return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: None})

        user_id = evaluate.get(EvaluateKey.USER_ID)

        user = await user_repo.get_user_by_id(user_id)
        self.logger.info("{} :: user :: {}".format(self.get_evaluate_for_current_user.__name__, user.name))
        if not user:
            return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: None})

        evaluate_period_id = evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)

        evaluate_period = await evaluate_period_model.get_evaluate_period_by_id(evaluate_period_id)
        self.logger.info(
            "{} :: evaluate_period :: {}".format(self.get_evaluate_for_current_user.__name__, evaluate_period)
        )

        job_title = await job_title_repo.get_job_title_by_id(user.job_title_id)
        job_title_level = await job_title_level_repo.get_job_title_level_by_id(user.job_title_level_id)
        department = await department_repo.get_department_by_id(user.departments[0].department_id)

        result_evaluate = {
            **evaluate,
            EvaluateKey.JOB_TITLE: job_title.name,
            UserKey.JOB_TITLE_ID: user.job_title_id,
            UserKey.JOB_TITLE_LEVEL_ID: user.job_title_level_id if user.job_title_level_id else "",
            EvaluateKey.JOB_TITLE_LEVEL: job_title_level.name if job_title_level else "",
            EvaluateKey.DEPARTMENT: department.name,
        }
        result_evaluate = await self._calculate_point_min_detail_evaluate(result_evaluate)

        results = {
            EvaluateKey.EVALUATE_PERIOD: await self.json_encoder(evaluate_period),
            EvaluateKey.EVALUATE: await self.json_encoder(result_evaluate),
        }

        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: results})

    async def export_evaluate(self, request: Request, evaluate_id: str, session: Session):
        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)
        export_type = request.query_params.get(ParamKey.EXPORT_TYPE, "csv")

        evaluate_model: EvaluateModel = EvaluateModel()
        user_repo: UserRepository = UserRepository(session)
        job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)
        department_repo: DepartmentRepository = DepartmentRepository(session)
        task_performance_model: TaskPerformanceModel = TaskPerformanceModel()

        evaluate = await evaluate_model.get_evaluate_by_id(evaluate_id, projection={EvaluateKey.ID: 0})
        self.logger.info("{} :: evaluate :: {}".format(self.get_evaluate_for_current_user.__name__, evaluate))
        if not evaluate:
            return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: None})

        user_id = evaluate.get(EvaluateKey.USER_ID)
        competency_groups = evaluate.get(EvaluateKey.COMPETENCY_GROUPS)
        evaluate_period_id = evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)
        data_exports = await self.get_evaluate_data_exports(competency_groups)

        user = await user_repo.get_user_by_id(user_id)
        self.logger.info("{} :: user :: {}".format(self.get_evaluate_for_current_user.__name__, user.name))
        if not user:
            return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: None})

        task_performances = await task_performance_model.find_one(
            {"user_id": user_id, "evaluate_period_id": ObjectId(evaluate_period_id)}
        )
        if task_performances:
            task_performances = task_performances.get("task_performance", [])

        try:
            if export_type == "csv":
                import pandas

                file_path = os.path.join(
                    ApplicationConfig.LADDER_HOME + "/resources" + "/exports/csv/" + evaluate_id + ".csv"
                )
                df = pandas.DataFrame(data_exports)
                df.to_csv(file_path, index=False, encoding="utf-8-sig")

                return FileResponse(
                    file_path,
                    filename=f"Bản đánh giá của nhân sự {user.name}.csv",
                    media_type="text/csv",
                    headers={"Content-Disposition": "Access-Control-Expose-Headers"},
                )

            elif export_type == "pdf":
                file_path = os.path.join(
                    ApplicationConfig.LADDER_HOME + "/resources" + "/exports/pdf/" + evaluate_id + ".pdf"
                )
                with open(file_path, "wb") as f:
                    f.write(json.dumps(data_exports).encode("utf-8"))

                return FileResponse(
                    file_path,
                    filename=f"Bản đánh giá của nhân sự {user.name}.pdf",
                    media_type="application/pdf",
                    headers={"Content-Disposition": "Access-Control-Expose-Headers"},
                )

            elif export_type == "excel":
                file_path = os.path.join(
                    ApplicationConfig.LADDER_HOME + "/resources" + "/exports/excel/" + evaluate_id + ".xlsx"
                )

                await gen_data_export_evaluate_to_excel(
                    company_id=company_id,
                    user=user,
                    user_repo=user_repo,
                    job_title_level_repo=job_title_level_repo,
                    department_repo=department_repo,
                    evaluate=evaluate,
                    competency_groups=competency_groups,
                    file_path=file_path,
                    task_performances=task_performances,
                )

                return FileResponse(
                    file_path,
                    filename=f"Bản đánh giá của nhân sự {user.name}.excel",
                    media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    headers={"Content-Disposition": "Access-Control-Expose-Headers"},
                )

        except Exception:
            self.logger.error("Error to::", exc_info=True)
            raise CustomError("Type export {} not supported.".format(export_type))


if __name__ == "__main__":
    pass
