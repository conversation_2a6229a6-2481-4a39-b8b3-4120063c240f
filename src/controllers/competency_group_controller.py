from bson import ObjectId
from starlette.requests import Request

from src.common.choices import LanguageC<PERSON>ice, QueryParamChoice
from src.common.common import CommonKey, CompetencyGroupKey
from src.common.custom_exception import ConflictMessage
from src.common.message import ErrorsMessage, SuccessMessage
from src.controllers.base_controller import BaseController
from src.models.mongo.competency_group_model import CompetencyGroupModel
from src.models.mongo.competency_model import CompetencyModel
from src.schemas.pydantic.competency_group_schema import (
    AddCompetencyGroupRequestSchema,
    UpdateCompetencyGroupRequestSchema,
)
from src.utils.time_helper import get_time_now


class CompetencyGroupController(BaseController):

    async def get_competency_groups(self, request: Request):
        func_name = self.get_competency_groups.__name__
        search = request.query_params.get(QueryParamChoice.SEARCH, "")
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        competency_model: CompetencyModel = CompetencyModel()
        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()

        results, paging = await competency_group_model.get_lst_competency_group_in_system(
            search, sort, order, page, per_page
        )
        self.logger.info("{} :: competency_groups :: {}".format(func_name, results))

        if not results:
            results = await competency_group_model.init_competency_default("admin")
            self.logger.info(
                "{} :: {} :: competency_groups :: {}".format(func_name, "init_competency_default", results)
            )

        for result in results:
            competency_group_id = str(result.get(CompetencyGroupKey.ID))
            lst_competency = await competency_model.get_competencies_by_group_id(competency_group_id)
            self.logger.info(
                "{} :: competency_group_id :: {} :: lst_competency :: {}".format(
                    func_name, competency_group_id, lst_competency
                )
            )
            result.update({CompetencyGroupKey.LST_COMPETENCY: await self.json_encoder(lst_competency)})

        return {
            CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
            CommonKey.DATA: await self.json_encoder(results),
            CommonKey.PAGING: paging,
        }

    async def add_competency_group(self, request: Request, payload: AddCompetencyGroupRequestSchema):
        func_name = self.add_competency_group.__name__
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)
        payload.created_by = payload.updated_by = account_id
        payload.created_time = payload.updated_time = get_time_now()

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()

        if await competency_group_model.get_one_competency_group_by_name(payload.name.lower()):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_EXIST).get(CommonKey.MESSAGE)
            )

        inserted_id = await competency_group_model.insert_competency_group(
            {
                **payload.dict(exclude_none=True, exclude_defaults=True),
                CompetencyGroupKey.LOWER_CASE_NAME: payload.name.lower(),
                CompetencyGroupKey.IS_DEFAULT: False,
                CompetencyGroupKey.ORDER: await competency_group_model.count({}) + 1,
                CommonKey.COMPANY_ID: company_id,
            }
        )
        if not inserted_id:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_EXIST).get(CommonKey.MESSAGE)
            )
        self.logger.info("{} :: inserted_id {}".format(func_name, inserted_id))

        data_return = {
            CompetencyGroupKey.ID: str(inserted_id),
            CompetencyGroupKey.NAME: payload.name,
            CompetencyGroupKey.IS_DEFAULT: False,
        }

        return {
            CommonKey.MESSAGE: SuccessMessage.ADD_SUCCESS,
            CommonKey.DATA: await self.json_encoder(data_return),
        }

    async def update_competency_group(
        self, request: Request, competency_group_id, payload: UpdateCompetencyGroupRequestSchema
    ):
        func_name = self.update_competency_group.__name__
        account_id = await self.get_user_id_from_token(request)
        payload.updated_by = account_id
        payload.updated_time = get_time_now()

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()

        competency_group = await competency_group_model.get_one_competency_group_by_id(competency_group_id)
        if not competency_group:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        if await competency_group_model.get_one_competency_group_by_name(payload.name.lower()):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_EXIST).get(CommonKey.MESSAGE)
            )

        is_default = competency_group.get(CompetencyGroupKey.IS_DEFAULT)

        if is_default:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_DEFAULT).get(CommonKey.MESSAGE)
            )

        await competency_group_model.update_one_competency_group(
            competency_group_id, payload.dict(exclude_none=True, exclude_defaults=True)
        )

        data_return = {
            CompetencyGroupKey.ID: competency_group_id,
            CompetencyGroupKey.NAME: payload.name,
            CompetencyGroupKey.IS_DEFAULT: is_default,
        }
        return {
            CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS,
            CommonKey.DATA: await self.json_encoder(data_return),
        }

    async def delete_competency_group(self, request: Request, competency_group_id):
        func_name = self.delete_competency_group.__name__

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()
        competency_model: CompetencyModel = CompetencyModel()

        competency_group = await competency_group_model.get_one_competency_group_by_id(competency_group_id)
        if not competency_group:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        is_default = competency_group.get(CompetencyGroupKey.IS_DEFAULT)
        if is_default:
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_DEFAULT).get(CommonKey.MESSAGE)
            )

        if await competency_model.get_competencies_by_group_id(competency_group_id):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.EXIST_COMPETENCY_IN_GROUP).get(CommonKey.MESSAGE)
            )

        await competency_group_model.delete_one_competency_group(competency_group_id=competency_group_id)
        self.logger.debug("{} :: competency_group_id  :: {} :: deleted".format(func_name, competency_group_id))

        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.DELETE_SUCCESS})

    async def get_default_apply(self, request: Request):
        company_id = await self.get_company_id_from_token(request)
        competency_model: CompetencyModel = CompetencyModel()
        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()
        department_id = request.query_params.get("department_id")

        list_competency_default = await competency_model.get_competencies_by_department_id(company_id, department_id)
        mapping_competency_group = {}
        for competency in list_competency_default:
            competency_group_id = str(competency.get("competency_group_id"))
            if competency_group_id:
                competency_group_id = ObjectId(competency_group_id)
            if competency_group_id not in mapping_competency_group:
                mapping_competency_group[competency_group_id] = []
            mapping_competency_group[competency_group_id].append(competency)

        list_competency_groups_default = await competency_group_model.get_default_competency_group_by_ids(
            list(mapping_competency_group.keys())
        )
        results = []
        for gr in list_competency_groups_default:
            competency_group_id = gr.get(CompetencyGroupKey.ID)
            results.append(
                {
                    **gr,
                    CompetencyGroupKey.LST_COMPETENCY: await self.json_encoder(
                        mapping_competency_group.get(competency_group_id)
                    ),
                }
            )

        return await self.json_encoder(
            {CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: await self.json_encoder(results)}
        )
