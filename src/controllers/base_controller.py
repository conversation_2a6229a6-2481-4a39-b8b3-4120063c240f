#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: tungdd
Date created: 04/01/2024
"""

from mobio.libs.logging import MobioLogging
from sqlalchemy.orm import Session
from starlette.requests import Request

from configs.database import get_db
from src.auth.encrypt import decode_oauth2_token
from src.common.choices import LanguageChoice
from src.common.common import CommonKey, UserKey
from src.common.json_encoder import J<PERSON><PERSON>ncoder
from src.common.lang_config import LangConfig


class BaseController:

    def __init__(self, request: Request = None, session: Session = None, **kwargs):
        # Logger
        self.logger = MobioLogging()

        # Request
        self.request = request
        self.session = session
        self.lang = LanguageChoice.VI.value
        if self.request:
            self.lang = self.request.query_params.get(CommonKey.LANG, self.lang)

        if "user" in kwargs:
            self.user = kwargs["user"]

    def get_lang(self, language):
        return LangConfig().lang_map(language)

    async def json_encoder(self, data):
        if isinstance(data, list):
            for item in data:
                item = await self.json_encoder(item)
        return JSONEncoder("%Y-%m-%dT%H:%MZ").json_loads(data)

    async def get_session(self):
        return next(get_db())

    async def get_email_from_token(self, request: Request):
        authorization = request.headers.get("Authorization")
        if authorization.startswith("Bearer "):
            authorization = authorization.replace("Bearer ", "")

        payload_decode: dict = await decode_oauth2_token(authorization)
        return payload_decode.get(UserKey.USER_ID)

    async def get_company_id_from_token(self, request: Request):
        authorization = request.headers.get("Authorization")
        if authorization.startswith("Bearer "):
            authorization = authorization.replace("Bearer ", "")

        payload_decode: dict = await decode_oauth2_token(authorization)
        return payload_decode.get(UserKey.COMPANY_ID)

    async def get_user_data_from_token(self):
        authorization = self.request.headers.get("Authorization")
        if authorization.startswith("Bearer "):
            authorization = authorization.replace("Bearer ", "")

        payload_decode: dict = await decode_oauth2_token(authorization)

        return payload_decode

    async def get_user_id_from_token(self, request: Request):
        authorization = request.headers.get("Authorization")
        if authorization.startswith("Bearer "):
            authorization = authorization.replace("Bearer ", "")

        payload_decode: dict = await decode_oauth2_token(authorization)
        return payload_decode.get(UserKey.USER_ID)

    async def get_roles_and_permissions_from_token(self, request: Request):
        authorization = request.headers.get("Authorization")
        if authorization.startswith("Bearer "):
            authorization = authorization.replace("Bearer ", "")

        payload_decode: dict = await decode_oauth2_token(authorization)
        roles = payload_decode.get(UserKey.ROLES)
        permissions = payload_decode.get(UserKey.PERMISSIONS)
        return roles, permissions
