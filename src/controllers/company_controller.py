import datetime
import os
import subprocess

from configs import ApplicationConfig
from src.common.caching import lru_cache_m_caching
from src.common.common import Common<PERSON><PERSON>, UserKey
from src.common.custom_exception import AccessDenied, CustomError
from src.common.message import ErrorsMessage
from src.controllers.base_controller import BaseController
from src.services.companies_service import CompaniesService


class CompanyController(BaseController):
    def __init__(self):
        super().__init__()
        self.companies_service = CompaniesService()

    @lru_cache_m_caching.add_for_class_async(expiration=3600)
    async def get_company_information(self):
        data, message = await self.companies_service.get_company_information()
        if not data:
            raise CustomError(message)
        return {CommonKey.DATA: data}

    async def sync_lark_data(self):
        from src.cronjobs.handler_migrate_sync_data_from_lark_to_ladder import (
            HandlerSyncDataFromLark,
        )

        current_user_data = await self.get_user_data_from_token()
        # Check pem
        if "admin" not in current_user_data.get(UserKey.ROLES, []):
            raise AccessDenied(self.get_lang(self.lang).get(ErrorsMessage.NOT_PERMISSION).get(CommonKey.MESSAGE), 403)

        # Check locking
        lock_data = HandlerSyncDataFromLark.Locker.get_lock_data()
        if lock_data.get("is_locked", False):
            return await self.json_encoder(
                {
                    CommonKey.CODE: 400,
                    CommonKey.MESSAGE: "Không thể đồng bộ do có 1 luồng khác đang xử lý",
                    CommonKey.DATA: lock_data,
                }
            )

        # Add to bg task
        time_now = datetime.datetime.now(datetime.UTC)
        log_file = os.path.join(
            ApplicationConfig.LADDER_HOME
            + "/monitor_logs/lark_sync_{}.log".format(time_now.strftime("%Y_%m_%d_%H_%M_%S"))
        )
        with open(log_file, "w") as f:
            process = subprocess.Popen(
                ["python3.11", "-m", "src.cronjobs.handler_migrate_sync_data_from_lark_to_ladder"], stdout=f, stderr=f
            )

        return await self.json_encoder({CommonKey.MESSAGE: "Sync lark data processing in background!!"})
