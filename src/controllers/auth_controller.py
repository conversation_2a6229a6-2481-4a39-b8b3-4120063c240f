from fastapi import Request
from sqlalchemy.orm import Session

from src.services.auth_service import AuthService
from src.auth import authentication
from src.auth.encrypt import generate_oauth2_token
from src.common.choices import LanguageChoice
from src.common.common import Authorization<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from src.common.custom_exception import ConflictMessage, Unauthorized
from src.common.message import ErrorsMessage
from src.controllers.base_controller import BaseController
from src.libs.lark_docs_sdk.oapi.object.user import LarkObjectUserApi
from src.repositories.users_repository import UserRepository


class AuthController(BaseController):

    async def login(self, request: Request):
        data = await AuthService().get_information_login()

        return await self.json_encoder({CommonKey.DATA: data})

    async def logout(self, request: Request):
        try:
            await authentication.revoke_token(request.headers.get("Authorization"))
        except Exception as e:
            import traceback

            full_e = traceback.format_exc()
            self.logger.error(f"AuthController::logout::error::{str(full_e)}")
        return {}

    async def callback_refactor(self, request: Request, session: Session):
        code = request.query_params.get(ParamKey.CODE)
        if not code:
            raise ConflictMessage(ErrorsMessage.CODE_NOT_FOUND, 404)

        lang = request.query_params.get(CommonKey.LANG, LanguageChoice.VI.value)

        lark_access_token = LarkObjectUserApi().get_token_user_by_code(code)

        self.logger.info(f"callback_refactor :: user_access_token :: {lark_access_token}")
        # user_information = await user_api.get_information_user_by_code(lark_access_token)
        if not lark_access_token:
            raise Unauthorized(self.get_lang(lang).get(ErrorsMessage.USER_IS_NOT_ENABLED).get(CommonKey.MESSAGE), 404)
        user_email = lark_access_token.get("email")
        user_repository: UserRepository = UserRepository(session)

        user_current = await user_repository.get_one_by_primary_email(user_email)
        if not user_current:
            raise Unauthorized(self.get_lang(lang).get(ErrorsMessage.USER_IS_NOT_ENABLED).get(CommonKey.MESSAGE), 404)

        roles_name, permissions = await self.get_role_info(user_current.roles)

        data_encode = {
            UserKey.PRIMARY_EMAIL: user_current.primary_email,
            UserKey.NAME: user_current.name,
            UserKey.USER_ID: user_current.user_id,
            UserKey.EMPLOYEE_CODE: user_current.employee_code,
            UserKey.LARK_USER_ID: user_current.lark_user_id,
            UserKey.ROLES: roles_name,
            UserKey.PERMISSIONS: permissions,
            UserKey.COMPANY_ID: user_current.company_id,
            UserKey.OPEN_USER_ID: user_current.open_user_id,
        }

        access_token = generate_oauth2_token(data_encode)
        return await self.json_encoder(
            {CommonKey.DATA: {AuthorizationKey.ACCESS_TOKEN: f"{AuthorizationKey.BEARER} {access_token}"}}
        )
