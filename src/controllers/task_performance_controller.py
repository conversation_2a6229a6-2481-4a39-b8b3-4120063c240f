import datetime
import os
import subprocess

from bson import ObjectId
from starlette.requests import Request

from configs import ApplicationConfig
from src.common.common import Common<PERSON><PERSON>, UserKey
from src.common.custom_exception import AccessDenied
from src.common.message import ErrorsMessage
from src.controllers.base_controller import BaseController
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.task_performance_model import TaskPerformanceModel


class TaskPerformanceController(BaseController):

    async def get_task_performance_by_user(self, user_id, request: Request):
        current_user_data = await self.get_user_data_from_token()
        evaluate_period_id = request.query_params.get("evaluate_period_id", None)
        evaluate_id = request.query_params.get("evaluate_id", None)

        if not evaluate_period_id:
            return {CommonKey.DATA: {}}

        # Check pem
        if (
            user_id != current_user_data.get(UserKey.USER_ID)
            and "leader" not in current_user_data.get(UserKey.ROLES, [])
            and "admin" not in current_user_data.get(UserKey.ROLES, [])
        ):
            raise AccessDenied(self.get_lang(self.lang).get(ErrorsMessage.NOT_PERMISSION).get(CommonKey.MESSAGE), 403)

        evaluate_detail = await EvaluateModel().find_one({"_id": ObjectId(evaluate_period_id)})
        if not evaluate_detail:
            return AccessDenied(self.get_lang(self.lang).get(ErrorsMessage.NOT_PERMISSION).get(CommonKey.MESSAGE), 403)

        performance_data = await TaskPerformanceModel().get_task_performance_by_user_id(
            user_id, evaluate_period_id=evaluate_detail.get("evaluate_period_id")
        )
        performance_data = await self.json_encoder(performance_data) if performance_data else {}

        return {CommonKey.DATA: performance_data}

    async def aggregate_task_performance(self, request: Request):
        # Add to bg task
        time_now = datetime.datetime.now(datetime.UTC)
        log_file = os.path.join(
            ApplicationConfig.LADDER_HOME
            + "/monitor_logs/aggregate_task_performance_{}.log".format(time_now.strftime("%Y_%m_%d_%H_%M_%S"))
        )
        with open(log_file, "w") as f:
            process = subprocess.Popen(
                ["python3.11", "-m", "src.cronjobs.handler_sync_task_performance_cronjob"], stdout=f, stderr=f
            )

        return await self.json_encoder({CommonKey.MESSAGE: "Sync data performance in background!!"})
