import os

from sqlmodel import Session
from starlette.requests import Request

from src.auth.authentication import Authentication
from src.common.choices import LanguageChoice, QueryParamChoice, RoleChoice
from src.common.common import CommonKey, CompetencyKey
from src.common.custom_exception import ConflictMessage
from src.common.message import ErrorsMessage, SuccessMessage
from src.controllers.base_controller import BaseController
from src.models.mongo.competency_group_model import CompetencyGroupModel
from src.models.mongo.competency_model import CompetencyModel
from src.repositories.department_repository import DepartmentRepository
from src.repositories.users_repository import UserRepository
from src.schemas.pydantic.competency_schemas import (
    AddCompetencyRequestSchema,
    DeleteCompetencyRequestSchema,
    UpdateCompetencyRequestSchema,
    UpdateGroupsRequestSchema,
)
from src.utils import send_lark_notify
from src.utils.time_helper import get_time_now


class CompetencyFrameworkController(BaseController):

    async def lst_competency(self, request: Request):
        func_name = self.lst_competency.__name__

        competency_model: CompetencyModel = CompetencyModel()

        search = request.query_params.get(QueryParamChoice.SEARCH, "")
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        lst_competency, paging = await competency_model.get_all_competency_in_system(
            search, sort, order, page, per_page
        )
        self.logger.info("{} :: lst_competency :: {}".format(func_name, lst_competency))
        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
                CommonKey.DATA: lst_competency,
                CommonKey.PAGING: paging,
            }
        )

    async def detail_competency(self, request: Request, competency_id):
        func_name = self.detail_competency.__name__

        competency_model: CompetencyModel = CompetencyModel()

        competency_current: dict = await competency_model.get_one_competency_by_id(competency_id)
        self.logger.info("{} :: detail_competency :: {}".format(func_name, competency_current))
        return {
            CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
            CommonKey.DATA: await self.json_encoder(competency_current),
        }

    async def _send_notifications_to_department_owners(
        self,
        department_repo: DepartmentRepository,
        user_repo: UserRepository,
        company_id: str,
        department_ids: list,
        competency_name: str,
        user_update_name: str,
        user_update_job_title_name: str,
    ):
        link_access = os.path.join(os.environ.get("PUBLIC_HOST"), "competency-framework")
        list_owner_departments = await department_repo.get_department_owners(company_id, department_ids)

        for dept in list_owner_departments:
            users = await user_repo.get_users_by_ids(company_id, dept.owners)
            user_lark_open_ids = []
            for user in users:
                role_users = user.roles
                if RoleChoice.LEADER.value not in [role.name for role in role_users]:
                    continue

                for role_user in role_users:
                    if role_user.name == RoleChoice.LEADER.value:
                        user_lark_open_ids.append(user.open_user_id)

            # Sử dụng các biến đã được truy xuất trước
            if user_update_job_title_name:
                text = f"{user_update_name} - {user_update_job_title_name} vừa áp dụng năng lực [{competency_name}]({link_access}) vào team {dept.name} của bạn. Hãy truy cập để hoàn thành thiết lập điểm số và cấp độ chức danh áp dụng cho Team."
            else:
                text = f"{user_update_name} vừa áp dụng năng lực [{competency_name}]({link_access}) vào team {dept.name} của bạn. Hãy truy cập để hoàn thành thiết lập điểm số và cấp độ chức danh áp dụng cho Team."

            send_lark_notify.send_simple_text(
                open_ids=user_lark_open_ids, message_text=text, title="Vừa có năng lực được thêm mới"
            )

    async def update_competency(
        self, request: Request, competency_id, payload: UpdateCompetencyRequestSchema, session: Session
    ):
        func_name = self.update_competency.__name__
        payload.updated_time = get_time_now()
        payload.updated_by = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)

        competency_model: CompetencyModel = CompetencyModel()
        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()

        department_repo: DepartmentRepository = DepartmentRepository(session)

        user_repo: UserRepository = UserRepository(session)
        user_update, _ = await Authentication().get_user_and_roles(request)
        user_update_name = user_update.name
        user_update_job_title_name = user_update.job_title.name if user_update.job_title else ""

        if not await competency_group_model.get_one_competency_group_by_id(payload.competency_group_id):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )
        data_update = {**payload.dict(exclude_none=True), CompetencyKey.LOWER_CASE_NAME: payload.name.lower()}

        match_count = await competency_model.update_competency(competency_id, data_update)
        if not match_count:
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.COMPETENCY_IS_NOT_EXIST).get(CommonKey.MESSAGE))
        default_apply_department_ids = data_update.get("default_apply_department_ids", [])
        is_default_apply = data_update.get("is_default_apply", False)

        if default_apply_department_ids and is_default_apply:
            await self._send_notifications_to_department_owners(
                department_repo,
                user_repo,
                company_id,
                default_apply_department_ids,
                payload.name,
                user_update_name,
                user_update_job_title_name,
            )

        competency = await competency_model.get_one_competency_by_id(competency_id)
        self.logger.info("{} :: update_competency :: {}".format(func_name, competency))

        return {
            CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS,
            CommonKey.DATA: await self.json_encoder(competency),
        }

    async def delete_competency(self, request: Request, payload: DeleteCompetencyRequestSchema):
        func_name = self.delete_competency.__name__
        competency_model: CompetencyModel = CompetencyModel()

        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)

        deleted_count = await competency_model.delete_competencies(payload.competency_ids)
        self.logger.info("{} :: deleted_count {} :: deleted".format(func_name, deleted_count))
        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.DELETE_SUCCESS,
                CommonKey.DATA: {
                    CommonKey.DELETED_COUNT: deleted_count,
                    CommonKey.TOTAL_COUNT: len(payload.competency_ids),
                },
            }
        )

    async def add_competency(self, request: Request, payload: AddCompetencyRequestSchema, session: Session):
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)

        payload.created_by = payload.updated_by = account_id
        payload.created_time = payload.updated_time = get_time_now()

        competency_model: CompetencyModel = CompetencyModel()
        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()

        if not await competency_group_model.get_one_competency_group_by_id(payload.competency_group_id):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_NOT_EXIST).get(CommonKey.MESSAGE)
            )

        payload_dict = {
            **payload.dict(exclude_none=True),
            CompetencyKey.LOWER_CASE_NAME: payload.name.lower(),
            CommonKey.COMPANY_ID: company_id,
        }

        inserted_id = await competency_model.insert_competency(payload_dict)
        if not inserted_id:
            raise ConflictMessage(self.get_lang(lang).get(ErrorsMessage.COMPETENCY_IS_NOT_EXIST).get(CommonKey.MESSAGE))

        default_apply_department_ids = payload_dict.get("default_apply_department_ids", [])
        is_default_apply = payload_dict.get("is_default_apply", False)
        if default_apply_department_ids and is_default_apply:
            department_repo: DepartmentRepository = DepartmentRepository(session)
            user_repo: UserRepository = UserRepository(session)
            user_update, _ = await Authentication().get_user_and_roles(request)
            user_update_name = user_update.name
            user_update_job_title_name = user_update.job_title.name if user_update.job_title else ""
            await self._send_notifications_to_department_owners(
                department_repo,
                user_repo,
                company_id,
                default_apply_department_ids,
                payload.name,
                user_update_name,
                user_update_job_title_name,
            )

        data_return = {
            CompetencyKey.ID: str(inserted_id),
            **payload_dict,
        }

        self.logger.info("inserted_id::{}".format(inserted_id))

        return {"data": await self.json_encoder(data_return)}

    async def update_groups(self, request: Request, payload: UpdateGroupsRequestSchema):
        func_name = self.update_groups.__name__
        account_id = await self.get_user_id_from_token(request)
        company_id = await self.get_company_id_from_token(request)

        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)

        competency_group_model: CompetencyGroupModel = CompetencyGroupModel()
        competency_model: CompetencyModel = CompetencyModel()

        if not await competency_group_model.get_one_competency_group_by_id(payload.competency_group_id):
            raise ConflictMessage(
                self.get_lang(lang).get(ErrorsMessage.COMPETENCY_GROUP_IS_DELETED).get(CommonKey.MESSAGE)
            )

        matched_count = await competency_model.update_competencies(
            payload.competency_ids,
            {
                CompetencyKey.COMPETENCY_GROUP_ID: payload.competency_group_id,
                CommonKey.UPDATED_BY: account_id,
                CommonKey.UPDATED_TIME: get_time_now(),
            },
        )
        self.logger.info("{} :: matched_count :: {}".format(func_name, matched_count))

        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS,
                CommonKey.DATA: {
                    CommonKey.TOTAL_COUNT: len(payload.competency_ids),
                    CommonKey.UPDATED_COUNT: matched_count,
                },
            }
        )
