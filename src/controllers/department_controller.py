#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 31/08/2024
"""
from fastapi import Request
from sqlalchemy.orm import Session

from src.common.choices import QueryParamChoice
from src.common.common import CommonKey
from src.common.message import SuccessMessage
from src.controllers.base_controller import BaseController
from src.repositories.department_repository import DepartmentRepository
from src.schemas.pydantic.department_schemas import (
    ChangeOrderDepartmentsRequestSchema,
)
from src.services.departments_service import DepartmentsService


class DepartmentController(BaseController):
    def __init__(self):
        super().__init__()
        self.departments_service = DepartmentsService()

    async def get_departments(self, request: Request, session: Session):

        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        search = request.query_params.get(QueryParamChoice.SEARCH, "")
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))
        is_show_department_hidden = int(
            request.query_params.get(QueryParamChoice.DISPLAY, 0)
        )  # [!] có show các phòng ban bị ẩn hay không?

        data, paging = await self.departments_service.get_departments(
            request,
            session,
            search,
            sort,
            order,
            page,
            per_page,
            is_show_department_hidden,
            company_id,
            account_id
        )
        self.logger.info("{} :: departments :: {}".format(self.get_departments.__name__, data))

        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
                CommonKey.DATA: data,
                CommonKey.PAGING: paging,
            }
        )

    async def get_departments_for_org_chart(self, request: Request, session: Session):
        company_id = await self.get_company_id_from_token(request)

        search = request.query_params.get(QueryParamChoice.SEARCH, "")
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        department_ids = request.query_params.get(QueryParamChoice.DEPARTMENT_IDS)
        if department_ids:
            department_ids = department_ids.split(",")
        display = 1

        data, paging = await self.departments_service.get_departments_for_org_chart(
            search,
            sort,
            order,
            page,
            per_page,
            department_ids,
            company_id,
            display,
        )
        self.logger.info("{} :: departments :: {}".format(self.get_departments_for_org_chart.__name__, data))

        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
                CommonKey.DATA: data,
                CommonKey.PAGING: paging,
            }
        )

    async def update_order_for_departments(
            self, request: Request, payload: ChangeOrderDepartmentsRequestSchema, session: Session
    ):

        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        department_ids = payload.departments
        department_ids = department_ids.split(",")

        department_repo: DepartmentRepository = DepartmentRepository(session)

        for department_order in payload.departments:
            await department_repo.update_order_for_department(
                company_id, department_order.department_id, department_order.order
            )

        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS})

    async def get_department_owners(self, request: Request, session: Session):
        company_id = await self.get_company_id_from_token(request)

        department_repo: DepartmentRepository = DepartmentRepository(session)
        department_ids = request.query_params.get(QueryParamChoice.DEPARTMENT_IDS, "")
        department_ids = department_ids.split(",") if department_ids else []

        data = await self.departments_service.get_department_owners(company_id, department_ids)

        # ====================================
        department_owners = await department_repo.get_department_owners(company_id, department_ids)

        results = []
        for department_owner in department_owners:
            get_department_schema = {
                "department_id": department_owner.department_id,
                "owner_ids": department_owner.owners,
            }
            results.append(get_department_schema)
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: results})
