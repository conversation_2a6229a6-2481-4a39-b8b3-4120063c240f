#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: Current Date
"""
import os
from typing import Dict

from fastapi import HTTPException, Request
from fastapi.responses import FileResponse, HTMLResponse
from sqlalchemy.orm import Session

from src.repositories.role_repository import RoleRepository
from src.repositories.users_repository import UserRepository


class PolicyAdminController:
    def __init__(self):
        # Get the directory of the static files for policy admin
        self.admin_dir = os.path.join(os.getcwd(), "static", "policy_admin")

    async def serve_admin_ui(self, request: Request):
        """Serve the policy admin UI homepage"""
        index_path = os.path.join(self.admin_dir, "index.html")

        if os.path.exists(index_path):
            with open(index_path, "r") as file:
                content = file.read()
                return HTMLResponse(content=content)
        else:
            raise HTTPException(status_code=404, detail="Admin UI not found")

    async def serve_dashboard(self, request: Request):
        """Serve the policy admin dashboard page"""
        dashboard_path = os.path.join(self.admin_dir, "dashboard.html")

        if os.path.exists(dashboard_path):
            with open(dashboard_path, "r") as file:
                content = file.read()
                return HTMLResponse(content=content)
        else:
            raise HTTPException(status_code=404, detail="Dashboard page not found")

    async def serve_static_file(self, request: Request, file_path: str):
        """Serve static files for the policy admin UI"""
        full_path = os.path.join(self.admin_dir, file_path)

        if os.path.exists(full_path) and os.path.isfile(full_path):
            return FileResponse(full_path)
        else:
            raise HTTPException(status_code=404, detail=f"File {file_path} not found")

    async def get_users(self, request: Request, session: Session) -> Dict:
        """Get all users for the policy admin UI"""
        user_repo = UserRepository(session)
        users, _ = await user_repo.get_users(company_id="4302feda-826d-4419-b1f5-c9a8291ffb51", page=-1)

        result = []
        for user in users:
            result.append(
                {
                    "user_id": user.user_id,
                    "name": getattr(user, "name", None) or user.primary_email.split("@")[0],
                    "primary_email": user.primary_email,
                    "created_time": user.created_time.isoformat(),
                }
            )

        return {"data": result}

    async def get_user(self, request: Request, user_id: str, session: Session) -> Dict:
        """Get user details for the policy admin UI"""
        user_repo = UserRepository(session)
        user = await user_repo.get_user_by_id(user_id)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Get user policies
        policies = []
        for policy in user.policies:
            policies.append(
                {
                    "policy_id": policy.policy_id,
                    "name": policy.name,
                    "description": policy.description,
                    "policy_type": policy.policy_type,
                }
            )

        return {
            "data": {
                "user_id": user.user_id,
                "name": getattr(user, "name", None) or user.primary_email.split("@")[0],
                "primary_email": user.primary_email,
                "created_time": user.created_time.isoformat(),
                "policies": policies,
            }
        }

    async def get_roles(self, request: Request, session: Session) -> Dict:
        """Get all roles for the policy admin UI"""
        role_repo = RoleRepository(session)
        roles = await role_repo.get_all_roles()

        result = []
        for role in roles:
            result.append(
                {
                    "role_id": role.role_id,
                    "name": role.name,
                    "description": role.description,
                    "created_time": role.created_time.isoformat(),
                }
            )

        return {"data": result}

    async def get_role(self, request: Request, role_id: str, session: Session) -> Dict:
        """Get role details for the policy admin UI"""
        role_repo = RoleRepository(session)
        roles = await role_repo.get_role_by_ids([role_id])

        if not roles or len(roles) == 0:
            raise HTTPException(status_code=404, detail="Role not found")

        role = roles[0]

        # Get role policies
        policies = []
        for policy in role.policies:
            policies.append(
                {
                    "policy_id": policy.policy_id,
                    "name": policy.name,
                    "description": policy.description,
                    "policy_type": policy.policy_type,
                }
            )

        return {
            "data": {
                "role_id": role.role_id,
                "name": role.name,
                "description": role.description,
                "created_time": role.created_time.isoformat(),
                "policies": policies,
            }
        }
