import datetime
import os
import subprocess

from fastapi import Request
from sqlalchemy.orm import Session

from configs import ApplicationConfig
from src.auth.authentication import Authentication
from src.common.choices import LanguageChoice, QueryParamChoice, RoleChoice
from src.common.common import <PERSON><PERSON><PERSON>, Department<PERSON>ey, User<PERSON><PERSON>
from src.common.custom_exception import InputParamError
from src.common.message import ErrorsMessage, SuccessMessage
from src.controllers.base_controller import BaseController
from src.models.mongo.custom_setting_model import CustomSettingModel
from src.repositories.department_repository import DepartmentRepository
from src.repositories.job_title_repository import JobTitleRepository
from src.repositories.role_repository import RoleRepository
from src.repositories.users_repository import UserRepository
from src.schemas.pydantic.role_schemas import AddRoleRequestSchema
from src.schemas.pydantic.user_schemas import (
    GetUserDetailResponseSchema,
    GetUsersResponseSchema,
    UpdateJobTitleLevelUsersSchema,
    UserFiltersRequestSchema,
)


class UserController(BaseController):
    def map_color_for_user_in_bod(self, job_title_name):
        data_mapping = {
            "Giám đốc vận hành (COO)": "#C2E68B",
            "Giám đốc điều hành (CEO)": "#B1B3F8",
            "Giám đốc công nghệ (CTO)": "#87D2F4",
            "Giám đốc dữ liệu (CDO)": "#FA8F45",
            "Giám đốc Content (CCO)": "#FA8F45",
        }

        return data_mapping.get(job_title_name, "#FA8F45")

    async def get_users(self, request: Request, filters: UserFiltersRequestSchema, session: Session):
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)
        department_repo: DepartmentRepository = DepartmentRepository(session)
        role_repo: RoleRepository = RoleRepository(session)

        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))
        company_id = await self.get_company_id_from_token(request)

        user_ids = []
        if filters.user_ids:
            user_ids = filters.user_ids

        user_current, roles = await Authentication().get_user_and_roles(request)
        if RoleChoice.LEADER.value in roles and RoleChoice.ADMIN.value not in roles:
            departments = await department_repo.get_departments_by_owner(user_current.user_id)
            department_ids = [department.department_id for department in departments]
            for user_filter in filters.user_filters:
                if user_filter.field == DepartmentKey.DEPARTMENT_ID and department_ids:
                    user_filter.value = department_ids
            lst_user_ids_by_leader = await user_repo.get_users_by_leader_id(user_current.user_id)
            lst_user_ids_by_leader = [user.user_id for user in lst_user_ids_by_leader]
            lst_user_ids_by_leader.append(user_current.user_id)

            if user_ids:
                for user_id in user_ids:
                    if user_id not in lst_user_ids_by_leader:
                        user_ids.remove(user_id)
            else:
                user_ids = lst_user_ids_by_leader
            filters.user_ids = user_ids

        results = []
        users, paging = await user_repo.get_users(
            company_id,
            filters.search,
            filters.user_filters,
            user_ids,
            sort=sort,
            order=order,
            page=page,
            per_page=per_page,
            roles=roles,
        )

        roles_leader = await role_repo.get_roles_by_names([RoleChoice.ADMIN.value, RoleChoice.LEADER.value])

        for user_information in users:
            job_title_level_name = ""

            departments = await department_repo.get_departments_by_owner(user_information.user_id)
            department_owner_id = [department.department_id for department in departments]

            job_title = await job_title_repo.get_job_title_by_id(user_information.job_title_id)
            if user_information.job_title_level_id:
                job_title_level = await job_title_repo.get_job_title_level(
                    job_title_level_id=user_information.job_title_level_id
                )
                job_title_level_name = job_title_level.name
            is_leader = user_information.roles[0] in roles_leader

            user_response_schema = GetUsersResponseSchema(
                user_id=user_information.user_id,
                name=user_information.name,
                is_leader=is_leader,
                thumb_avatar_link=user_information.thumb_avatar_link,
                job_title=job_title.name,
                job_title_id=user_information.job_title_id,
                job_title_level_id=user_information.job_title_level_id,
                job_title_level_name=job_title_level_name,
                employment_type=user_information.employment_type_id,
                department_id=user_information.departments[0].department_id,
                department_owner_id=department_owner_id,
                leader_user_id=user_information.leader_user_id,
                order=user_information.order if user_information.order else 1,
            )
            results.append(user_response_schema.dict(exclude_none=True))

        if sort in ["job_title_level_id", "job_title_id"]:
            key_sort = {"job_title_level_id": "job_title_level_name", "job_title_id": "job_title"}
            results = sorted(results, key=lambda x: x[key_sort[sort]].lower(), reverse=order == -1)
        return await self.json_encoder(
            {CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: results, CommonKey.PAGING: paging}
        )

    async def get_detail_user(self, request: Request, user_id, session: Session, area: str = ""):
        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)

        user = await user_repo.get_user_by_id(user_id)
        if not user:
            raise InputParamError(self.get_lang(lang).get(ErrorsMessage.USER_IS_NOT_EXIST).get(CommonKey.MESSAGE), 404)
        self.logger.info("{} :: name :: {}".format(self.get_detail_user.__name__, user.name))

        account_id = await self.get_user_id_from_token(request)

        leader_user = await user_repo.get_user_by_id(user.leader_user_id)
        job_title = await job_title_repo.get_job_title_by_id(user.job_title_id)
        user_type = await user_repo.get_user_type(user.employment_type_id)
        user_type_name = user_type.vi_name if lang == LanguageChoice.VI.value else user_type.en_name
        datetime_now = datetime.datetime.now(datetime.UTC)
        start_onboard_at = user.start_onboard_at
        if start_onboard_at:
            # Tính tổng số ngày làm việc
            total_days = (datetime_now.replace(tzinfo=None) - start_onboard_at.replace(tzinfo=None)).days

            # Tính số năm, tháng và ngày
            years = total_days // 365
            remaining_days = total_days % 365
            months = remaining_days // 30
            days = remaining_days % 30
            result = []

            if years > 0:
                result.append(f"{years} năm")
            if months > 0:
                result.append(f"{months} tháng")
            if days > 0:
                result.append(f"{days} ngày")
            # Hiển thị kết quả
            working_seniority = " ".join(result)

        job_title_level_name = ""
        job_title_level = await job_title_repo.get_job_title_level(job_title_level_id=user.job_title_level_id)
        if job_title_level:
            job_title_level_name = job_title_level.name

        lst_role_name = [role.name for role in user.roles]
        if area == "org-chart":
            if account_id != user.user_id:
                if RoleChoice.ADMIN.value not in lst_role_name:
                    if user.leader_user_id != account_id:
                        job_title_level_name = ""

        user_detail = GetUserDetailResponseSchema(
            user_id=user.user_id,
            name=user.name,
            thumb_avatar_link=user.thumb_avatar_link,
            primary_email=user.primary_email,
            type=user_type_name,
            leader_user_id=user.leader_user_id if user.leader_user_id else "",
            job_title=job_title.name if area != "org-chart" else "",
            role_names=[role.name for role in user.roles],
            leader_user=leader_user.name if leader_user else "",
            department=user.departments[0].name,
            department_id=user.departments[0].department_id,
            phone_number=user.phone_number,
            start_onboard_at=user.start_onboard_at,
            job_title_level_name=job_title_level_name,
            working_seniority=working_seniority,
        )

        return await self.json_encoder(
            {CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: user_detail.dict(exclude_none=True)}
        )

    async def change_job_title_level_of_users(
        self, request: Request, payload: UpdateJobTitleLevelUsersSchema, session: Session
    ):
        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)

        user_repo: UserRepository = UserRepository(session)
        job_title_repo: JobTitleRepository = JobTitleRepository(session)

        if not await job_title_repo.get_job_title_level(job_title_level_id=payload.job_title_level_id):
            raise InputParamError(
                self.get_lang(lang).get(ErrorsMessage.JOB_TITLE_LEVEL_IS_NOT_EXIST).get(CommonKey.MESSAGE), 404
            )

        data_update = []
        for user_id in payload.user_ids:
            user = await user_repo.get_user_by_id(user_id)
            if not user:
                raise InputParamError(
                    self.get_lang(lang).get(ErrorsMessage.USER_IS_NOT_EXIST).get(CommonKey.MESSAGE), 404
                )
            data_update.append(
                {
                    UserKey.USER_ID: user_id,
                    UserKey.JOB_TITLE_LEVEL_ID: payload.job_title_level_id,
                    UserKey.EMPLOYEE_CODE: user.employee_code,
                }
            )
        self.logger.info("change_job_title_level_of_users :: data_update :: {}" % data_update)
        await user_repo.update_users(data_update)
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS})

    async def add_roles_for_user(self, request: Request, payload: AddRoleRequestSchema, session):

        lang = request.headers.get(CommonKey.LANG, LanguageChoice.VI.value)

        user_repo: UserRepository = UserRepository(session)
        role_repo: RoleRepository = RoleRepository(session)

        roles = await role_repo.get_role_by_ids(payload.role_ids)
        self.logger.info("add_roles_for_user :: roles :: {}".format([role.name for role in roles]))
        if not await user_repo.add_roles_to_user(user_id=payload.user_id, roles=roles):
            raise InputParamError(self.get_lang(lang).get(ErrorsMessage.USER_IS_NOT_EXIST).get(CommonKey.MESSAGE), 404)
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS})

    async def get_users_for_org_chart(self, filters: UserFiltersRequestSchema):
        datetime_now = datetime.datetime.now(datetime.UTC)
        user_repo: UserRepository = UserRepository(self.session)
        job_title_repository: JobTitleRepository = JobTitleRepository(self.session)

        sort = self.request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(self.request.query_params.get(QueryParamChoice.ORDER, -1))
        company_id = await self.get_company_id_from_token(self.request)

        results = []
        department_ids = []
        for user_filter in filters.user_filters:
            if user_filter.field == DepartmentKey.DEPARTMENT_ID:
                department_ids.extend(user_filter.value)

        self.logger.info(f"{filters}")

        list_all_job_title = await job_title_repository.get_all_job_titles()
        mapping_job_title = {}
        for job_title in list_all_job_title:
            mapping_job_title[job_title.job_title_id] = job_title.name

        mapping_job_title_level = {}

        users = await user_repo.get_users_for_org_chart(
            company_id, filters.search, filters.user_filters, sort=sort, order=order
        )

        custom_setting_model: CustomSettingModel = CustomSettingModel()
        custom_setting_role_user = await custom_setting_model.get_custom_setting_role()
        for user_information in users:
            working_seniority = ""
            start_onboard_at = user_information.start_onboard_at
            role_users = [role.name for role in user_information.roles]
            user_id = user_information.user_id
            email = user_information.primary_email

            role_custom_user = custom_setting_role_user.get(email)

            department_owner_ids = []
            for department in user_information.departments:
                if (
                    user_id in department.owners
                    and department.department_id not in department_owner_ids
                    and (RoleChoice.ADMIN.value in role_users and not role_custom_user)
                ):
                    department_owner_ids.append(department.department_id)

            if start_onboard_at:
                # Tính tổng số ngày làm việc
                total_days = (datetime_now.replace(tzinfo=None) - start_onboard_at.replace(tzinfo=None)).days
                # Tính số năm, tháng và ngày
                years = total_days // 365
                remaining_days = total_days % 365
                months = remaining_days // 30
                days = remaining_days % 30
                list_working_seniority = []
                if months > 0 and months < 12:
                    list_working_seniority.append(f"{months} tháng")
                if months == 12:
                    years += 1
                if years > 0:
                    list_working_seniority.append(f"{years} năm")
                if days > 0:
                    list_working_seniority.append(f"{days} ngày")
                # Hiển thị kết quả
                working_seniority = " ".join(list_working_seniority)

            color = self.map_color_for_user_in_bod(user_information.job_title.name)
            departments = user_information.departments
            if len(departments) == 0:
                continue
            results.append(
                {
                    "user_id": user_information.user_id,
                    "name": user_information.name,
                    "is_leader": (
                        True if RoleChoice.ADMIN.value in role_users or RoleChoice.LEADER.value in role_users else False
                    ),
                    "thumb_avatar_link": user_information.thumb_avatar_link,
                    "job_title": mapping_job_title.get(user_information.job_title_id, ""),
                    "job_title_id": user_information.job_title_id or "",
                    "job_title_level_id": "",
                    "job_title_level_name": mapping_job_title_level.get(user_information.job_title_level_id, ""),
                    "employment_type": user_information.employment_type_id,
                    "department_owner_id": department_owner_ids,
                    "department_id": departments[0].department_id if len(departments) > 0 else "",
                    "leader_user_id": user_information.leader_user_id,
                    "order": user_information.order if user_information.order else 1,
                    "color": color,
                    "working_seniority": working_seniority,
                }
            )

        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
                CommonKey.DATA: results,
            }
        )

    async def trigger_push_notification(self, request: Request):
        time_now = datetime.datetime.now(datetime.UTC)
        log_file = os.path.join(
            ApplicationConfig.LADDER_HOME
            + "/monitor_logs/lark_sync_{}.log".format(time_now.strftime("%Y_%m_%d_%H_%M_%S"))
        )
        with open(log_file, "w") as f:
            process = subprocess.Popen(
                ["python3.11", "-m", "src.cronjobs.handler_push_notification_to_user"], stdout=f, stderr=f
            )

        return await self.json_encoder({CommonKey.MESSAGE: "Push notification processing in background!!"})
