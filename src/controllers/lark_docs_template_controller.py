from src.common.common import User<PERSON>ey
from src.controllers.base_controller import BaseController
from src.models.mongo.lark_docs_template_model import LarkDocsTemplateModel
from src.schemas.pydantic.lark_docs_template_schema import (
    CreateLarkDocsTemplateSchema,
    ListLarkDocsTemplateResponseSchema,
)


class LarkDocsTemplateController(BaseController):

    async def create_template(self, body: CreateLarkDocsTemplateSchema):
        user_data = await self.get_user_data_from_token()
        template_data = body.dict()

        # Insert to mongo
        template_data["created_by"] = user_data[UserKey.OPEN_USER_ID]
        template_data["updated_by"] = user_data[UserKey.OPEN_USER_ID]
        mg_model = LarkDocsTemplateModel()
        await mg_model.upsert_template(template_data)
        return {}

    async def list_template(self):
        mg_model = LarkDocsTemplateModel()
        list_template_data = await mg_model.get_list_template()
        return ListLarkDocsTemplateResponseSchema(data=list_template_data)
