import asyncio
import copy
import datetime
from slugify import slugify
from src.repositories.users_repository import UserRepository
from src.redis.pubsub.subscribers.base_subscriber import BaseSubscriber
from src.common.common import RedisSubscriberChannel
from src.libs.lark_docs_sdk.builder.bitable import BitableAppBuilder, BitableTableBuilder, BitableTableRecordsBuilder
from src.libs.lark_docs_sdk.builder.folder import FolderBuilder
from src.models.mongo.sync_lark_bitable_mapping import SyncLarkBitableMapping
from configs.database import get_db
from mobio.libs.logging import MobioLogging

MAPPING_COLUMN_NAME = {
    "competency_name": "Tên NL",
    "competency_group_name": "Nhóm NL",
}

DEFAULT_FIELDS = [
    {"field_name": MAPPING_COLUMN_NAME["competency_name"], "type": 1},
    {"field_name": MAPPING_COLUMN_NAME["competency_group_name"], "type": 1},
]


class SyncBitableCompetencyFrameworkSubscriber(BaseSubscriber):

    TYPE = "competency_framework"
    CHANNEL = RedisSubscriberChannel.SYNC_BITABLE_COMPETENCY_FRAMEWORK_CHANNEL

    @staticmethod
    def _get_default_table(app_token, default_table_name):
        builder = BitableTableBuilder(app_token)
        list_table_response = builder.get_list_tables(per_page=100)
        for table in list_table_response.get("items", []):
            name = table["name"]
            if slugify(name) == slugify(default_table_name):
                return table

    @staticmethod
    def _sync_to_base(
        competency_framework_id,
        competency_framework_name,
        list_job_title_level,
        list_access_user_oid,
        competency_framework_data,
    ):
        # Define const
        cls = SyncBitableCompetencyFrameworkSubscriber
        mapping_job_title_level_name = copy.deepcopy(MAPPING_COLUMN_NAME)
        time_now = datetime.datetime.utcnow()
        folder_title = competency_framework_name
        bitable_app_name = competency_framework_name
        folder_token = None
        app_token = None
        table_id = None

        # Get mapping lark base - ladder
        mapping_sync_data = asyncio.run(
            SyncLarkBitableMapping().find_one({"competency_framework_id": competency_framework_id})
        )
        table_fields = copy.deepcopy(DEFAULT_FIELDS)
        for job in list_job_title_level:

            # Mapping col name
            job_title_level_id = job.get("job_title_level_id")
            job_title_level_name = job.get("job_title_level_name")
            mapping_job_title_level_name.update({job_title_level_id: job_title_level_name})

            # Field config
            table_fields.append(
                {"field_name": job_title_level_name, "type": 1},
            )
        
        if not mapping_sync_data:  # First time sync

            # Setting permission
            list_user_access_data = []
            for oid in list_access_user_oid:
                list_user_access_data.append(
                    {"user_open_id": oid, "perm": "view"},
                )

            # Create folder
            folder_builder = FolderBuilder()
            folder_info = folder_builder.create_folder_with_permissions(
                folder_name=folder_title,
                list_user_access_data=list_user_access_data,
                need_notification=True,
            )
            folder_token = folder_info["token"]

            # Create new app
            bitable_app_builder = BitableAppBuilder(folder_token)
            app_info = bitable_app_builder.create_app_with_permissions(
                name=bitable_app_name,
                list_user_access_data=list_user_access_data,
                need_notification_add_perm=True,
            )
            app_token = app_info["app"]["app_token"]
            default_table_id = app_info["app"]["default_table_id"]

            # Create new table
            bitable_table_builder = BitableTableBuilder(app_token)
            new_table_params = {
                "table_name": folder_title,
                "default_view_name": bitable_app_name,
                "fields": table_fields,
            }
            table_response = bitable_table_builder.create_table(**new_table_params)
            table_id = table_response["table_id"]

            # Delete default view
            bitable_table_builder.delete_table(table_id=default_table_id)

            # Save mapping
            mapping_sync_data = {
                "competency_framework_id": competency_framework_id,
                "folder_token": folder_token,
                "app_token": app_token,
                "table_id": table_id,
                "type": cls.TYPE,
            }
            asyncio.run(SyncLarkBitableMapping().insert(mapping_sync_data))

        # ================================================== Sync ================================================== #
        # Clear table
        app_token = mapping_sync_data["app_token"]
        table_id = mapping_sync_data["table_id"]
        bitable_records_builder = BitableTableRecordsBuilder(app_token=app_token, table_id=table_id)
        bitable_records_builder.clear_all_records()
        # Setup records
        records = []
        for item in competency_framework_data:
            record_item = {}
            for k, v in item.items():
                if mapping_job_title_level_name.get(k):
                    record_item[mapping_job_title_level_name[k]] = str(v)

            records.append({"fields": record_item})

        # Insert batch records
        bitable_records_builder.create_batch_record(records)
        asyncio.run(
            SyncLarkBitableMapping().update_set_dictionary(
                {"competency_framework_id": competency_framework_id},
                {
                    "app_token": app_token,
                    "table_id": table_id,
                    "action_time": time_now,
                },
            )
        )
        MobioLogging().info(f"{cls.log_prefix()}::_sync_to_base::done")

    @staticmethod
    def consume_data(data):
        cls = SyncBitableCompetencyFrameworkSubscriber
        MobioLogging().info(f"{cls.log_prefix()}::consume_data::{str(data)}")
        list_job_title_level = data["list_job_title_level"]
        department_id = data["department_id"]
        competency_framework_id = data["competency_framework_id"]
        competency_framework_name = data["competency_framework_name"]
        competency_framework_data = data["competency_framework_data"]

        list_access_user_oid = []
        # Get list admin oid
        session = next(get_db())
        user_repo = UserRepository(session)
        list_admin = asyncio.run(user_repo.get_list_admin())
        list_user_in_department = asyncio.run(user_repo.get_users_by_departments([department_id]))

        for u in list_admin + list_user_in_department:
            list_access_user_oid.append(u.open_user_id)

        # Sync with base
        cls._sync_to_base(
            competency_framework_id,
            competency_framework_name,
            list_job_title_level,
            list_access_user_oid,
            competency_framework_data,
        )
        MobioLogging().info(f"{cls.log_prefix()}::consume_data::done")


if __name__ == "__main__":
    data = {
        "list_job_title_level": [
            {"job_title_level_id": "ec4e1232-83fa-4f01-a9ab-058747d676ee", "job_title_level_name": "a"},
            {"job_title_level_id": "2bf1e4d4-5792-4a7c-9ad3-b1ee493862f2", "job_title_level_name": "b"},
            {"job_title_level_id": "6d613bd4-a227-4f93-a163-f11052076e69", "job_title_level_name": "c"},
            {"job_title_level_id": "7290eba1-bdcd-41fc-8317-354280a6fe24", "job_title_level_name": "d"},
            {"job_title_level_id": "6a53f594-d8df-4498-863e-3ad0230ccc24", "job_title_level_name": "Gà mờ"},
            {"job_title_level_id": "215e2d18-a99e-47c0-ad9d-bb34494d9aed", "job_title_level_name": "Cũng cũng"},
        ],
        "department_id": "2227e29a-a475-4b21-b8a8-043d529482b2",
        "competency_framework_id": "67e3b7a3ceb15cea0575858c",
        "competency_framework_name": "Khung năng lực team Phòng content",
        "competency_framework_data": [
            {
                "6a53f594-d8df-4498-863e-3ad0230ccc24": "Mức 1 (1 điểm)",
                "215e2d18-a99e-47c0-ad9d-bb34494d9aed": "Mức 2 (2 điểm)",
                "ec4e1232-83fa-4f01-a9ab-058747d676ee": "Mức 1 (1 điểm)",
                "2bf1e4d4-5792-4a7c-9ad3-b1ee493862f2": "Mức 2 (2 điểm)",
                "6d613bd4-a227-4f93-a163-f11052076e69": "Mức 4 (4 điểm)",
                "7290eba1-bdcd-41fc-8317-354280a6fe24": "Mức 5 (5 điểm)",
                "competency_name": "sửa rùi nefk",
                "competency_group_name": "Nhóm năng lực chuyên môn",
                "weight": 1,
            },
            {
                "6a53f594-d8df-4498-863e-3ad0230ccc24": "Mức 1 (1 điểm)",
                "215e2d18-a99e-47c0-ad9d-bb34494d9aed": "Mức 2 (2 điểm)",
                "ec4e1232-83fa-4f01-a9ab-058747d676ee": "Mức 1 (1 điểm)",
                "2bf1e4d4-5792-4a7c-9ad3-b1ee493862f2": "Mức 2 (2 điểm)",
                "6d613bd4-a227-4f93-a163-f11052076e69": "Mức 4 (4 điểm)",
                "7290eba1-bdcd-41fc-8317-354280a6fe24": "Mức 5 (5 điểm)",
                "competency_name": "12312",
                "competency_group_name": "Nhóm năng lực chuyên môn",
                "weight": 1,
            },
            {
                "6a53f594-d8df-4498-863e-3ad0230ccc24": "Mức 1 (1 điểm)",
                "215e2d18-a99e-47c0-ad9d-bb34494d9aed": "Mức 2 (2 điểm)",
                "ec4e1232-83fa-4f01-a9ab-058747d676ee": "Mức 1 (1 điểm)",
                "2bf1e4d4-5792-4a7c-9ad3-b1ee493862f2": "Mức 1 (1 điểm)",
                "6d613bd4-a227-4f93-a163-f11052076e69": "Mức 1 (1 điểm)",
                "7290eba1-bdcd-41fc-8317-354280a6fe24": "Mức 2 (2 điểm)",
                "competency_name": "12312",
                "competency_group_name": "Nhóm năng lực chuyên môn",
                "weight": 1,
            },
            {
                "6a53f594-d8df-4498-863e-3ad0230ccc24": "Mức 3 (3 điểm)",
                "215e2d18-a99e-47c0-ad9d-bb34494d9aed": "Mức 4 (4 điểm)",
                "ec4e1232-83fa-4f01-a9ab-058747d676ee": "Mức 1 (1 điểm)",
                "2bf1e4d4-5792-4a7c-9ad3-b1ee493862f2": "Mức 4 (4 điểm)",
                "6d613bd4-a227-4f93-a163-f11052076e69": "Mức 2 (2 điểm)",
                "7290eba1-bdcd-41fc-8317-354280a6fe24": "Mức 5 (5 điểm)",
                "competency_name": "12312",
                "competency_group_name": "Nhóm năng lực chuyên môn",
                "weight": 1,
            },
        ],
    }
    SyncBitableCompetencyFrameworkSubscriber.consume_data(data)
