import asyncio
import datetime
from slugify import slugify

from src.common.common import LarkDocsTemplateEnum, RedisSubscriberChannel
from src.libs.lark_docs_sdk.builder.bitable import BitableAppBuilder, BitableTableBuilder, BitableTableRecordsBuilder
from src.libs.lark_docs_sdk.builder.folder import FolderBuilder
from src.models.mongo.sync_lark_bitable_mapping import SyncLarkBitableMapping
from src.models.mongo.lark_docs_template_model import LarkDocsTemplateModel
from src.redis.pubsub.subscribers.base_subscriber import BaseSubscriber
from mobio.libs.logging import MobioLogging


MAPPING_COLUMN_NAME = {
    "competency_name": "Tên NL",
    "competency_group_name": "Nhóm NL",
    "weight": "Trọng số",
    "point_min": "Điểm đánh giá - Điểm tối thiểu theo cấp độ",
    "point_self_evaluate": "Điể<PERSON> đ<PERSON>h giá - <PERSON>ự đánh giá",
    "point_leader_evaluate": "Điểm đánh giá - <PERSON>uản lý đánh giá",
    "gained_point_min": "Điểm đạt được - Điểm tối thiểu theo cấp độ",
    "gained_point_self_evaluate": "Điểm đạt được - Tự đánh giá",
    "gained_point_leader_evaluate": "Điểm đạt được - Quản lý đánh giá",
    "gap": "GAP",
}

DEFAULT_FIELDS = [
    {"field_name": MAPPING_COLUMN_NAME["competency_name"], "type": 1},
    {"field_name": MAPPING_COLUMN_NAME["competency_group_name"], "type": 1},
    {"field_name": MAPPING_COLUMN_NAME["weight"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["point_min"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["point_self_evaluate"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["point_leader_evaluate"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["gained_point_min"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["gained_point_self_evaluate"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["gained_point_leader_evaluate"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["gap"], "type": 2},
]


class SyncBitableDetailEvaluationSubscriber(BaseSubscriber):
    TYPE = "detail_evaluation"
    CHANNEL = RedisSubscriberChannel.SYNC_BITABLE_DETAIL_EVALUATION_CHANNEL

    @staticmethod
    def _get_default_table(app_token, default_table_name):
        builder = BitableTableBuilder(app_token)
        list_table_response = builder.get_list_tables(per_page=100)
        for table in list_table_response.get("items", []):
            name = table["name"]
            if slugify(name) == slugify(default_table_name):
                return table

    @staticmethod
    def _sync_to_base(evaluation_id, user_oid, user_name, leader_user_oid, records):
        # Define const
        cls = SyncBitableDetailEvaluationSubscriber
        time_now = datetime.datetime.utcnow()
        folder_title = "Đánh giá năng lực kỳ"
        bitable_app_name = f"Đánh giá {user_name}"
        folder_token = None
        app_token = None
        table_id = None

        # Get mapping lark base - ladder
        mapping_sync_data = asyncio.run(
            SyncLarkBitableMapping().find_one({"evaluation_id": evaluation_id, "type": cls.TYPE})
        )
        if not mapping_sync_data:  # First time sync
            # Create folder
            folder_builder = FolderBuilder()
            folder_info = folder_builder.create_folder_with_permissions(
                folder_name=folder_title,
                list_user_access_data=[
                    {"user_open_id": leader_user_oid, "perm": "view"},
                    {"user_open_id": user_oid, "perm": "view"},
                ],
                need_notification=True,
            )
            folder_token = folder_info["token"]

            # Create app
            # Check template
            template = asyncio.run(
                LarkDocsTemplateModel().get_template_by_type(
                    LarkDocsTemplateEnum.Type.BITABLE,
                    LarkDocsTemplateEnum.Key.Bitable.DETAIL_EVALUATION,
                )
            )
            bitable_app_builder = BitableAppBuilder(folder_token)

            # Setting permission
            list_user_access_data = [
                {"user_open_id": leader_user_oid, "perm": BitableAppBuilder.Permissions.FULL_ACCESS}
            ]
            if bool(template):
                # Clone app from template
                template_app_token = template["template_config"]["app_token"]
                app_info = bitable_app_builder.copy_app_with_permissions(
                    app_token=template_app_token,
                    name=bitable_app_name,
                    without_content=False,
                    list_user_access_data=list_user_access_data,
                    need_notification_add_perm=True,
                )
                app_token = app_info["app"]["app_token"]

                # Get table default id
                default_table_name = template["template_config"]["default_table_name"]
                default_table = SyncBitableDetailEvaluationSubscriber._get_default_table(
                    app_token=app_token, default_table_name=default_table_name
                )
                table_id = default_table["table_id"]
            else:
                # Create new app
                app_info = bitable_app_builder.create_app_with_permissions(
                    name=bitable_app_name,
                    list_user_access_data=list_user_access_data,
                    need_notification_add_perm=True,
                )
                app_token = app_info["app"]["app_token"]
                default_table_id = app_info["app"]["default_table_id"]

                # Create new table
                bitable_table_builder = BitableTableBuilder(app_token)
                new_table_params = {
                    "table_name": folder_title,
                    "default_view_name": bitable_app_name,
                    "fields": DEFAULT_FIELDS,
                }
                table_response = bitable_table_builder.create_table(**new_table_params)
                table_id = table_response["table_id"]

                # Delete default view
                bitable_table_builder.delete_table(table_id=default_table_id)

            # Save mapping
            mapping_sync_data = {
                "evaluation_id": evaluation_id,
                "folder_token": folder_token,
                "app_token": app_token,
                "table_id": table_id,
                "type": SyncBitableDetailEvaluationSubscriber.TYPE,
            }
            asyncio.run(SyncLarkBitableMapping().insert(mapping_sync_data))

        # ================================================== Sync ================================================== #
        # Clear table
        app_token = mapping_sync_data["app_token"]
        table_id = mapping_sync_data["table_id"]
        bitable_records_builder = BitableTableRecordsBuilder(app_token=app_token, table_id=table_id)
        bitable_records_builder.clear_all_records()
        # Insert batch records
        bitable_records_builder.create_batch_record(records)
        asyncio.run(
            SyncLarkBitableMapping().update_set_dictionary(
                {"evaluation_id": evaluation_id},
                {
                    "app_token": app_token,
                    "table_id": table_id,
                    "action_time": time_now,
                },
            )
        )
        MobioLogging().info(f"{cls.log_prefix()}::_sync_to_base::done")

    @staticmethod
    def consume_data(data):
        cls = SyncBitableDetailEvaluationSubscriber
        MobioLogging().info(f"{cls.log_prefix()}::consume_data::{str(data)}")
        evaluation_id = data["id"]
        user_name = data["user_name"]
        user_oid = data["user_open_id"]
        leader_user_oid = data["leader_open_id"]
        evaluation_data = data["evaluation_data"]

        # Process
        bitable_records = []
        for r in evaluation_data:
            competency_name = r.get("competency_name", "")
            competency_group_name = r.get("competency_group_name", "")
            weight = r.get("weight", 0)
            point_min = r.get("point_min", 0)

            # Get point evaluate
            point_self_evaluate = 0
            point_leader_evaluate = 0
            points = r.get("points", [])
            for item in points:
                point_value = item.get("point", 0)
                user_type = item.get("user_type")
                if user_type == 1:
                    point_self_evaluate = point_value
                elif user_type == 2:
                    point_leader_evaluate = point_value

            # gained_point_min
            gained_point_min = weight * point_min

            # gained_point_self_evaluate
            gained_point_self_evaluate = weight * point_self_evaluate

            # gained_point_leader_evaluate
            gained_point_leader_evaluate = weight * point_leader_evaluate

            # gap
            gap = gained_point_leader_evaluate - gained_point_min

            bitable_records.append(
                {
                    "fields": {
                        MAPPING_COLUMN_NAME["competency_name"]: competency_name,
                        MAPPING_COLUMN_NAME["competency_group_name"]: competency_group_name,
                        MAPPING_COLUMN_NAME["weight"]: weight,
                        MAPPING_COLUMN_NAME["point_min"]: point_min,
                        MAPPING_COLUMN_NAME["point_self_evaluate"]: point_self_evaluate,
                        MAPPING_COLUMN_NAME["point_leader_evaluate"]: point_leader_evaluate,
                        MAPPING_COLUMN_NAME["gained_point_min"]: gained_point_min,
                        MAPPING_COLUMN_NAME["gained_point_self_evaluate"]: gained_point_self_evaluate,
                        MAPPING_COLUMN_NAME["gained_point_leader_evaluate"]: gained_point_leader_evaluate,
                        MAPPING_COLUMN_NAME["gap"]: gap,
                    }
                }
            )
        cls._sync_to_base(
            evaluation_id=evaluation_id,
            user_name=user_name,
            user_oid=user_oid,
            leader_user_oid=leader_user_oid,
            records=bitable_records,
        )

        MobioLogging().info(f"{cls.log_prefix()}::consume_data::done")


if __name__ == "__main__":
    data = {
        "id": "sonnn_test",
        "user_open_id": "ou_029e840785e7d1a042be142850ad9dec",
        "leader_open_id": "ou_029e840785e7d1a042be142850ad9dec",
        "evaluation_data": [
            {
                "competency_id": "<competency_id>",
                "name": "<name>",
                "competency_group_id": "<competency_id>",
                "competency_group_name": "<competency_group_name>",
                "weight": 1,
                "point_min": 1,
                "points": [
                    {
                        "point": 15,
                        "user_type": "user",
                    },
                    {
                        "point": 1,
                        "user_type": "leader",
                    },
                ],
            },
            {
                "competency_id": "<competency_id>",
                "name": "<name>",
                "competency_group_id": "<competency_id>",
                "competency_group_name": "<competency_group_name>",
                "weight": 1,
                "point_min": 1,
                "points": [
                    {
                        "point": 15,
                        "user_type": "user",
                    },
                    {
                        "point": 1,
                        "user_type": "leader",
                    },
                ],
            },
            {
                "competency_id": "<competency_id>",
                "name": "<name>",
                "competency_group_id": "<competency_id>",
                "competency_group_name": "<competency_group_name>",
                "weight": 1,
                "point_min": 1,
                "points": [
                    {
                        "point": 15,
                        "user_type": "user",
                    },
                    {
                        "point": 1,
                        "user_type": "leader",
                    },
                ],
            },
            {
                "competency_id": "<competency_id>",
                "name": "<name>",
                "competency_group_id": "<competency_id>",
                "competency_group_name": "<competency_group_name>",
                "weight": 1,
                "point_min": 1,
                "points": [
                    {
                        "point": 15,
                        "user_type": "user",
                    },
                    {
                        "point": 1,
                        "user_type": "leader",
                    },
                ],
            },
            {
                "competency_id": "<competency_id>",
                "name": "<name>",
                "competency_group_id": "<competency_id>",
                "competency_group_name": "<competency_group_name>",
                "weight": 1,
                "point_min": 3,
                "points": [
                    {
                        "point": 1,
                        "user_type": "user",
                    },
                    {
                        "point": 1,
                        "user_type": "leader",
                    },
                ],
            },
        ],
    }

    SyncBitableDetailEvaluationSubscriber.consume_data(data=data)
