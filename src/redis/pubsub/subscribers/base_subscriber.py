import json
import time

from mobio.libs.logging import MobioLogging

import redis
from configs import ApplicationConfig

pool = redis.ConnectionPool.from_url(ApplicationConfig.REDIS_URI)


class BaseSubscriber:

    CHANNEL = ""

    def __init__(self) -> None:
        if not self.CHANNEL:
            raise AttributeError("CHANNEL of subscriber is required")

    @classmethod
    def log_prefix(cls):
        return f"RedisSubscriber::{cls.CHANNEL}"

    @staticmethod
    def consume_data(data):
        """
        Implement this func to consume data
        """

    @classmethod
    def subscribe_messages(cls):
        try:
            redis_client = redis.Redis(connection_pool=pool)

            # Sub to channel
            pubsub = redis_client.pubsub()
            pubsub.subscribe(cls.CHANNEL)
            MobioLogging().info(f"{cls.log_prefix()}::subscribe successfully!!")
            while True:
                time.sleep(1)
                message = pubsub.get_message()
                if not message:
                    continue
                MobioLogging().info(f"{cls.log_prefix()}::received message::{message}")
                if message["type"] == "message":
                    data = json.loads(message["data"])
                    if data:
                        MobioLogging().info(f"{cls.CHANNEL}::received data::{data}")
                        cls.consume_data(data)
        except Exception as e:
            MobioLogging().info(f"{cls.log_prefix()}::error::{str(e)}")
