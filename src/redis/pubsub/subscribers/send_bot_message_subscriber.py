from mobio.libs.logging import MobioLogging
from src.common.common import RedisSubscriberChannel
from src.libs.lark_docs_sdk.oapi.bot_send_msg import BotSendMessageApi
from src.redis.pubsub.subscribers.base_subscriber import BaseSubscriber


class SendBotMessageSubscriber(BaseSubscriber):

    CHANNEL = RedisSubscriberChannel.SEND_BOT_MESSAGE

    class SendType:
        SEND_MESSAGE = "send_message"
        SEND_BATCH_MESSAGES = "send_batch_messages"

    @staticmethod
    def consume_data(data):
        """
        Args:
            data (_type_): _description_
            {
                "send_type": # Required, One of: send_message, send_batch_messages,
                "receive_id_type":# Required if send_type = send_message -> Param for BotSendMessageApi
                "payload":# Required -> Param for BotSendMessageApi
            }
        """

        cls = SendBotMessageSubscriber
        MobioLogging().info(f"{cls.log_prefix()}::consume_data::{str(data)}")

        # Init params
        try:
            send_type = data.get("send_type", cls.SendType.SEND_MESSAGE)
            receive_id_type = data.get("receive_id_type")
            payload = data.get("payload")

            # Validate
            if not payload:
                raise ValueError("payload is empty")

            if send_type not in (cls.SendType.SEND_MESSAGE, cls.SendType.SEND_BATCH_MESSAGES):
                raise ValueError("send_type not allowed")

            # Send message
            if send_type == cls.SendType.SEND_MESSAGE:
                BotSendMessageApi().send_message(receive_id_type=receive_id_type, payload=payload)

            elif send_type == cls.SendType.SEND_BATCH_MESSAGES:
                BotSendMessageApi().send_messages_in_batches(payload=payload)

            MobioLogging().info(f"{cls.log_prefix()}::consume_data::done")
        except Exception as e:
            MobioLogging().error(f"{cls.log_prefix()}::consume_data::error::{str(e)}")


if __name__ == "__main__":
    from src.redis.pubsub import publisher
    from src.common.common import RedisSubscriberChannel
    import time
    import datetime

    t = str(datetime.datetime.utcnow())
    publisher.publish_message(
        RedisSubscriberChannel.SEND_BOT_MESSAGE,
        {
            # "send_type": SendBotMessageSubscriber.SendType.SEND_BATCH_MESSAGES,
            # "payload": {
            #     "department_ids": ["od-399f6d9eda761bd24cf96b3c6d8183d2"],
            #     "msg_type": "interactive",
            #     "card": {
            #         "config": {"wide_screen_mode": True},
            #         "elements": [{"tag": "div", "text": {"content": "Build failed 🤒🤒🤒🤒~~~ %s" % t, "tag": "plain_text"}}],
            #         "header": {"template": "blue", "title": {"content": "Jenkins - Ladder", "tag": "plain_text"}},
            #     },
            # },
        },
    )
