import asyncio
import datetime
import os

from bson import ObjectId
from mobio.libs.logging import MobioLogging
from slugify import slugify

from configs.database import get_db
from src.common.choices import EvaluateStatusFilterChoice, UserEvaluateTypeChoice
from src.common.common import LarkDocsTemplateEnum, RedisSubscriberChannel
from src.libs.lark_docs_sdk.builder.bitable import (
    BitableAppBuilder,
    BitableTableBuilder,
    BitableTableRecordsBuilder,
)
from src.libs.lark_docs_sdk.builder.folder import FolderBuilder
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.mongo.lark_docs_template_model import LarkDocsTemplateModel
from src.models.mongo.sync_lark_bitable_mapping import SyncLarkBitableMapping
from src.redis.pubsub.subscribers.base_subscriber import BaseSubscriber
from src.repositories.user_department_repository import UserDepartmentRepository
from src.repositories.users_repository import UserRepository
from src.utils import send_lark_notify

MAPPING_COLUMN_NAME = {
    "user_name": "Họ và tên",
    "department_name": "Phòng ban",
    # "job_title_name": "Chức danh",
    "status": "Trạng thái đánh giá",
    "total_gained_point_min": "Tổng điểm tối thiểu theo cấp độ",
    "total_gained_point_self_evaluate": "Tổng tự đánh giá",
    "total_gained_point_leader_evaluate": "Tổng quản lý đánh giá",
    "gap": "GAP",
}

DEFAULT_FIELDS = [
    {"field_name": MAPPING_COLUMN_NAME["user_name"], "type": 1},
    {"field_name": MAPPING_COLUMN_NAME["department_name"], "type": 1},
    {"field_name": MAPPING_COLUMN_NAME["status"], "type": 1},
    {"field_name": MAPPING_COLUMN_NAME["total_gained_point_min"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["total_gained_point_self_evaluate"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["total_gained_point_leader_evaluate"], "type": 2},
    {"field_name": MAPPING_COLUMN_NAME["gap"], "type": 2},
]


class SyncBitableEvaluationAllUserSubscriber(BaseSubscriber):
    TYPE = "evaluation_all_user"
    CHANNEL = RedisSubscriberChannel.SYNC_BITABLE_EVALUATION_ALL_USER_CHANNEL

    @staticmethod
    def _get_default_table(app_token, default_table_name):
        builder = BitableTableBuilder(app_token)
        list_table_response = builder.get_list_tables(per_page=100)
        for table in list_table_response.get("items", []):
            name = table["name"]
            if slugify(name) == slugify(default_table_name):
                return table

    @staticmethod
    def _sync_to_base(evaluate_period_id, evaluate_period_name, list_access_user_oid, records):
        # Define const
        cls = SyncBitableEvaluationAllUserSubscriber
        time_now = datetime.datetime.utcnow()
        folder_title = evaluate_period_name
        bitable_app_name = evaluate_period_name
        folder_token = None
        app_token = None
        table_id = None

        # Get mapping lark base - ladder
        mapping_sync_data = asyncio.run(
            SyncLarkBitableMapping().find_one({"evaluate_period_id": evaluate_period_id, "type": cls.TYPE})
        )
        if not mapping_sync_data:  # First time sync
            # Setting permission
            list_user_access_data = []
            for oid in list_access_user_oid:
                list_user_access_data.append(
                    {"user_open_id": oid, "perm": "full_access"},
                )

            # Create folder
            folder_builder = FolderBuilder()
            folder_info = folder_builder.create_folder_with_permissions(
                folder_name=folder_title,
                list_user_access_data=list_user_access_data,
                need_notification=True,
            )
            folder_token = folder_info["token"]

            # Create app
            # Check template
            template = asyncio.run(
                LarkDocsTemplateModel().get_template_by_type(
                    LarkDocsTemplateEnum.Type.BITABLE,
                    LarkDocsTemplateEnum.Key.Bitable.EVALUATION_ALL_USER,
                )
            )
            bitable_app_builder = BitableAppBuilder(folder_token)

            # Setting permission
            if bool(template):
                # Clone app from template
                template_app_token = template["template_config"]["app_token"]
                app_info = bitable_app_builder.copy_app_with_permissions(
                    app_token=template_app_token,
                    name=bitable_app_name,
                    without_content=False,
                    list_user_access_data=list_user_access_data,
                    need_notification_add_perm=True,
                )
                app_token = app_info["app"]["app_token"]

                # Get table default id
                default_table_name = template["template_config"]["default_table_name"]
                default_table = SyncBitableEvaluationAllUserSubscriber._get_default_table(
                    app_token=app_token, default_table_name=default_table_name
                )
                table_id = default_table["table_id"]
            else:
                # Create new app
                app_info = bitable_app_builder.create_app_with_permissions(
                    name=bitable_app_name,
                    list_user_access_data=list_user_access_data,
                    need_notification_add_perm=True,
                )
                app_token = app_info["app"]["app_token"]
                default_table_id = app_info["app"]["default_table_id"]

                # Create new table
                bitable_table_builder = BitableTableBuilder(app_token)
                new_table_params = {
                    "table_name": folder_title,
                    "default_view_name": bitable_app_name,
                    "fields": DEFAULT_FIELDS,
                }
                table_response = bitable_table_builder.create_table(**new_table_params)
                table_id = table_response["table_id"]

                # Delete default view
                bitable_table_builder.delete_table(table_id=default_table_id)

            # Save mapping
            mapping_sync_data = {
                "evaluate_period_id": evaluate_period_id,
                "folder_token": folder_token,
                "app_token": app_token,
                "table_id": table_id,
                "type": SyncBitableEvaluationAllUserSubscriber.TYPE,
            }
            asyncio.run(SyncLarkBitableMapping().insert(mapping_sync_data))

        # ================================================== Sync ================================================== #
        # Clear table
        app_token = mapping_sync_data["app_token"]
        table_id = mapping_sync_data["table_id"]
        bitable_records_builder = BitableTableRecordsBuilder(app_token=app_token, table_id=table_id)
        bitable_records_builder.clear_all_records()
        # Insert batch records
        bitable_records_builder.create_batch_record(records)
        asyncio.run(
            SyncLarkBitableMapping().update_set_dictionary(
                {"evaluate_period_id": evaluate_period_id},
                {
                    "app_token": app_token,
                    "table_id": table_id,
                    "action_time": time_now,
                },
            )
        )
        MobioLogging().info(f"{cls.log_prefix()}::_sync_to_base::done")

    @staticmethod
    def consume_data(data):
        time_now = datetime.datetime.utcnow()
        cls = SyncBitableEvaluationAllUserSubscriber
        MobioLogging().info(f"{cls.log_prefix()}::consume_data::{str(data)}")

        evaluate_period_id = data["evaluate_period_id"]
        status_filter_old = data.get("status_filter_old")
        status_filter_new = data.get("status_filter_new")
        user_action_id = data.get("user_action_id")
        evaluate_id = data.get("evaluate_id")

        # Current evaluate info
        evaluate_period = asyncio.run(
            EvaluatePeriodModel().get_evaluate_period_by_id(evaluate_period_id=evaluate_period_id)
        )
        company_id = evaluate_period["company_id"]
        evaluate_period_id = str(evaluate_period["_id"])
        evaluate_period_name = evaluate_period["name"]
        competency_framework_name = evaluate_period_name.strip()

        # Query data
        session = next(get_db())
        list_evaluation = asyncio.run(
            EvaluateModel().find(
                {"evaluate_period_id": ObjectId(evaluate_period_id), "_id": ObjectId(evaluate_id)},
                obj_field_select=[
                    "_id",
                    "user_id",
                    "status",
                    "time_eval_of_users",
                    "competency_groups",
                    "evaluate_period_id",
                ],
            )
        )
        MobioLogging().info(f"list_evaluation: {list_evaluation}")
        mapping_evaluation_data = {}
        for evaluation in list_evaluation:
            evaluation_id = str(evaluation["_id"])
            period_id = str(evaluation["evaluate_period_id"])
            after_job_title_level_id = evaluation.get("after_job_title_level_id")
            total_gained_point_min = 0
            total_gained_point_self_evaluate = 0
            total_gained_point_leader_evaluate = 0
            user_id = evaluation["user_id"]
            status = evaluation["status"]
            # Logic
            # Get point evaluate
            competency_groups = evaluation.get("competency_groups", [])

            for competency_group_item in competency_groups:
                lst_competency = competency_group_item.get("lst_competency", [])
                for competency in lst_competency:
                    weight = competency.get("weight", 0)
                    point_min = competency.get("point_min", 0)
                    list_point = competency.get("points", [])

                    # gained_point_min
                    total_gained_point_min += weight * point_min

                    for point in list_point:
                        point_value = point.get("point", 0)
                        user_type = point.get("user_type")
                        # Self point
                        if user_type == 1:
                            total_gained_point_self_evaluate += weight * point_value
                        # Leader point
                        elif user_type == 2:
                            total_gained_point_leader_evaluate += weight * point_value

            # Get status str
            time_eval_of_users = evaluation["time_eval_of_users"]
            leader_id = None
            user_submit_id = None
            # if status_filter_new == EvaluateStatusFilterChoice.COMPLETED.value:
            #     status_str = "Hoàn thành"
            # else:
            mapping_user_type_submit_time = {}
            time_eval_of_users = sorted(time_eval_of_users, key=lambda x: x["user_type"])
            status_str = "Chờ Quản Lý đánh giá"

            for time_eval in time_eval_of_users:
                user_type = time_eval["user_type"]
                user_id = time_eval["user_id"]
                submit_time = time_eval.get("submit_time")
                mapping_user_type_submit_time[user_type] = submit_time
                if user_type == UserEvaluateTypeChoice.OWNER.value and submit_time:
                    user_submit_id = user_id
                    status_str = "Chờ Quản Lý đánh giá"
                if mapping_user_type_submit_time.get(UserEvaluateTypeChoice.OWNER.value) and user_type == 2:
                    leader_id = user_id
                    status_str = "Hoàn thành" if submit_time else "Chờ Quản Lý đánh giá"

            MobioLogging().info(f"status_str: {status_str}")
            link_access = os.path.join(
                os.environ.get("PUBLIC_HOST"),
                f"evaluate-result/detail?id={evaluation_id}&period_id={period_id}",
            )
            user_action_info = asyncio.run(UserRepository(session).get_user_by_id(user_action_id))
            leader_by_user_submit = asyncio.run(UserRepository(session).get_leader_user_ids([user_submit_id]))
            if user_submit_id == user_action_id:
                title = "Thực hiện đánh giá"
                for leader_id in leader_by_user_submit:
                    leader_info = asyncio.run(UserRepository(session).get_user_by_id(leader_id[0]))
                    if status_filter_old in [
                        EvaluateStatusFilterChoice.WAITING_LEADER.value,
                        EvaluateStatusFilterChoice.COMPLETED.value,
                    ]:
                        title = "Thông báo"
                        text = f"{user_action_info.name} vừa cập nhật lại kết quả đánh giá [{competency_framework_name}]({link_access})."
                    else:
                        text = f"{user_action_info.name} đã hoàn thành NV tự đánh giá, bạn vui lòng thực hiện bước Quản lý đánh giá tại [{competency_framework_name}]({link_access})."
                    send_lark_notify.send_simple_text(
                        open_ids=[leader_info.open_user_id], message_text=text, title=title
                    )
            if user_action_id == leader_id:
                user_department = asyncio.run(
                    UserDepartmentRepository(session).get_department_by_user_id(user_action_info.user_id)
                )
                user_member_info = asyncio.run(UserRepository(session).get_user_by_id(user_submit_id))

                if user_department:
                    lst_user_send = [user_member_info.open_user_id]
                    text = ""
                    title = "Thông báo"
                    if status_filter_old in [
                        EvaluateStatusFilterChoice.WAITING_LEADER.value,
                    ]:
                        owner_departments = user_department.owners
                        user_in_departments = user_department.users
                        for user_in_department in user_in_departments:
                            if (
                                user_in_department.user_id in owner_departments
                                and user_in_department.user_id != user_action_id
                            ):
                                lst_user_send.append(user_in_department.open_user_id)
                        text = f"Bản đánh giá [{competency_framework_name}]({link_access}) của {user_member_info.name} {user_department.name} đã hoàn thành."
                    if status_filter_old in [
                        EvaluateStatusFilterChoice.COMPLETED.value,
                    ]:
                        title = "Thông báo"
                        text = f"{user_action_info.name} vừa cập nhật lại kết quả đánh giá [{competency_framework_name}]({link_access})."

                    if lst_user_send and text:
                        send_lark_notify.send_simple_text(
                            open_ids=lst_user_send,
                            message_text=text,
                            title=title,
                        )
        mapping_data_send_noti_leader = {user_submit_id: None, leader_id: None}

        # Add to mapping
        mapping_evaluation_data[user_id] = {
            "status": status_str,
            "total_gained_point_min": total_gained_point_min,
            "total_gained_point_self_evaluate": total_gained_point_self_evaluate,
            "total_gained_point_leader_evaluate": total_gained_point_leader_evaluate,
            "gap": total_gained_point_leader_evaluate - total_gained_point_min,
        }

        # Query user
        list_access_user_oid = []
        list_all_user = asyncio.run(UserRepository(session).get_all_user_by_company_id(company_id))
        bitable_records = []
        for user in list_all_user:

            if user.user_id in mapping_data_send_noti_leader:
                mapping_data_send_noti_leader[user.user_id] = user

            if not user.roles or not user.departments:
                continue

            if user.roles[0].name == "admin":
                list_access_user_oid.append(user.open_user_id)
            else:
                evaluation_data = mapping_evaluation_data.get(user.user_id, {})
                status = (
                    evaluation_data.get("status", "Chờ nhân viên đánh giá")
                    if evaluation_data
                    else "Chưa có bản đánh giá"
                )
                bitable_records.append(
                    {
                        "fields": {
                            MAPPING_COLUMN_NAME["user_name"]: user.name,
                            MAPPING_COLUMN_NAME["department_name"]: user.departments[0].name,
                            # MAPPING_COLUMN_NAME["job_title_name"]: user.job_title_id,
                            MAPPING_COLUMN_NAME["status"]: status,
                            MAPPING_COLUMN_NAME["total_gained_point_min"]: evaluation_data.get(
                                "total_gained_point_min", 0
                            ),
                            MAPPING_COLUMN_NAME["total_gained_point_self_evaluate"]: evaluation_data.get(
                                "total_gained_point_self_evaluate", 0
                            ),
                            MAPPING_COLUMN_NAME["total_gained_point_leader_evaluate"]: evaluation_data.get(
                                "total_gained_point_leader_evaluate", 0
                            ),
                            MAPPING_COLUMN_NAME["gap"]: evaluation_data.get("gap", 0),
                        }
                    }
                )
        cls._sync_to_base(
            evaluate_period_id=evaluate_period_id,
            evaluate_period_name=evaluate_period_name,
            list_access_user_oid=list_access_user_oid,
            records=bitable_records,
        )

        # if status_str == "Chờ Quản Lý đánh giá" and leader_id:
        #     MobioLogging().info("Send notification leader ")
        #     leader = mapping_data_send_noti_leader.get(leader_id)
        #     text_send_noti = f"{leader.name} đã hoàn thành NV tự đánh giá, bạn vui lòng thực hiện bước Quản lý đánh giá tại {competency_framework_name}"
        #     SendNotiToUserController().send_message_alert_lark(leader, text_send_noti)

        MobioLogging().info(f"{cls.log_prefix()}::consume_data::done")


if __name__ == "__main__":
    SyncBitableEvaluationAllUserSubscriber.consume_data(
        data={
            "evaluate_period_id": "68774ad9f81be1452c325210",
            "evaluate_id": "68797d92d6beddd9d5debe01",
            "status_filter_old": "waiting_leader",
            "status_filter_new": "waiting_leader",
            "user_action_id": "aae64578-d998-4eec-af95-c62bfad1477e",
        }
    )
