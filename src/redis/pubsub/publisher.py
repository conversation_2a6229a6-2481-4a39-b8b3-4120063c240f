import json
import redis
from mobio.libs.logging import <PERSON><PERSON>Logging

from configs import ApplicationConfig

from functools import lru_cache

@lru_cache
def get_redis_pool():
    return redis.ConnectionPool.from_url(ApplicationConfig.REDIS_URI)

def publish_message(channel, message):
    log_prefix = f"RedisPublisher::{channel}"
    redis_client = redis.Redis(connection_pool=get_redis_pool())
    try:
        MobioLogging().info(f"{log_prefix}::publishing message::{str(message)}")
        redis_client.publish(channel, json.dumps(message, default=str))
        MobioLogging().info(f"{log_prefix}::publishing message::successfully!!")
        return True
    except Exception as e:
        MobioLogging().error(f"{log_prefix}::publishing message::{str(message)}::error::{str(e)}")
        return False