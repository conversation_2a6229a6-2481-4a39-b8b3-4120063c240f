#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 23/01/2025
"""
from mobio.libs.logging import MobioLogging

from src.common.caching import lru_cache_m_caching


class BaseCachingRedis:
    """ """

    EXPIRATION_DEFAULT = 60 * 60

    @staticmethod
    def hash_key(*args):
        return lru_cache_m_caching.cache_prefix + "#" + "#".join([str(a) for a in args])

    @staticmethod
    def get_value_by_key_not_hash(key):
        try:
            return lru_cache_m_caching.cache._redis.get(key)
        except Exception as e:
            MobioLogging().error(f"get_value_by_key_not_hash :: error :: {e}")
            return None

    def get_value_by_key(self, *args):
        has_key = lru_cache_m_caching.cache_prefix + "#" + self.hash_key(*args)

        try:
            return lru_cache_m_caching.cache._redis.get(has_key)
        except:
            return None

    def set_value_by_key_not_hash(self, key, value, expiration=None):
        if not expiration:
            expiration = self.EXPIRATION_DEFAULT
        # Set cache
        lru_cache_m_caching.cache._redis.set(key, value, expiration)
        return value

    def set_value_by_key(self, *args, value, expiration=None):
        if not expiration:
            expiration = self.EXPIRATION_DEFAULT
        has_key = self.hash_key(*args)
        # Set cache
        lru_cache_m_caching.cache._redis.set(has_key, value, expiration=expiration)
        return value

    @classmethod
    def delete_cache_by_key(cls, key):
        return lru_cache_m_caching.cache._redis.delete(key)
