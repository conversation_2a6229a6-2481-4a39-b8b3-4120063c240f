#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: tungdd
Date created: 05/01/2024
"""
import datetime
from typing import List

from sqlalchemy import (
    JSON,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    Sequence,
    String,
    event,
    func,
    select,
)
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import Mapped, relationship
from unidecode import unidecode

from src.models.postgres import EntityMeta
from src.utils.attrs import generate_uuid


class BaseModel(EntityMeta):
    __abstract__ = True
    status = Column(Integer, nullable=False, default=1)
    created_by = Column(String, default="admin")
    updated_by = Column(String, default="admin")
    created_time = Column(DateTime, default=datetime.datetime.utcnow())
    updated_time = Column(DateTime, default=datetime.datetime.utcnow())

    __table_args__ = {"extend_existing": True}


def generate_lower_case_name(mapper, connection, target):
    if target.name:
        target.lower_case_name = target.name.lower()


def generate_unsigned_name(mapper, connection, target):
    if target.name:
        target.unsigned_name = unidecode(target.name).lower()


class CompanyModel(BaseModel):
    __tablename__ = "company"
    company_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    tenant_key = Column(String)
    name = Column(String)
    avatar = Column(String)


class UserDepartmentModel(BaseModel):
    __tablename__ = "user_department"
    user_department_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    user_id = Column(String, ForeignKey("user.user_id", use_alter=True), index=True)
    department_id = Column(String, ForeignKey("department.department_id", use_alter=True), index=True)
    department = relationship("DepartmentModel", back_populates="user_departments")


class DepartmentModel(BaseModel):
    __tablename__ = "department"
    department_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    department_code = Column(String)
    lark_department_id = Column(String)
    open_department_id = Column(String)
    display = Column(Integer, default=1)
    name = Column(String, unique=True)
    lower_case_name = Column(String, unique=True)
    description = Column(String)
    order = Column(Integer, default=0)
    owners = Column(ARRAY(String), default=[])

    users: Mapped[List["UserModel"]] = relationship(
        "UserModel", secondary=UserDepartmentModel.__tablename__, back_populates="departments"
    )
    user_departments = relationship("UserDepartmentModel", back_populates="department")


event.listen(DepartmentModel, "before_insert", generate_lower_case_name)


class UserSubDepartmentModel(BaseModel):
    __tablename__ = "user_sub_department"
    user_sub_department_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    user_id = Column(String, ForeignKey("user.user_id", use_alter=True), index=True)
    sub_department_id = Column(String, ForeignKey("sub_department.sub_department_id", use_alter=True), index=True)


class SubDepartmentModel(BaseModel):
    __tablename__ = "sub_department"
    sub_department_id = Column(String, primary_key=True, default=generate_uuid, index=True, unique=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    code = Column(String, primary_key=True, unique=True)
    name = Column(String)
    department_id = Column(String, ForeignKey("department.department_id", use_alter=True))
    description = Column(String)

    users: Mapped[List["UserModel"]] = relationship(
        "UserModel", secondary=UserSubDepartmentModel.__tablename__, back_populates="sub_departments"
    )


class UserRoleModel(BaseModel):
    __tablename__ = "user_role"
    user_role_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    user_id = Column(String, ForeignKey("user.user_id", use_alter=True), index=True)
    role_id = Column(String, ForeignKey("role.role_id", use_alter=True), index=True)


class RolePermissionModel(BaseModel):
    __tablename__ = "role_permission"
    role_permission_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    role_id = Column(String, ForeignKey("role.role_id", use_alter=True), index=True)
    permission_id = Column(String, ForeignKey("permission.permission_id", use_alter=True), index=True)


class JobTitleModel(BaseModel):
    __tablename__ = "job_title"

    job_title_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    department_id = Column(String, ForeignKey("department.department_id", use_alter=True), index=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    name = Column(String, nullable=False)
    lower_case_name = Column(String, nullable=False)
    description = Column(String)
    order = Column(Integer, default=0)

    users: Mapped[List["UserModel"]] = relationship("UserModel", back_populates="job_title")


class JobTitleLevelModel(BaseModel):
    __tablename__ = "job_title_level"
    job_title_level_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    job_title_id = Column(String, ForeignKey("job_title.job_title_id", use_alter=True), index=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    name = Column(String, nullable=False)
    level = Column(Integer, nullable=False)
    lower_case_name = Column(String, nullable=False)
    users: Mapped[List["UserModel"]] = relationship("UserModel", back_populates="job_title_level")


event.listen(JobTitleLevelModel, "before_insert", generate_lower_case_name)


class RoleModel(BaseModel):
    __tablename__ = "role"

    role_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    name = Column(String, nullable=False, unique=True)
    lower_case_name = Column(String, nullable=False, unique=True)
    description = Column(String)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    users: Mapped[List["UserModel"]] = relationship(
        "UserModel", secondary=UserRoleModel.__tablename__, back_populates="roles"
    )
    permissions: Mapped[List["PermissionModel"]] = relationship(
        "PermissionModel", secondary=RolePermissionModel.__tablename__, back_populates="roles"
    )
    policies: Mapped[List["PolicyModel"]] = relationship(
        "PolicyModel", secondary="role_policies", back_populates="roles"
    )


event.listen(RoleModel, "before_insert", generate_lower_case_name)


class UserModel(BaseModel):
    __tablename__ = "user"

    user_id = Column(String, primary_key=True, default=generate_uuid, index=True, unique=True)
    employee_code = Column(String, Sequence("employee_code_seq"), primary_key=True, index=True, unique=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    first_name = Column(String, nullable=True)
    lark_user_id = Column(String, nullable=False)
    open_user_id = Column(String, nullable=False)
    middle_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    name = Column(String, nullable=False)
    unsigned_name = Column(String, nullable=True)
    primary_email = Column(String, nullable=False, unique=True)
    personal_email = Column(String, nullable=True)
    username = Column(String, nullable=True, unique=True)
    password = Column(String, nullable=True)
    gender = Column(
        Integer,
        nullable=False,
    )
    marital_status = Column(Integer, nullable=True)
    leader_user_id = Column(String, nullable=True)
    education_level = Column(String)
    employment_type_id = Column(Integer, ForeignKey("employment_type.employment_type_id", use_alter=True))
    thumb_avatar_link = Column(String)
    icon_avatar_link = Column(String)
    home_town = Column(String)
    date_of_birth = Column(DateTime, default=datetime.datetime.utcnow())
    current_address = Column(String)
    phone_number = Column(String)
    last_time_login = Column(DateTime, default=datetime.datetime.utcnow())
    salary_amount = Column(Float, default=0)
    start_salary = Column(DateTime)
    start_onboard_at = Column(DateTime)
    order = Column(Integer, default=1)
    contract_number = Column(String, ForeignKey("contract.contract_number", use_alter=True), unique=True)
    job_title_id = Column(String, ForeignKey("job_title.job_title_id", use_alter=True), index=True, nullable=False)
    job_title_level_id = Column(String, ForeignKey("job_title_level.job_title_level_id", use_alter=True), index=True)

    roles: Mapped[List["RoleModel"]] = relationship(
        "RoleModel", secondary=UserRoleModel.__tablename__, back_populates="users"
    )
    departments: Mapped[List["DepartmentModel"]] = relationship(
        "DepartmentModel", secondary=UserDepartmentModel.__tablename__, back_populates="users"
    )
    sub_departments: Mapped[List["SubDepartmentModel"]] = relationship(
        "SubDepartmentModel", secondary=UserSubDepartmentModel.__tablename__, back_populates="users"
    )
    job_title = relationship("JobTitleModel", back_populates="users")
    job_title_level = relationship("JobTitleLevelModel", back_populates="users")
    policies: Mapped[List["PolicyModel"]] = relationship(
        "PolicyModel", secondary="user_policies", back_populates="users"
    )

    def generate_employee_code(self, db):
        stmt = select(func.count(UserModel.user_id))
        result = db.execute(stmt).scalar()
        while True:
            new_id = result + 2 if result else 1
            employee_code = f"NV{new_id:05d}"
            if not db.query(UserModel).filter(UserModel.employee_code == employee_code).first():
                return employee_code
            result += 1


event.listen(UserModel, "before_insert", generate_unsigned_name)


class BranchModel(BaseModel):
    __tablename__ = "branch"

    branch_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    code = Column(String, index=True)
    name = Column(String, nullable=False)
    address = Column(String, nullable=False)
    phone = Column(String, nullable=False)
    owner_code = Column(String, nullable=False)


class ContactInfoModel(BaseModel):
    __tablename__ = "contact_info"
    contact_id = Column(Integer, primary_key=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    employee_code = Column(String, ForeignKey("user.employee_code", use_alter=True), index=True)
    full_name = Column(String, nullable=False)
    email = Column(String)
    phone = Column(String)
    address = Column(String)
    relationship_type = Column(String)


class ContractModel(BaseModel):
    __tablename__ = "contract"

    contract_id = Column(Integer, primary_key=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    contract_number = Column(String, index=True, unique=True)
    contract_file_scan_link = Column(String, default="")


class EmploymentTypeModel(BaseModel):
    __tablename__ = "employment_type"
    employment_type_id = Column(Integer, primary_key=True, unique=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    vi_name = Column(String)
    en_name = Column(String)
    description = Column(String)


class PermissionModel(BaseModel):
    __tablename__ = "permission"

    permission_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    description = Column(String)
    action = Column(String, nullable=False)
    scope = Column(String, nullable=False)

    roles: Mapped[List["RoleModel"]] = relationship(
        "RoleModel", secondary=RolePermissionModel.__tablename__, back_populates="permissions"
    )


class UserEventModel(BaseModel):
    __tablename__ = "user_event"

    user_event_id = Column(Integer, primary_key=True)
    employee_code = Column(String, ForeignKey("user.employee_code", use_alter=True))
    body_event = Column(JSON, default={})
    event_type = Column(String, nullable=False)


class UserHistoryModel(BaseModel):
    __tablename__ = "user_history"

    history_id = Column(Integer, primary_key=True)
    employee_code = Column(String, ForeignKey("user.employee_code", use_alter=True))
    before_data = Column(JSON, default={})
    after_data = Column(JSON, default={})


class UserIdentificationModel(BaseModel):
    __tablename__ = "user_identification"

    user_identification_id = Column(Integer, primary_key=True)
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    employee_code = Column(String, ForeignKey("user.employee_code", use_alter=True), index=True)
    type = Column(String, nullable=False)
    issue_place = Column(String, nullable=False)
    front_image = Column(String, nullable=False)
    back_image = Column(String, nullable=False)
    identification_number = Column(String, nullable=True)
    issue_date = Column(DateTime, default=datetime.datetime.utcnow())


class PolicyModel(BaseModel):
    __tablename__ = "policies"

    policy_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(String)
    policy_type = Column(String, default="IDENTITY")  # IDENTITY, RESOURCE, or PERMISSION_BOUNDARY
    company_id = Column(String, ForeignKey("company.company_id"), nullable=True)
    document = Column(JSON, nullable=False)  # JSON policy document
    created_by = Column(String, nullable=False)

    # Relationships
    users = relationship("UserModel", secondary="user_policies", back_populates="policies")
    roles = relationship("RoleModel", secondary="role_policies", back_populates="policies")


class UserPolicyModel(BaseModel):
    __tablename__ = "user_policies"

    user_policy_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    user_id = Column(String, ForeignKey("user.user_id", use_alter=True), index=True)
    policy_id = Column(String, ForeignKey("policies.policy_id", use_alter=True), index=True)


class RolePolicyModel(BaseModel):
    __tablename__ = "role_policies"

    role_policy_id = Column(String, primary_key=True, default=generate_uuid, index=True)
    role_id = Column(String, ForeignKey("role.role_id", use_alter=True), index=True)
    policy_id = Column(String, ForeignKey("policies.policy_id", use_alter=True), index=True)
