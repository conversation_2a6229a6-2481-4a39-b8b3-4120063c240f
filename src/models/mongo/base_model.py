#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: Current Date
"""
from typing import Optional

from pydantic import BaseModel, Field


class MongoBaseModel(BaseModel):
    """Base model for MongoDB collections"""

    id: Optional[str] = Field(None, alias="_id")

    class Meta:
        collection_name: str = None

    class Config:
        populate_by_name = True
        allow_population_by_field_name = True

    @classmethod
    def get_collection_name(cls) -> str:
        """Get the collection name for this model"""
        if not cls.Meta.collection_name:
            raise ValueError(f"Collection name not defined for {cls.__name__}")
        return cls.Meta.collection_name
