from bson import ObjectId

from src.common.common import <PERSON><PERSON><PERSON>, Competency<PERSON><PERSON>, EvaluatePeriod<PERSON>ey
from src.models.mongo import MongoBaseModel
from src.utils.time_helper import time_str_to_datetime


class EvaluatePeriodModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "evaluate_period"

    async def insert_evaluate_period(self, payload):
        document = await self.insert_document(payload)
        return str(document.inserted_id)

    async def update_evaluate_period(self, evaluate_period_id, payload):
        return await self.update_one_query({CompetencyKey.ID: ObjectId(evaluate_period_id)}, payload)

    async def get_evaluate_period_by_id(self, evaluate_period_id):
        return await self.find_one({EvaluatePeriodKey.ID: ObjectId(evaluate_period_id)})

    async def get_evaluate_periods(
        self,
        company_id,
        evaluate_period_ids,
        start_time,
        end_time,
        sort=None,
        order=None,
        department_ids=None,
        filter_type_default=None,
    ):
        query = {CommonKey.COMPANY_ID: company_id}
        if evaluate_period_ids:
            query.update({EvaluatePeriodKey.ID: {"$in": [ObjectId(id) for id in evaluate_period_ids]}})

        if start_time:
            start_datetime = time_str_to_datetime(start_time)
            if start_datetime:  # Kiểm tra kết quả hợp lệ
                query.update({EvaluatePeriodKey.START_TIME: {"$gte": start_datetime}})
        if end_time:
            end_datetime = time_str_to_datetime(end_time)
            if end_datetime:  # Kiểm tra kết quả hợp lệ
                query.update({EvaluatePeriodKey.END_TIME: {"$lte": end_datetime}})

        if filter_type_default:
            query.update({EvaluatePeriodKey.START_TIME: {"$lte": filter_type_default}})
        if department_ids:
            query.update(
                {
                    "$or": [
                        {"specific_department_ids": {"$in": department_ids}},
                        {"specific_department_ids": []},
                    ]
                }
            )

        results = await self.find_paginate(query, sort=sort, order=order)
        return list(results)

    async def get_evaluate_period_in_range_time(self, company_id, time_now, department_ids=None):
        query = {
            CommonKey.COMPANY_ID: company_id,
            EvaluatePeriodKey.START_TIME: {"$lte": time_now},
            EvaluatePeriodKey.END_TIME: {"$gte": time_now},
        }
        if department_ids:
            query.update(
                {
                    "$and": [
                        {"specific_department_ids": {"$in": department_ids}},
                        {"specific_department_ids": {"$exists": True}},
                    ]
                }
            )
        return await self.find_one(query)

    async def get_evaluate_period_by_start_time(self, company_id, start_time):
        return await self.find_one(
            {
                CommonKey.COMPANY_ID: company_id,
                EvaluatePeriodKey.START_TIME: start_time,
            }
        )
