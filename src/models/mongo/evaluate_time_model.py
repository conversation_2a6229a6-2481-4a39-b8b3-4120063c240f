from src.common.common import <PERSON><PERSON><PERSON>, EvaluateTimeKey
from src.models.mongo import MongoBaseModel


class EvaluateTimeModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "evaluate_time"

    async def get_evaluate_time_by_department_id(self, company_id, department_id, times):
        return await self.find_one(
            {
                EvaluateTimeKey.DEPARTMENT_ID: department_id,
                CommonKey.COMPANY_ID: company_id,
                EvaluateTimeKey.TIMES: times,
            }
        )

    async def get_evaluate_time_in_time_now(self, company_id, department_id, time_now, field_option={}):
        return await self.find_one(
            {
                CommonKey.COMPANY_ID: company_id,
                EvaluateTimeKey.DEPARTMENT_ID: department_id,
                EvaluateTimeKey.TIMES: {
                    "$elemMatch": {
                        EvaluateTimeKey.START_TIME: {"$lte": time_now},
                        EvaluateTimeKey.END_TIME: {"$gte": time_now},
                    }
                },
            },
            projection=field_option,
        )
