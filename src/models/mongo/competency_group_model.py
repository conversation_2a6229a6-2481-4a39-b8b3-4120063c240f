from bson import ObjectId

from src.common.common import CompetencyGroupKey, CommonKey
from src.models.mongo import MongoBaseModel
from src.utils.time_helper import get_time_now


class CompetencyGroupModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "competency_group"

    async def get_one_competency_group_by_fields(self):
        pass

    async def insert_competency_group(self, payload):
        competency_group_insert = await self.insert(payload)
        return competency_group_insert.inserted_id

    async def get_lst_competency_group_in_system(self, search, sort, order, page, per_page):
        query: dict = {CompetencyGroupKey.LOWER_CASE_NAME: {"$regex": search}}
        results = await self.find_paginate(
            query,
            page=page,
            per_page=per_page,
            sort=sort,
            order=order,
            projection={CompetencyGroupKey.LOWER_CASE_NAME: 0},
        )
        return list(results), {
            CommonKey.PAGE: page,
            CommonKey.PER_PAGE: per_page,
            CommonKey.TOTAL_COUNT: await self.count(query),
            CommonKey.TOTAL_PAGE: (await self.count(query)) // per_page + 1,
        }

    async def get_one_competency_group_by_name(self, name):
        return await self.find_one({CompetencyGroupKey.LOWER_CASE_NAME: name})

    async def get_one_competency_group_by_id(self, competency_group_id):

        return await self.find_one({CompetencyGroupKey.ID: ObjectId(competency_group_id)})

    async def get_competency_default_by_id(self, competency_group_id):
        return await self.find_one(
            {CompetencyGroupKey.ID: ObjectId(competency_group_id), CompetencyGroupKey.IS_DEFAULT: True}
        )

    async def update_one_competency_group(self, competency_group_id, data_update):
        name = data_update.get(CompetencyGroupKey.NAME)
        if name:
            data_update.update({CompetencyGroupKey.LOWER_CASE_NAME: name.lower()})
        return await self.update_dictionary(competency_group_id, data_update)

    async def delete_one_competency_group(self, competency_group_id):
        return await self.delete_one({"_id": ObjectId(competency_group_id)})

    async def get_competency_group_default_in_system(self):
        lst_competency_group = await self.find({CompetencyGroupKey.IS_DEFAULT: True})
        return list(lst_competency_group)

    async def init_competency_default(self, email):
        data_init: list = [
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực cốt lõi",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực cốt lõi",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 1,
            },
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực chuyên môn",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực chuyên môn",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 2,
            },
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực bổ trợ",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực bổ trợ",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 3,
            },
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực quản lý",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực quản lý",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 4,
            },
        ]
        await self.insert_many(data_init)
        return data_init

    
    async def get_default_competency_group_by_ids(self, list_competency_group_ids):
        return await self.find({CompetencyGroupKey.ID: {"$in": list_competency_group_ids}})