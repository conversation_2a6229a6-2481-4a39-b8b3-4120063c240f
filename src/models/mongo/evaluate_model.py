import datetime

from bson import ObjectId

from src.common.choices import (
    EvaluateStatusChoice,
    EvaluateStatusFilterChoice,
    OperatorChoice,
    UserEvaluateTypeChoice,
)
from src.common.common import Common<PERSON><PERSON>, Competency<PERSON><PERSON>, <PERSON>luate<PERSON>ey
from src.common.constants import ConstantTypeOfReview
from src.models.mongo import MongoBaseModel
from src.utils.time_helper import get_time_now


class EvaluateModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "evaluate"

    async def insert_evaluate(self, payload):
        document = await self.insert_document(payload)
        return document.inserted_id

    async def update_evaluate(self, evaluate_id, payload):
        return await self.update_one_query({CompetencyKey.ID: ObjectId(evaluate_id)}, payload)

    async def get_evaluate_current_for_user(self, evaluate_id):
        time_now = get_time_now()
        return await self.find_one(
            {
                EvaluateKey.ID: ObjectId(evaluate_id),
                EvaluateKey.END_TIME: {"$gte": time_now},
                EvaluateKey.START_TIME: {"$lte": time_now},
            }
        )

    async def get_evaluate_by_department_id(self, company_id, department_id, time_now):
        query = {
            CommonKey.COMPANY_ID: company_id,
            EvaluateKey.DEPARTMENT_ID: department_id,
            EvaluateKey.START_TIME: {"$lte": time_now},
            EvaluateKey.END_TIME: {"$gte": time_now},
            EvaluateKey.STATUS_FILTER: {
                "$in": [
                    EvaluateStatusFilterChoice.WAITING_USER.value,
                    EvaluateStatusFilterChoice.WAITING_LEADER.value,
                ]
            },
        }
        result = await self.find(query)
        result = result.sort({"created_time": -1}).limit(1)

        result = [*result]
        return result[0] if result else {}

    async def get_evaluates_by_competency_framework(self, company_id, competency_framework_id):
        time_now = get_time_now()

        data = await self.find(
            {
                CommonKey.COMPANY_ID: company_id,
                EvaluateKey.COMPETENCY_FRAMEWORK_ID: competency_framework_id,
                EvaluateKey.START_TIME: {"$lte": time_now},
                EvaluateKey.END_TIME: {"$gte": time_now},
            }
        )
        return list(data)

    async def get_evaluates(
        self,
        company_id,
        filters=[],
        sort=None,
        order=None,
        page=None,
        per_page=None,
        user_ids_for_search=[],
        user_ids_for_abac=[],
        sort_option=[],
    ):

        query = {
            CommonKey.COMPANY_ID: company_id,
        }

        if not sort_option and sort:
            sort_option = [(sort, order)]

        user_id_query = []

        if user_ids_for_search and not user_ids_for_abac:
            user_id_query = user_ids_for_search

        elif not user_ids_for_search and user_ids_for_abac:
            user_id_query = user_ids_for_abac

        elif user_ids_for_search and user_ids_for_abac:
            user_id_query = list(set(user_ids_for_abac) & set(user_ids_for_search))
        if user_id_query:
            query.update({EvaluateKey.USER_ID: {"$in": user_id_query}})

        status_filters = []

        for evaluate_filter in filters.evaluate_filters:
            # Covert value
            if evaluate_filter.field == EvaluateKey.EVALUATE_PERIOD_ID:
                evaluate_filter.value = [ObjectId(str_id) for str_id in evaluate_filter.value]

            if evaluate_filter.field == EvaluateKey.STATUS:
                status_mapping = {
                    (
                        UserEvaluateTypeChoice.OWNER.value,
                        EvaluateStatusChoice.WAITING.value,
                    ): EvaluateStatusFilterChoice.WAITING_USER.value,
                    (
                        UserEvaluateTypeChoice.LEADER.value,
                        EvaluateStatusChoice.WAITING.value,
                    ): EvaluateStatusFilterChoice.WAITING_LEADER.value,
                }

                for value in evaluate_filter.value:
                    user_type = value.get(EvaluateKey.USER_TYPE)
                    status = value.get(EvaluateKey.STATUS)
                    if user_type and status == EvaluateStatusChoice.WAITING.value:
                        status_filters.append(status_mapping.get((user_type, status)))
                    else:
                        if status == EvaluateStatusChoice.COMPLETED.value:
                            status_filters.append(EvaluateStatusFilterChoice.COMPLETED.value)
            elif evaluate_filter.field == EvaluateKey.LEADER_ID:
                query.update(
                    {
                        "time_eval_of_users": {
                            "$elemMatch": {
                                "user_type": UserEvaluateTypeChoice.LEADER.value,
                                "user_id": {"$in": evaluate_filter.value},
                            }
                        }
                    }
                )
            elif evaluate_filter.field == EvaluateKey.TYPE_OF_REVIEW:
                if evaluate_filter.value == [ConstantTypeOfReview.SUDDENLY]:
                    query.update({EvaluateKey.TYPE_OF_REVIEW: ConstantTypeOfReview.SUDDENLY})
                elif evaluate_filter.value == [ConstantTypeOfReview.PERIODIC]:
                    query.update({})
            elif evaluate_filter.field == "time":
                month_datetime_query = evaluate_filter.value[0]
                start_time = "01/{}".format(month_datetime_query)
                # Convert start time theo định dạng dd/mm/yyyy
                start_time = datetime.datetime.strptime(start_time, "%d/%m/%Y").replace(hour=0, minute=0, second=0)
                end_time = (start_time + datetime.timedelta(days=30)).replace(hour=23, minute=59, second=59)

                query.update(
                    {EvaluateKey.START_TIME: {"$gte": start_time, "$lte": end_time}},
                )
            else:
                if evaluate_filter.operator == OperatorChoice.OP_IS_IN.value:
                    query.update({evaluate_filter.field: {"$in": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_EQ:
                    query.update({evaluate_filter.field: evaluate_filter.value[0]})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_RE.value:
                    query.update({evaluate_filter.field: {"$regex": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_LT.value:
                    query.update({evaluate_filter.field: {"$lt": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_GE.value:
                    query.update({evaluate_filter.field: {"$gte": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_PE.value:
                    query.update(
                        {evaluate_filter.key: {"$gte": evaluate_filter.value[0], "$lte": evaluate_filter.value[1]}}
                    )
            if status_filters:
                query.update({EvaluateKey.STATUS_FILTER: {"$in": status_filters}})

        return [*await self.find_paginate_version_new(query, sort_option=sort_option, page=page, per_page=per_page)]

    async def get_evaluates_managed_team(
        self,
        company_id,
        filters=[],
        sort=None,
        order=None,
        page=None,
        per_page=None,
        user_ids_for_search=[],
        user_ids_for_abac=[],
    ):
        query = {
            CommonKey.COMPANY_ID: company_id,
        }

        user_id_query = []

        if user_ids_for_search and not user_ids_for_abac:
            user_id_query = user_ids_for_search

        elif not user_ids_for_search and user_ids_for_abac:
            user_id_query = user_ids_for_abac

        elif user_ids_for_search and user_ids_for_abac:
            user_id_query = list(set(user_ids_for_abac) & set(user_ids_for_search))
        if user_id_query:
            query.update({EvaluateKey.USER_ID: {"$in": user_id_query}})

        for evaluate_filter in filters.evaluate_filters:
            # Covert value
            if evaluate_filter.field == EvaluateKey.EVALUATE_PERIOD_ID:
                evaluate_filter.value = [ObjectId(str_id) for str_id in evaluate_filter.value]

            if evaluate_filter.field == EvaluateKey.STATUS:
                for value in evaluate_filter.value:
                    user_type = value.get(EvaluateKey.USER_TYPE)
                    status = value.get(EvaluateKey.STATUS)

                    query.update({EvaluateKey.STATUS: status})
                    if user_type:
                        query.update(
                            {
                                EvaluateKey.TIME_EVAL_OF_USERS: {
                                    "$elemMatch": {
                                        EvaluateKey.USER_TYPE: user_type,
                                        EvaluateKey.SUBMIT_TIME: {"$exists": False},
                                    }
                                }
                            }
                        )
            elif evaluate_filter.field == EvaluateKey.LEADER_ID:
                query.update({"type_of_review.user_id": {"$in": evaluate_filter.value}})
            elif evaluate_filter.field == EvaluateKey.TYPE_OF_REVIEW:
                query.update({})
            else:
                if evaluate_filter.operator == OperatorChoice.OP_IS_IN.value:
                    query.update({evaluate_filter.field: {"$in": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_EQ:
                    query.update({evaluate_filter.field: evaluate_filter.value[0]})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_RE.value:
                    query.update({evaluate_filter.field: {"$regex": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_LT.value:
                    query.update({evaluate_filter.field: {"$lt": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_GE.value:
                    query.update({evaluate_filter.field: {"$gte": evaluate_filter.value}})
                elif evaluate_filter.operator == OperatorChoice.OP_IS_PE.value:
                    query.update(
                        {evaluate_filter.key: {"$gte": evaluate_filter.value[0], "$lte": evaluate_filter.value[1]}}
                    )
        skip_records = (page - 1) * per_page
        limit_records = per_page

        # Xây dựng sort_list bên ngoài pipeline để MongoDB có thể sử dụng đúng thứ tự ưu tiên
        sort_list = []

        # Chuẩn bị các option sắp xếp
        sort_options = {}

        if sort and order:
            if sort == "number_evaluate_of_complete":
                sort_options["number_evaluate_of_complete"] = order
            elif sort == "number_evaluate_of_waiting":
                sort_options["number_evaluate_of_waiting"] = order
            elif sort == "department_id":
                sort_options["department_id"] = order
            elif sort == "updated_time":
                sort_options["department_id"] = -1
                sort_options["updated_time"] = order
            elif sort == "job_title":
                sort_options["department_id"] = order

        # Đảm bảo department_id luôn được đặt lên đầu
        # Thêm department_id vào đầu danh sách sort_list nếu đã được chỉ định
        if "department_id" in sort_options:
            sort_list.append({"department_id": sort_options["department_id"]})
            del sort_options["department_id"]
        elif "number_evaluate_of_complete" not in sort_options and "number_evaluate_of_waiting" not in sort_options:
            # Nếu không được chỉ định, mặc định là sắp xếp giảm dần
            sort_list.append({"department_id": -1})

        # Thêm các tùy chọn sắp xếp khác
        for key, value in sort_options.items():
            sort_list.append({key: value})

        # Luôn thêm _id vào cuối để đảm bảo kết quả ổn định
        sort_list.append({"_id": 1})
        sort_dict = {}
        for sort in sort_list:
            sort_dict.update(sort)

        pipeline = [
            # Lọc dữ liệu theo các điều kiện ban đầu
            {"$match": query},
            # Nhóm theo user_id và status để đếm số lượng cho mỗi trạng thái
            {
                "$group": {
                    "_id": {
                        "user_id": "$user_id",
                        "status": "$status",
                        "department_id": "$department_id",
                        "updated_time": "$updated_time",
                    },
                    "count": {"$sum": 1},
                }
            },
            # Nhóm theo user_id để tổng hợp các status
            {
                "$group": {
                    "_id": "$_id.user_id",
                    "department_id": {"$first": "$_id.department_id"},
                    "statuses": {"$push": {"status": "$_id.status", "count": "$count"}},
                    "total_records_for_user": {"$sum": "$count"},
                    "updated_time": {"$first": "$_id.updated_time"},
                }
            },
            # Tính trước số lượng bản ghi theo từng trạng thái để dễ sắp xếp
            {
                "$addFields": {
                    "number_evaluate_of_complete": {
                        "$reduce": {
                            "input": "$statuses",
                            "initialValue": 0,
                            "in": {
                                "$cond": [
                                    {"$eq": ["$$this.status", EvaluateStatusChoice.COMPLETED.value]},
                                    {"$add": ["$$value", "$$this.count"]},
                                    "$$value",
                                ]
                            },
                        }
                    },
                    "number_evaluate_of_waiting": {
                        "$reduce": {
                            "input": "$statuses",
                            "initialValue": 0,
                            "in": {
                                "$cond": [
                                    {"$eq": ["$$this.status", EvaluateStatusChoice.WAITING.value]},
                                    {"$add": ["$$value", "$$this.count"]},
                                    "$$value",
                                ]
                            },
                        }
                    },
                }
            },
            # Sắp xếp theo các tiêu chí đã được xây dựng trong sort_list
            {"$sort": sort_dict},
            # Tạo facet để phân trang
            {
                "$facet": {
                    "metadata": [{"$count": "total_users"}],
                    "data": [
                        # Sắp xếp lại trong data để đảm bảo thứ tự đúng
                        {"$sort": sort_dict},
                        {"$skip": skip_records},
                        {"$limit": limit_records},
                    ],
                }
            },
            # Format kết quả trả về
            {
                "$project": {
                    "data": {
                        "$map": {
                            "input": "$data",
                            "as": "item",
                            "in": {
                                "_id": "$$item._id",
                                "department_id": "$$item.department_id",
                                "statuses": "$$item.statuses",
                                "total_records_for_user": "$$item.total_records_for_user",
                                "updated_time": "$$item.updated_time",
                                "number_evaluate_of_complete": "$$item.number_evaluate_of_complete",
                                "number_evaluate_of_waiting": "$$item.number_evaluate_of_waiting",
                            },
                        }
                    },
                    "total_users": {"$ifNull": [{"$arrayElemAt": ["$metadata.total_users", 0]}, 0]},
                    "page": page,
                    "per_page": per_page,
                    "total_pages": {
                        "$ceil": {
                            "$divide": [{"$ifNull": [{"$arrayElemAt": ["$metadata.total_users", 0]}, 0]}, per_page]
                        }
                    },
                }
            },
        ]

        try:
            result = await self.aggregate(pipeline)
            result = [*result]
            if result:
                return result[0]  # Facet luôn trả về 1 document kết quả
            return {"data": [], "total_users": 0, "page": page, "per_page": per_page, "total_pages": 0}
        except Exception:
            return {"data": [], "total_users": 0, "page": page, "per_page": per_page, "total_pages": 0}

    async def get_evaluate_current_by_user_id(self, company_id, user_id):
        time_now = get_time_now()

        lst_evaluate = await self.find(
            {
                EvaluateKey.USER_ID: user_id,
                CommonKey.COMPANY_ID: company_id,
                EvaluateKey.END_TIME: {"$gte": time_now},
                EvaluateKey.START_TIME: {"$lte": time_now},
            },
        )

        lst_evaluate = lst_evaluate.sort({"created_time": -1})

        if lst_evaluate:
            lst_evaluate = [*lst_evaluate]
            return lst_evaluate[0] if lst_evaluate else None

        return None

    async def get_evaluate_by_id(self, evaluate_id, projection={}):
        return await self.find_one({EvaluateKey.ID: ObjectId(evaluate_id)}, projection=projection)

    async def get_evaluates_by_status(self, status):
        results = await self.find({EvaluateKey.STATUS: status})
        return list(results)
