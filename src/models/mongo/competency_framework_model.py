from bson import ObjectId

from src.common.choices import (
    CompetencyFrameworkStatusChoice,
    OperatorChoice,
    RoleChoice,
    TypeCompetencyFrameworkChoice,
)
from src.common.common import CommonKey, CompetencyFrameworkKey
from src.models.mongo import MongoBaseModel
from src.utils.time_helper import get_time_now, time_str_to_datetime


class CompetencyFrameworkModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "competency_framework"

    async def insert_competency_framework(self, payload):
        payload.update({CompetencyFrameworkKey.LOWER_CASE_NAME: payload["name"].lower()})

        document = await self.insert_document(payload)
        return document.inserted_id

    async def get_one_competency_framework_by_id(self, competency_framework_id):
        return await self.find_one({CompetencyFrameworkKey.ID: ObjectId(competency_framework_id)})

    async def update_competency_framework(self, competency_framework_id, payload):
        if payload.get("name"):
            payload.update({CompetencyFrameworkKey.LOWER_CASE_NAME: payload["name"].lower()})

        return await self.update_dictionary(ObjectId(competency_framework_id), payload)

    # async def delete_competency_framework(self, competency_framework_id):
    #     return await self.delete_one({CompetencyFrameworkKey.ID: ObjectId(competency_framework_id)})
    async def delete_competency_framework(self, competency_framework_id):
        return await self.update_by_set(
            {CompetencyFrameworkKey.ID: ObjectId(competency_framework_id)},
            {
                CompetencyFrameworkKey.TYPE: TypeCompetencyFrameworkChoice.DRAFT_DELETE.value,
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT_DELETE.value,
            },
        )

    async def get_lst_competency_framework_in_system(
        self,
        company_id,
        search="",
        filters=[],
        sort="updated_time",
        order=-1,
        page=1,
        per_page=20,
        roles=[],
        department_id="",
        query={},
    ):
        query = {
            CommonKey.COMPANY_ID: company_id,
            **query,
        }

        is_only_role_member = False
        if roles[0] in [RoleChoice.LEADER.value, RoleChoice.USER.value] and RoleChoice.ADMIN.value not in roles:
            query.update({CompetencyFrameworkKey.DEPARTMENT_ID: department_id})

        if (
            RoleChoice.USER.value in roles
            and RoleChoice.ADMIN.value not in roles
            and RoleChoice.LEADER.value not in roles
        ):
            is_only_role_member = True

        for competency_framework_filter in filters:
            if competency_framework_filter.field == CompetencyFrameworkKey.STATUS:
                if competency_framework_filter.value[0] == CompetencyFrameworkStatusChoice.DRAFT.value:
                    query.update({CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT.value})

            if competency_framework_filter.field in [
                CompetencyFrameworkKey.START_TIME,
                CompetencyFrameworkKey.END_TIME,
            ]:
                if type(competency_framework_filter.value[0]) == str:
                    competency_framework_filter.value = [
                        time_str_to_datetime(v) for v in competency_framework_filter.value
                    ]

            if competency_framework_filter.operator == OperatorChoice.OP_IS_IN.value:
                query[competency_framework_filter.field] = {"$in": competency_framework_filter.value}
            elif competency_framework_filter.operator == OperatorChoice.OP_IS_EQ.value:
                if competency_framework_filter.field in [
                    CompetencyFrameworkKey.START_TIME,
                    CompetencyFrameworkKey.END_TIME,
                ]:
                    start = competency_framework_filter.value[0].replace(hour=00, minute=00, second=00, microsecond=0)
                    end = competency_framework_filter.value[0].replace(hour=23, minute=59, second=59, microsecond=0)

                    query[competency_framework_filter.field] = {"$gte": start, "$lte": end}
                else:
                    query[competency_framework_filter.field] = competency_framework_filter.value[0]

                if (
                    competency_framework_filter.field == CompetencyFrameworkKey.STATUS
                    and competency_framework_filter.value[0]
                    in [
                        CompetencyFrameworkStatusChoice.DRAFT.value,
                        CompetencyFrameworkStatusChoice.INACTIVE.value,
                    ]
                ):
                    if is_only_role_member:
                        query.update(
                            {CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ONLY_ROLE_MEMBER.value}
                        )
                    else:
                        query.update({CompetencyFrameworkKey.TYPE: {"$ne": TypeCompetencyFrameworkChoice.DRAFT.value}})

            elif competency_framework_filter.operator == OperatorChoice.OP_IS_RE.value:
                query[competency_framework_filter.field] = {"$regex": competency_framework_filter.value}
            elif competency_framework_filter.operator == OperatorChoice.OP_IS_LT.value:
                query[competency_framework_filter.field] = {"$lt": competency_framework_filter.value[0]}
            elif competency_framework_filter.operator == OperatorChoice.OP_IS_GE.value:
                query[competency_framework_filter.field] = {"$gt": competency_framework_filter.value[0]}
            elif competency_framework_filter.operator == OperatorChoice.OP_IS_PE.value:
                query[competency_framework_filter.field] = {
                    "$gte": competency_framework_filter.value[0],
                    "$lte": competency_framework_filter.value[1],
                }

        if search:
            query.update({CompetencyFrameworkKey.NAME: {"$regex": search}})

        if "status" in query and is_only_role_member:
            if query["status"] in [
                CompetencyFrameworkStatusChoice.DRAFT.value,
                CompetencyFrameworkStatusChoice.INACTIVE.value,
            ]:
                query.update({CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ONLY_ROLE_MEMBER.value})

        results = await self.find_paginate(
            query,
            page=page,
            per_page=per_page,
            sort=sort,
            order=order,
            projection={CompetencyFrameworkKey.LOWER_CASE_NAME: 0},
        )

        total_count = await self.count(query)

        return list(results), {
            CommonKey.PAGE: page,
            CommonKey.PER_PAGE: per_page,
            CommonKey.TOTAL_COUNT: total_count,
            CommonKey.TOTAL_PAGE: total_count // per_page + 1,
        }

    async def get_one_competency_framework_by_name(self, name):
        return await self.find_one({CompetencyFrameworkKey.LOWER_CASE_NAME: name.lower()})

    async def count_competency_frameworks(self, filter_options):
        return await self.count_by_query(filter_options)

    async def get_competency_frameworks_expired(self):
        results = await self.find(
            {
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
                CompetencyFrameworkKey.END_TIME: {"$lt": get_time_now()},
            }
        )

        return list(results)

    async def get_competency_frameworks_of_exist_job_title(self, company_id, job_title_ids, lst_id_ignore=None):
        filter_option = {
            CommonKey.COMPANY_ID: company_id,
            CompetencyFrameworkKey.JOB_TITLE_IDS: {"$elemMatch": {"$in": job_title_ids}},
            CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
        }
        if lst_id_ignore:
            filter_option.update({"_id": {"$nin": [ObjectId(id_ignore) for id_ignore in lst_id_ignore]}})
        results = await self.find(filter_option)

        return list(results)

    async def get_competency_frameworks_by_job_title_ids_diff_cf_current(
        self, job_title_ids, start_time, end_time, cf_current_id
    ):
        """_summary_

        Args:
            job_title_ids (_type_): _description_
            start_time (_type_): _description_
            end_time (_type_): _description_
            cf_current_id (_type_): là id khung năng lực hiện tại
        """
        filter_option = {
            CompetencyFrameworkKey.JOB_TITLE_IDS: {"$in": job_title_ids},
            CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
        }
        if cf_current_id:
            filter_option.update({"_id": {"$ne": ObjectId(cf_current_id)}})
        return await self.find(
            filter_option,
            {CompetencyFrameworkKey.START_TIME: 1, CompetencyFrameworkKey.END_TIME: 1, CompetencyFrameworkKey.NAME: 1},
        )
