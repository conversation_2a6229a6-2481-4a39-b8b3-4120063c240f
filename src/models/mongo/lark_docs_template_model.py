from bson import ObjectId

from src.models.mongo import MongoBaseModel


class LarkDocsTemplateModel(MongoBaseModel):

    collection_name = "lark_docs_template"

    async def update_template(self, template_id, template_data):
        if "_id" in template_data:
            del template_data["_id"]
        return await self.update_one_query(query={"template_id": template_id}, data=template_data)

    async def upsert_template(self, template_data):
        if "_id" not in template_data:
            template_data["_id"] = ObjectId()
            template_data["template_id"] = str(template_data["_id"])

        current_template = await self.get_template(type=template_data["type"])
        if bool(current_template):
            await self.update_template(template_id=template_data["template_id"], template_data=template_data)
        else:
            await self.insert(template_data)

        return template_data

    async def get_template(self, type):
        data = await self.find_one({"type": type})
        return data

    async def get_list_template(self):
        list_data = list(await self.find({}))
        return list_data

    async def get_template_by_type(self, type, key):
        data = await self.find_one(
            {
                "type": type,
                "key": key,
            },
        )
        return data
