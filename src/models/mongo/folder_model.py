from src.common.common import CommonKey
from src.models.mongo import MongoBaseModel
from src.utils.time_helper import get_time_now


class FolderModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "folder"

    async def create_folder(self, payload):
        created_time = updated_time = get_time_now()
        payload.update(
            {
                CommonKey.CREATED_TIME: created_time,
                CommonKey.UPDATED_TIME: updated_time,
                CommonKey.CREATED_BY: "admin",
                CommonKey.UPDATED_BY: "admin",
            }
        )

        document = await self.insert_document(payload)
        return document.inserted_id

    async def get_folder_by_name(self, name_lower):
        return await self.find_one({"lower_case_name": name_lower})
