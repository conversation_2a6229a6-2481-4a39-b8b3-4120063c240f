from src.models.mongo import MongoBaseModel


class CompanyConfigModel(MongoBaseModel):

    def __init__(self):
        super().__init__()
        self.collection_name = "company_config"

    async def get_config_by_company_id(self, company_id, fields=[]):
        projection = {}
        if fields:
            for field in fields:
                projection[field] = 1

        return await self.find_one({"company_id": company_id}, projection=projection)
