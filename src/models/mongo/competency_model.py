from bson import ObjectId

from src.common.common import CompetencyKey
from src.models.mongo import MongoBaseModel


class CompetencyModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "competency"

    async def get_all_competency_in_system(self, search, sort, order, page=1, per_page=20):
        query = {}

        if search:
            query = {CompetencyKey.LOWER_CASE_NAME: {"$regex": search}}

        data = await self.find_paginate(
            query,
            page=page,
            per_page=per_page,
            sort=sort,
            order=order,
        )

        return list(data), {
            "page": page,
            "per_page": per_page,
            "total_count": await self.count(query),
            "total_page": (await self.count(query)) // per_page + 1,
        }

    async def get_one_competency_by_name(self, name):
        return await self.find_one({CompetencyKey.LOWER_CASE_NAME: name})

    async def get_one_competency_by_id(self, competency_id):
        return await self.find_one({CompetencyKey.ID: ObjectId(competency_id)})

    async def insert_competency(self, competency):
        insert_one_result = await self.insert(competency)
        return insert_one_result.inserted_id

    async def update_competency(self, competency_id, payload):
        return await self.update_one_query({CompetencyKey.ID: ObjectId(competency_id)}, payload)

    async def delete_competencies(self, competency_ids):
        competency_ids = [ObjectId(competency_id) for competency_id in competency_ids]
        return await self.delete_many({CompetencyKey.ID: {"$in": competency_ids}})

    async def get_competencies_by_group_id(self, group_id):
        return list(
            await self.find(
                {CompetencyKey.COMPETENCY_GROUP_ID: group_id},
                obj_field_select={CompetencyKey.LOWER_CASE_NAME: 0, CompetencyKey.COMPETENCY_GROUP_ID: 0},
            )
        )

    async def update_competencies(self, competency_ids, payload):
        competency_ids = [ObjectId(competency_id) for competency_id in competency_ids]
        return await self.update_many({CompetencyKey.ID: {"$in": competency_ids}}, payload)

    async def get_competencies_by_department_id(self, company_id, department_id):
        return list(
            await self.find(
                {
                    "company_id": company_id,
                    "default_apply_department_ids": {"$in": [department_id]},
                    "is_default_apply": True,
                }
            )
        )
