import json

from mobio.libs.Singleton import Singleton

from configs import ApplicationConfig


@Singleton
class LangConfig:

    def lang_map(self, lang="vi"):
        return self._lang_json(lang)

    def _lang_json(self, lang):
        path = ApplicationConfig.LADDER_HOME + "/resources" + "/lang" + "/message_" + lang + ".json"
        try:
            # print('lang(%s): %s' % (lang, path))
            with open(path) as data_file:
                data = json.loads(data_file.read())

            return data
        except Exception as ex:
            print(ex)
            return None
