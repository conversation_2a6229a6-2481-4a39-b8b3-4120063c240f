class COMMON:
    DATE_TIME_FORMAT = "%Y-%m-%dT%TZ"
    DATE_TIME_FORMAT_PARSE = "%Y-%m-%dT%H:%M:%SZ"
    EXCEL = ".xlsx"
    DATE_TIME_FORMAT_ZONE = "%Y-%m-%dT%H:%M:%S.%fZ"
    DATE_TIME_FORMAT_EXTERNAL = "%Y-%m-%d %H:%M:%S.%f"
    DEFAULT_YEAR = 2104


class CommonKey:
    ID = "_id"
    EXP = "exp"
    TOKEN = "token"
    CREATED_BY = "created_by"
    CREATED_TIME = "created_time"
    UPDATED_BY = "updated_by"
    UPDATED_TIME = "updated_time"
    TYPE = "type"
    PROPERTIES = "properties"
    REQUIRED = "required"
    MIN_LENGTH = "minLength"
    MAX_LENGTH = "maxLength"
    MESSAGE = "message"
    DATA = "data"
    CODE = "code"
    ADDITIONAL_PROPERTIES = "additionalProperties"
    LANG = "lang"
    FIELD = "field"
    VALUE = "value"
    OPERATOR = "operator"
    COMPANY_ID = "company_id"
    PAGE = "page"
    PER_PAGE = "per_page"
    TOTAL_COUNT = "total_count"
    TOTAL_PAGE = "total_page"
    PAGING = "paging"
    TOTAL = "total"
    ORDER = "order"
    DELETED_COUNT = "deleted_count"
    UPDATED_COUNT = "updated_count"


class UserKey:
    USER_ID = "user_id"
    EMAIL = "email"
    COMPANY_ID = "company_id"
    IS_ADMIN = "is_admin"
    IS_ACTIVATED = "is_activated"
    JOB_TITLE_ID = "job_title_id"
    STATUS = "status"
    LARK_USER_ID = "lark_user_id"
    FIRST_NAME = "first_name"
    MIDDLE_NAME = "middle_name"
    LAST_NAME = "last_name"
    NAME = "name"
    PRIMARY_EMAIL = "primary_email"
    PERSONAL_EMAIL = "personal_email"
    USERNAME = "username"
    EMPLOYEE_CODE = "employee_code"
    PASSWORD = "password"
    GENDER = "gender"
    MARITAL_STATUS = "marital_status"
    EDUCATION_LEVEL = "education_level"
    EMPLOYMENT_TYPE_ID = "employment_type_id"
    THUMB_AVATAR_LINK = "thumb_avatar_link"
    ICON_AVATAR_LINK = "icon_avatar_link"
    HOME_TOWN = "home_town"
    DATE_OF_BIRTH = "date_of_birth"
    CURRENT_ADDRESS = "current_address"
    PHONE_NUMBER = "phone_number"
    LAST_TIME_LOGIN = "last_time_login"
    SALARY_AMOUNT = "salary_amount"
    START_SALARY = "start_salary"
    START_ONBOARD_AT = "start_onboard_at"
    CONTRACT_NUMBER = "contract_number"
    DEPARTMENTS = "departments"
    LEADER_USER_ID = "leader_user_id"
    ROLES = "roles"
    PERMISSIONS = "permissions"
    JOB_TITLE_LEVEL_ID = "job_title_level_id"
    OPEN_USER_ID = "open_user_id"
    # CONTRACT_START_DATE = "contract_start_date"
    # CONTRACT_END_DATE = "contract_end_date"
    # CONTRACT_STATUS = "contract_status"


class RoleKey:
    NAME = "name"
    DESCRIPTION = "description"
    ACTION = "action"
    SCOPE = "scope"


class JobTitleKey:
    JOB_TITLE_ID = "job_title_id"
    JOB_TITLE_IDS = "job_title_ids"
    NAME = "name"
    DESCRIPTION = "description"
    ORDER = "order"
    LOWER_CASE_NAME = "lower_case_name"
    DEPARTMENT_ID = "department_id"
    LARK_DEPARTMENT_IDS = "lark_department_ids"
    JOB_TITLE_LEVELS = "job_title_levels"


class JobTitleLevelKey:
    JOB_TITLE_LEVEL_ID = "job_title_level_id"
    NAME = "name"
    DESCRIPTION = "description"
    LOWER_CASE_NAME = "lower_case_name"
    JOB_TITLE_ID = "job_title_id"
    LEVEL = "level"
    COLOR = "color"
    STATUS = "status"
    JOB_TITLE_LEVEL_NAME = "job_title_level_name"


class CompetencyKey:
    ID = "_id"
    NAME = "name"
    DESCRIPTION = "description"
    LOWER_CASE_NAME = "lower_case_name"
    COMPETENCY_GROUP_ID = "competency_group_id"
    BEHAVIOR_EXPRESSIONS = "behavior_expressions"
    JOB_TITLE_NAME = "job_title_name"
    JOB_TITLE_LEVEL = "job_title_level"
    IS_DEFAULT = "is_default"


class CompetencyGroupKey:
    ID = "_id"
    NAME = "name"
    DESCRIPTION = "description"
    LOWER_CASE_NAME = "lower_case_name"
    IS_DEFAULT = "is_default"
    LST_COMPETENCY = "lst_competency"
    ORDER = "order"


class DepartmentKey:
    DEPARTMENT_ID = "department_id"
    NAME = "name"
    LOWER_CASE_NAME = "lower_case_name"
    STATUS = "status"
    LARK_DEPARTMENT_ID = "lark_department_id"
    LARK_OPEN_DEPARTMENT_ID = "open_department_id"


class CompetencyFrameworkKey:
    ID = "_id"
    NAME = "name"
    LOWER_CASE_NAME = "lower_case_name"
    STATUS = "status"
    DEPARTMENT_ID = "department_id"
    DEPARTMENT_NAME = "department_name"
    SYNC_STATUS = "sync_status"
    DESCRIPTION = "description"
    COMPETENCY_GROUPS = "competency_groups"
    COMPETENCY_GROUP_ID = "competency_group_id"
    LST_COMPETENCY = "lst_competency"
    COMPETENCY_ID = "competency_id"
    WEIGHT = "weight"
    BEHAVIOR_EXPRESSIONS = "behavior_expressions"
    JOB_TITLES = "job_titles"
    JOB_TITLE_IDS = "job_title_ids"
    JOB_TITLE_LEVELS = "job_title_levels"
    BEHAVIOR_EXPRESSION_ID = "behavior_expression_id"
    JOB_TITLE_LEVEL_ID = "job_title_level_id"
    BEHAVIOR_EXPRESSION_LEVEL = "behavior_expression_level"
    START_TIME = "start_time"
    END_TIME = "end_time"
    LEVEL = "level"
    COUNT = "count"
    POINT_MIN = "point_min"
    IS_ACTIVATE = "is_activate"
    EVALUATE_TIME_START = "evaluate_time_start"
    EVALUATE_TIME_END = "evaluate_time_end"
    SESSION_ID = "session_id"
    PASK_CODE = "pask_code"
    REFERENCE_INFO = "reference_info"
    DRAFT_ID = "draft_id"
    UPDATED_TIME = "updated_time"
    EXIST_DRAFT = "exist_draft"
    TYPE = "type"


class RoleKey:
    NAME = "name"
    DESCRIPTION = "description"


class PermissionKey:
    ROLE_ID = "role_id"
    NAME = "name"
    DESCRIPTION = "description"
    ACTION = "action"
    SCOPE = "scope"


class ParamKey:
    CODE = "code"
    EXPORT_TYPE = "export_type"


class BodyKey:
    USER_FILTERS = "user_filters"
    SEARCH = "search"


class LarkKey:
    OPEN_ID = "open_id"
    USER_ID = "user_id"
    OPEN_DEPARTMENT_ID = "open_department_id"


class EmploymentTypeKey:
    EMPLOYMENT_TYPE_ID = "employment_type_id"
    VI_NAME = "vi_name"
    EN_NAME = "en_name"
    DESCRIPTION = "description"


class CompanyKey:
    AVATAR = "avatar"
    NAME = "name"
    USERS_COUNT = "users_count"
    DEPARTMENTS_COUNT = "departments_count"


class AuthorizationKey:
    TOKEN = "token"
    USER_ID = "user_id"
    ROLE = "role"
    PERMISSION = "permission"
    AUTHORIZATION = "Authorization"
    BEARER = "Bearer"
    ACCESS_TOKEN = "access_token"


class EvaluateKey:
    ID = "_id"
    STATUS = "status"
    USER_ID = "user_id"
    IS_DRAFT = "is_draft"
    USER_TYPE = "user_type"
    START_TIME = "start_time"
    END_TIME = "end_time"
    SUBMIT_TIME = "submit_time"
    EVALUATE_PERIOD_ID = "evaluate_period_id"
    COMPETENCY_FRAMEWORK_ID = "competency_framework_id"
    BEFORE_JOB_TITLE_LEVEL_ID = "before_job_title_level_id"
    AFTER_JOB_TITLE_LEVEL_ID = "after_job_title_level_id"
    USER_EVALUATE_JOB_TITLE_LEVEL_ID = "user_evaluate_job_title_level_id"
    LEADER_EVALUATE_JOB_TITLE_LEVEL_ID = "leader_evaluate_job_title_level_id"
    BEFORE_JOB_TITLE_LEVEL = "before_job_title_level"
    AFTER_JOB_TITLE_LEVEL = "after_job_title_level"
    COMPETENCY_GROUPS = "competency_groups"
    LST_COMPETENCY = "lst_competency"
    AMOUNT_OF_WORK_NOTE = "amount_of_work_note"
    WORK_QUALITY_NOTE = "work_quality_note"
    USER_NOTE = "user_note"
    HR_REVIEW = "hr_review"
    LEADER_REVIEW = "leader_review"
    WORK_PROCESS_NOTE = "work_process_note"
    STATUS_FILTER = "status_filter"
    TIME_EVAL_OF_USERS = "time_eval_of_users"
    TYPE_OF_REVIEW = "type_of_review"
    POINTS = "points"
    POINT = "point"
    COUNT = "count"
    NAME = "name"
    WEIGHT = "weight"
    POINT_MIN = "point_min"
    COMPETENCY_GROUP_ID = "competency_group_id"
    COMPETENCY_ID = "competency_id"
    BEHAVIOR_EXPRESSIONS = "behavior_expressions"
    COMPETENCY_GROUP_NAME = "Nhóm năng lực"
    COMPETENCY_NAME = "Năng lực"
    USER_POINT = "user_point"
    LEADER_POINT = "leader_point"
    POINT_MIN_OF_LEVEL = "point_min_of_level"
    POINT_ACHIEVED_BY_USER = "point_achieved_by_user"
    POINT_ACHIEVED_BY_LEADER = "point_achieved_by_leader"
    GAP = "gap"
    DISABLE_EDIT = "disable_edit"
    JOB_TITLE_LEVELS = "job_title_levels"
    JOB_TITLE_LEVEL_STATUS = "job_title_level_status"

    DESCRIPTION = "description"
    PASK_CODE = "pask_code"
    REFERENCE_INFO = "reference_info"

    # response
    EVALUATE_PERIOD = "evaluate_period"
    EVALUATE = "evaluate"
    JOB_TITLE = "job_title"
    JOB_TITLE_LEVEL = "job_title_level"
    DEPARTMENT = "department"
    USER_NAME = "user_name"
    USER_AVATAR = "user_avatar"
    DEPARTMENT_ID = "department_id"
    DEPARTMENT_NAME = "department_name"
    LEADER_ID = "leader_id"
    LEADER_NAME = "leader_name"
    LEADER_AVATAR = "leader_avatar"
    COLOR = "color"


class MessagePushSyncEvaluateKey:
    EVALUATE_ID = "evaluate_id"
    USER_OPEN_ID = "user_open_id"
    LEADER_OPEN_ID = "leader_open_id"
    EVALUATION_DATA = "evaluation_data"
    NAME = "name"
    COMPETENCY_GROUP_NAME = "competency_group_name"


class EvaluatePeriodKey:
    ID = "_id"
    COMPANY_ID = "company_id"
    START_TIME = "start_time"
    END_TIME = "end_time"
    STATUS = "status"
    NAME = "name"
    REPEAT_TYPE = "repeat_type"
    TIMES = "times"
    CONFIG = "config"
    EXCLUDE_DEPARTMENT_IDS = "exclude_department_ids"
    SPECIFIC_DEPARTMENT_IDS = "specific_department_ids"
    EMPLOYEE_EVALUATE_INTERVAL_DAY = "employee_evaluate_interval_day"
    LEADER_EVALUATE_INTERVAL_DAY = "leader_evaluate_interval_day"
    START_TIME_AGGREGATE_PERFORMANCE = "start_time_aggregate_performance"
    END_TIME_AGGREGATE_PERFORMANCE = "end_time_aggregate_performance"
    TYPE = "type"
    MONTH_REVIEW = "month_review"


class EvaluatePeriodEnum:
    class RepeatType:
        ONCE = "once"
        YEARLY = "yearly"

    class Type:
        NUMBER_MONTH_SIX = "number_month_six"
        NUMBER_MONTH_TWELVE = "number_month_twelve"


class LarkDocsTemplateEnum:

    class Type:
        BITABLE = "bitable"
        COMPETENCY_FRAMEWORK = "competency_framework"

    class Key:
        class Bitable:
            DETAIL_EVALUATION = "detail_evaluation"  # Đánh giá năng lực định kỳ
            EVALUATION_ALL_USER = "evaluation_all_user"  # Đánh giá năng lực định kỳ all member


class PaskEnum:
    """
    Code nhóm năng lực theo tiêu chí PASK
        personality :: P (Personality - Tố chất con người)
        attitude :: A (Attitude - Thái độ)
        skill :: S (Skill - Kỹ năng)
        knowledge :: K (Knowledge - Kiến thức)
    """

    class Code:
        PERSONALITY = "personality"
        ATTITUDE = "attitude"
        SKILL = "skill"
        KNOWLEDGE = "knowledge"

        @classmethod
        def get_list_allow(cls):
            return [cls.PERSONALITY, cls.ATTITUDE, cls.SKILL, cls.KNOWLEDGE]


class EvaluateTimeKey:
    ID = "_id"
    START_TIME = "start_time"
    END_TIME = "end_time"
    EVALUATE_PERIOD_ID = "evaluate_period_id"
    TIMES = "times"
    START_TIME = "start_time"
    END_TIME = "end_time"
    DEPARTMENT_ID = "department_id"
    IS_PROCESSED_EVALUATE_USER = "is_processed_evaluate_user"


class JobLevelStatusKey:
    JOB_LEVEL_STATUS_INCREASE = "JOB_LEVEL_STATUS_INCREASE"
    JOB_LEVEL_STATUS_DECREASE = "JOB_LEVEL_STATUS_DECREASE"
    JOB_LEVEL_STATUS_SAME = "JOB_LEVEL_STATUS_SAME"


class RedisSubscriberChannel:
    SEND_BOT_MESSAGE = "send_bot_message_channel"

    SYNC_BITABLE_DETAIL_EVALUATION_CHANNEL = "sync_bitable_detail_evaluation_channel"
    SYNC_BITABLE_EVALUATION_ALL_USER_CHANNEL = "sync_bitable_evaluation_all_user_channel"
    SYNC_BITABLE_COMPETENCY_FRAMEWORK_CHANNEL = "sync_bitable_competency_framework_channel"
