from enum import Enum


class PatternsChoice:
    MOBIO_EMAIL_PATTERN = r"^[\w\.-]+@mobio\.io$"


class StatusChoice(Enum):
    ACTIVATE = 1
    DEACTIVATE = 2
    DRAFT = 3

    SUCCESS = 1
    FAIL = 2
    INSERT_WAITING = 3
    UPDATE_WAITING = 4
    DELETE_WAITING = 5

    EVALUATE_COMPLETED = 1
    EVALUATE_DRAFT = 2
    EVALUATE_WAITING = 3


class TimeConfigChoice:
    UTC = "utc"


class RoleChoice:
    ADMIN = "admin"
    USER = "user"
    LEADER = "leader"


class TypeChoice:
    OBJECT = "object"
    STRING = "string"
    NUMBER = "number"


class QueryParamChoice:
    PAGE = "page"
    PER_PAGE = "per_page"
    SEARCH = "search"
    SKIP = "skip"
    LIMIT = "limit"
    SORT = "sort"
    ORDER = "order"
    DEPARTMENT_IDS = "department_ids"
    JOB_TITLE_IDS = "job_title_ids"
    JOB_TITLE_LEVEL_IDS = "job_title_level_ids"
    COMPANY_ID = "company_id"
    DISPLAY = "display"
    EVALUATE_PERIOD_IDS = "evaluate_period_ids"
    START_TIME = "start_time"
    END_TIME = "end_time"
    EVALUATE_PERIOD_ID = "evaluate_period_id"


class BehaviorExpressionLevelChoice(Enum):
    LEVEL_1 = 1
    LEVEL_2 = 2
    LEVEL_3 = 3
    LEVEL_4 = 4
    LEVEL_5 = 5


class JobTitleLevelChoice(Enum):
    LEVEL_1 = 1
    LEVEL_2 = 2
    LEVEL_3 = 3
    LEVEL_4 = 4
    LEVEL_5 = 5


class DepartmentChoice(Enum):
    BOD = "bod"


class RoleChoice(Enum):
    ADMIN = "admin"
    USER = "user"
    LEADER = "leader"


class LanguageChoice(Enum):
    VI = "vi"
    EN = "en"


class LarkDocsTemplateTypeChoice(str, Enum):
    bitable = "bitable"


class OperatorChoice(Enum):
    OP_IS_IN = "op_is_in"  # giá trị trong mảng
    OP_IS_EQ = "op_is_eq"  # giá trị bằng
    OP_IS_RE = "op_is_re"  # giá trị khớp
    OP_IS_LT = "op_is_lt"  # giá trị nhỏ hơn
    OP_IS_GE = "op_is_ge"  # giá trị lớn hơn
    OP_IS_PE = "op_is_pe"  # giá trị trong khoảng


class CompetencyFrameworkStatusChoice(Enum):
    ACTIVE = 1
    INACTIVE = 0
    DRAFT = 2
    ONLY_ROLE_MEMBER = 3
    DRAFT_DELETE = 4


class TypeCompetencyFrameworkChoice(str, Enum):
    ORIGIN = "origin"
    DRAFT = "draft"
    DRAFT_DELETE = "draft_delete"


class EvaluateStatusChoice(Enum):
    COMPLETED = 1
    # DRAFT = 2
    WAITING = 3


class UserEvaluateTypeChoice(Enum):
    OWNER = 1
    LEADER = 2
    HR = 3
    ASSIGN = 4
    OTHER = 5


class EvaluatePeriodStatusChoice(Enum):
    ACTIVE = 1
    INACTIVE = 0
    DRAFT = 2


class StatusSendReviewFrameworkToLeaderChoice(Enum):
    NOT_SEND = 0
    SENDING = 1
    SENT = 2


class StatusSendReviewFrameworkToUserChoice(Enum):
    NOT_SEND = 0
    SENDING = 1
    SENT = 2


class EvaluateStatusFilterChoice(Enum):
    WAITING_USER = "waiting_user"
    WAITING_LEADER = "waiting_leader"
    COMPLETED = "completed"
