#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 15/01/2024
"""

import logging
from logging.config import dictConfig

from src.utils.logging import CustomLogging


class CustomBaseException(Exception):
    logger_name = "CustomBaseException"
    dictConfig(CustomLogging(logger_name=logger_name).dict())
    logger = logging.getLogger(logger_name)


class InputParamError(CustomBaseException):
    def __init__(self, message, status_code=422):
        self.message = message
        self.status_code = status_code


class CustomError(CustomBaseException):
    def __init__(self, message, data=None, status_code=413):
        self.message = message
        self.status_code = status_code
        self.data = data


class Unauthorized(CustomBaseException):
    def __init__(self, message, status_code=401):
        self.status_code = status_code
        self.message = message if message else "Unauthorized"


class AccessDenied(CustomBaseException):
    def __init__(self, message, status_code=403):
        self.status_code = status_code
        self.message = message if message else "Access denied"


class ConflictMessage(CustomBaseException):
    def __init__(self, message, status_code=412):
        self.message = message
        self.status_code = status_code


class ServerErrorMessage(CustomBaseException):
    def __init__(self, message, status_code=500):
        self.status_code = status_code
        self.message = message if message else "Server error!"


class CustomException(CustomBaseException):
    def __init__(self, message, status_code=500):
        self.status_code = status_code
        self.message = message if message else "Server error!"
