class ErrorsMessage:
    # User

    # competency
    COMPETENCY_IS_NOT_EXIST = "competency_is_not_exist"
    COMPETENCY_FRAMEWORK_IS_EXIST = "competency_framework_is_exist"
    COMPETENCY_IS_EXIST = "competency_is_exist"

    # competency Group
    COMPETENCY_GROUP_IS_EXIST = "competency_group_is_exist"
    COMPETENCY_GROUP_IS_NOT_EXIST = "competency_group_is_not_exist"
    AUTHENTICATION_REQUIRED = "authentication_is_required"
    COMPETENCY_GROUP_IS_DEFAULT = "competency_group_is_default"
    EXIST_COMPETENCY_IN_GROUP = "exist_competency_in_group"
    COMPETENCY_GROUP_IS_DELETED = "competency_group_is_deleted"

    # Job Title
    JOB_TITLE_IS_EXIST = "job_title_is_exist"
    JOB_TITLE_IS_NOT_EXIST = "job_title_is_not_exist"
    JOB_TITLE_LEVELS_IS_EXCEED = "job_title_levels_is_exceed"
    JOB_TITLE_LEVEL_IS_EXIST = "job_title_level_is_exist"

    # Auth
    EMAIL_NOT_FOUND = "email_not_found"
    EMAIL_IS_NOT_ACTIVATED = "email_is_not_activated"
    TOKEN_EXPIRED = "token_is_expired"
    NOT_PERMISSION = "not_permission"

    # competency Framework
    ONLY_ONE_COMPETENCY_FRAMEWORK = "only_one_competency_framework"
    # competency Framework
    COMPETENCY_FRAMEWORK_IS_NOT_EXIST = "competency_framework_is_not_exist"
    COMPETENCY_FRAMEWORK_IS_NOT_ACTIVE = "competency_framework_is_not_active"
    OVERLAPPING_APPLICATION_PERIOD_AND_TITLE = "overlapping_application_period_and_title"

    # Behavior Expression
    BEHAVIOR_EXPRESSION_IS_NOT_EXIST = "behavior_expression_is_not_exist"
    NOT_MATCH_BEHAVIOR_EXPRESSION_LEVEL_IN_LST_JOB_TITLE_LEVEL_POINT = (
        "not_match_behavior_expression_level_in_lst_job_title_level_point"
    )
    BEHAVIOR_EXPRESSION_LIMIT = "behavior_expression_limit_5"

    # Job Title Level
    JOB_TITLE_LEVEL_IS_NOT_EXIST = "job_title_level_is_not_exist"

    # Department
    DEPARTMENT_IS_NOT_EXIST = "department_is_not_exist"

    CODE_NOT_FOUND = "code_not_found"
    USER_IS_NOT_ENABLED = "user_is_not_enabled"
    USER_IS_NOT_EXIST = "user_is_not_exist"
    JOB_TITLE_LEVELS_IS_USED = "job_title_levels_is_used"
    START_TIME_GREATER_THAN_END_TIME = "start_time_greater_than_end_time"
    EVALUATE_PERIOD_IS_NOT_EXIST = "evaluate_period_is_not_exist"
    EVALUATE_IS_EXIST = "evaluate_is_exist"
    COMPETENCY_FRAMEWORK_IS_IN_PROGRESS = "competency_framework_is_in_progress"
    EVALUATE_IS_NOT_EXIST = "evaluate_is_not_exist"
    COMPETENCY_FRAMEWORK_IS_USED = "competency_framework_is_used"

    # evaluate
    INVALID_EVALUATE_POINT = "invalid_evaluate_point"
    NOT_EDITED = "not_edited"


class SuccessMessage:
    ADD_SUCCESS = "Add success"
    UPDATE_SUCCESS = "Update success"
    DELETE_SUCCESS = "Delete success"
    GET_SUCCESS = "Get success"
    UPSERT_SUCCESS = "Upsert success"
