from pydantic import BaseModel


class CustomLogging(BaseModel):
    """Logging configuration to be set for the server"""

    LOGGER_NAME: str = "Common"
    LOG_LEVEL: str = "DEBUG"

    # Logging config
    version: int = 1
    disable_existing_loggers: bool = False
    handlers: dict = {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stderr",
        },
    }

    def __init__(self, logger_name="Common", *arg, **kwargs):
        super().__init__(*arg, **kwargs)
        self.LOGGER_NAME = logger_name

    def dict(self, **kwargs):
        res = super().dict(**kwargs)
        res["formatters"] = {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": f"%(levelprefix)s %(asctime)s::{self.LOGGER_NAME}::%(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        }
        res["loggers"] = {
            self.LOGGER_NAME: {"handlers": ["default"], "level": self.LOG_LEVEL},
        }
        return res