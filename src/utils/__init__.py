import re



def utf8_to_ascii(text):
    patterns = {
        "[àáảãạăắằẵặẳâầấậẫẩ]": "a",
        "[đ]": "d",
        "[èéẻẽẹêềếểễệ]": "e",
        "[ìíỉĩị]": "i",
        "[òóỏõọôồốổỗộơờớởỡợ]": "o",
        "[ùúủũụưừứ<PERSON>ự]": "u",
        "[ỳýỷỹỵ]": "y",
    }
    if text is None:
        return ""
    output = text
    for regex, replace in patterns.items():
        output = re.sub(regex, replace, output)
        # deal with upper case
        output = re.sub(regex.upper(), replace.upper(), output)
    return output
