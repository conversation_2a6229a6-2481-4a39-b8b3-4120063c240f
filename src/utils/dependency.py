import secrets
from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPBasicCredentials

from configs import ApplicationConfig


def basic_auth(credentials: Annotated[HTTPBasicCredentials, Depends(HTTPBasic())]) -> str:
    current_username_bytes = credentials.username.encode("utf8")
    correct_username_bytes = ApplicationConfig.BASIC_AUTH_USERNAME.encode()
    is_correct_username = secrets.compare_digest(current_username_bytes, correct_username_bytes)
    current_password_bytes = credentials.password.encode("utf8")
    correct_password_bytes = ApplicationConfig.BASIC_AUTH_PASSWORD.encode()
    is_correct_password = secrets.compare_digest(current_password_bytes, correct_password_bytes)

    if not (is_correct_username and is_correct_password):
        credentials = None
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Permission Denied!",
        )
    return credentials.username
