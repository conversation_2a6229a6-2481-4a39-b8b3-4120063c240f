from src.utils.policy_builder import (
    PolicyBuilder,
    build_admin_policy,
    build_department_manager_policy,
    build_read_only_policy,
    build_self_service_policy,
    build_user_manager_policy,
)

# Default policy definitions
DEFAULT_POLICIES = {
    "AdministratorAccess": {
        "name": "AdministratorAccess",
        "description": "Provides full access to all services",
        "document": build_admin_policy(),
        "policy_type": "IDENTITY",
    },
    "ReadOnlyAccess": {
        "name": "ReadOnlyAccess",
        "description": "Provides read-only access to all resources",
        "document": build_read_only_policy(),
        "policy_type": "IDENTITY",
    },
    "UserManagerAccess": {
        "name": "UserManagerAccess",
        "description": "Provides access to manage users",
        "document": build_user_manager_policy(),
        "policy_type": "IDENTITY",
    },
    "DepartmentManagerAccess": {
        "name": "DepartmentManagerAccess",
        "description": "Provides access to manage departments",
        "document": build_department_manager_policy(),
        "policy_type": "IDENTITY",
    },
    "SelfServiceAccess": {
        "name": "SelfServiceAccess",
        "description": "Provides access for users to manage their own profiles",
        "document": build_self_service_policy(),
        "policy_type": "IDENTITY",
    },
    "LeaderAccess": {
        "name": "LeaderAccess",
        "description": "Provides access for team leaders",
        "document": PolicyBuilder("LeaderAccess", "Access for team leaders")
        .allow(
            ["user:List", "user:Read", "user:Update", "department:List", "department:Read"],
            ["arn:app:user:*", "arn:app:department:*"],
            # Only for users in their department
            {"StringEquals": {"user:department_id": "${department:id}"}},
        )
        .build(),
        "policy_type": "IDENTITY",
    },
    "PolicyManagerAccess": {
        "name": "PolicyManagerAccess",
        "description": "Provides access to manage policies",
        "document": PolicyBuilder("PolicyManagerAccess", "Access to manage policies")
        .allow(
            ["policy:*", "role:List", "role:Read", "user:List", "user:Read"],
            ["arn:app:policy:*", "arn:app:role:*", "arn:app:user:*"],
        )
        .build(),
        "policy_type": "IDENTITY",
    },
    "RoleManagerAccess": {
        "name": "RoleManagerAccess",
        "description": "Provides access to manage roles",
        "document": PolicyBuilder("RoleManagerAccess", "Access to manage roles")
        .allow(
            ["role:*", "policy:List", "policy:Read", "user:List", "user:Read"],
            ["arn:app:role:*", "arn:app:policy:*", "arn:app:user:*"],
        )
        .build(),
        "policy_type": "IDENTITY",
    },
}


# Policy templates for specific services
def create_service_admin_policy(service_name: str, description: str = None) -> dict:
    """
    Create an admin policy for a specific service.

    Args:
        service_name: The name of the service (e.g., "user", "department")
        description: Optional custom description

    Returns:
        dict: Policy definition
    """
    if not description:
        description = f"Provides full access to the {service_name} service"

    return {
        "name": f"{service_name.capitalize()}AdminAccess",
        "description": description,
        "document": PolicyBuilder(f"{service_name.capitalize()}AdminAccess", description)
        .allow(f"{service_name}:*", f"arn:app:{service_name}:*")
        .build(),
        "policy_type": "IDENTITY",
    }


def create_service_read_only_policy(service_name: str, description: str = None) -> dict:
    """
    Create a read-only policy for a specific service.

    Args:
        service_name: The name of the service (e.g., "user", "department")
        description: Optional custom description

    Returns:
        dict: Policy definition
    """
    if not description:
        description = f"Provides read-only access to the {service_name} service"

    return {
        "name": f"{service_name.capitalize()}ReadOnlyAccess",
        "description": description,
        "document": PolicyBuilder(f"{service_name.capitalize()}ReadOnlyAccess", description)
        .allow(
            [f"{service_name}:List", f"{service_name}:Read", f"{service_name}:View", f"{service_name}:Describe"],
            f"arn:app:{service_name}:*",
        )
        .build(),
        "policy_type": "IDENTITY",
    }
