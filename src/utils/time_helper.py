from datetime import datetime, timedelta
import zoneinfo

import pytz
from dateutil.parser import parse

from src.common.choices import TimeConfigChoice


def get_time_now(timezone=TimeConfigChoice.UTC, is_iso_string=False):
    datetime_now = datetime.now(pytz.timezone(timezone))
    if is_iso_string:
        return datetime_now.isoformat()
    return datetime_now


def time_minutes_delta(start=datetime.now(), minutes=0):
    time = start + timedelta(minutes=minutes)
    return time


def get_timestamp(timezone=TimeConfigChoice.UTC):
    data = datetime.now(pytz.timezone(timezone)).timestamp()
    return int(data) * 1000


def to_as_timezone(time, timezone=TimeConfigChoice.UTC, is_str=False):
    if not time:
        return None
    
    if is_str:
        return parse(time).astimezone(pytz.timezone(timezone))
    return time.astimezone(pytz.timezone(timezone))


def to_time_at(timestamp, timezone=TimeConfigChoice.UTC):
    return datetime.fromtimestamp(timestamp, pytz.timezone(timezone))


def time_str_to_datetime(time_str, timezone=TimeConfigChoice.UTC):
    if not isinstance(time_str, str):
        return time_str
    try:
        return datetime.strptime(time_str, "%Y-%m-%dT%H:%M:%S.%fZ").astimezone(pytz.timezone(timezone))
    except ValueError:
        # Xử lý trường hợp ngày không hợp lệ
        try:
            # Phân tích định dạng chuỗi thời gian
            parts = time_str.split('T')
            date_part = parts[0]
            time_part = parts[1] if len(parts) > 1 else "00:00:00.000Z"
            
            # Lấy năm và tháng
            year_month = date_part.rsplit('-', 1)[0]
            year = int(year_month.split('-')[0])
            month = int(year_month.split('-')[1])
            
            # Tính ngày cuối cùng trong tháng
            if month in [4, 6, 9, 11]:
                last_day = 30
            elif month == 2:
                # Kiểm tra năm nhuận
                if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                    last_day = 29
                else:
                    last_day = 28
            else:
                last_day = 31
            
            # Tạo chuỗi thời gian mới với ngày hợp lệ
            valid_date_str = f"{year_month}-{last_day}T{time_part}"
            return datetime.strptime(valid_date_str, "%Y-%m-%dT%H:%M:%S.%fZ").astimezone(pytz.timezone(timezone))
        except Exception as e:
            # Trong trường hợp lỗi, thử dùng parse từ dateutil
            try:
                dt = parse(time_str)
                return dt.astimezone(pytz.timezone(timezone))
            except Exception as e:
                print(f"Error parsing date: {time_str}, error: {str(e)}")
                return None


def to_time_offset_aware(time, timezone="UTC"):
    zone = zoneinfo.ZoneInfo(timezone)
    return time.replace(tzinfo=zone)
