from src.common.common import RedisSubscriberChannel
from src.redis.pubsub import publisher
from src.redis.pubsub.subscribers.send_bot_message_subscriber import SendBotMessageSubscriber


def send_simple_text(
    open_ids=[],
    department_ids=[],
    user_ids=[],
    union_ids=[],
    title="Ladder",
    message_text="",
    extra_payload={},
    send_type=SendBotMessageSubscriber.SendType.SEND_BATCH_MESSAGES,
):
    message = {
        "send_type": send_type,
        "payload": {
            "open_ids": open_ids,
            "department_ids": department_ids,
            "user_ids": user_ids,
            "union_ids": union_ids,
            "msg_type": "interactive",
            "card": {
                "config": {"wide_screen_mode": True},
                "elements": [
                    {
                        "tag": "div",
                        "text": {"content": f"{message_text}", "tag": "lark_md"},
                    }
                ],
                "header": {
                    "template": "blue",
                    "title": {"content": title, "tag": "plain_text"},
                },
            },
            **extra_payload,
        },
    }
    publisher.publish_message(channel=RedisSubscriberChannel.SEND_BOT_MESSAGE, message=message)


if __name__ == "__main__":
    send_simple_text(
        open_ids=["ou_f41940eebe37ccc2ce0c75fc1ea606b2"],
        message_text="Hello, world!",
        title="Test",
    )
