from typing import Dict, List, Optional, Union


class PolicyBuilder:
    """
    Utility class to build AWS IAM-style policy documents.
    """

    def __init__(self, policy_name: str, description: str = ""):
        self.policy_name = policy_name
        self.description = description
        self.statements = []

    def add_statement(
        self,
        effect: str,
        actions: Union[str, List[str]],
        resources: Union[str, List[str]],
        conditions: Optional[Dict] = None,
    ):
        """
        Add a statement to the policy.

        Args:
            effect: "Allow" or "Deny"
            actions: List of actions or a single action string
            resources: List of resources or a single resource string
            conditions: Optional conditions for the statement
        """
        # Validate inputs
        if effect not in ["Allow", "Deny"]:
            raise ValueError("Effect must be 'Allow' or 'Deny'")

        # Convert single strings to lists
        if isinstance(actions, str):
            actions = [actions]

        if isinstance(resources, str):
            resources = [resources]

        # Create the statement
        statement = {"Effect": effect, "Action": actions, "Resource": resources}

        # Add conditions if provided
        if conditions:
            statement["Condition"] = conditions

        self.statements.append(statement)
        return self

    def allow(
        self, actions: Union[str, List[str]], resources: Union[str, List[str]], conditions: Optional[Dict] = None
    ):
        """Shorthand for add_statement with Allow effect"""
        return self.add_statement("Allow", actions, resources, conditions)

    def deny(self, actions: Union[str, List[str]], resources: Union[str, List[str]], conditions: Optional[Dict] = None):
        """Shorthand for add_statement with Deny effect"""
        return self.add_statement("Deny", actions, resources, conditions)

    def build(self) -> Dict:
        """
        Build the final policy document.

        Returns:
            Dict: The complete policy document
        """
        return {"Version": "2023-01-01", "Id": f"{self.policy_name}-policy", "Statement": self.statements}


# Common policy builders
def build_admin_policy() -> Dict:
    """
    Create a policy that grants administrator access.
    """
    return PolicyBuilder("AdministratorAccess", "Provides full access to all services").allow("*", "*").build()


def build_read_only_policy() -> Dict:
    """
    Create a policy that grants read-only access.
    """
    return (
        PolicyBuilder("ReadOnlyAccess", "Provides read-only access to all resources")
        .allow(["*:List*", "*:Get*", "*:Describe*", "*:View*", "*:Read*"], "*")
        .build()
    )


def build_user_manager_policy() -> Dict:
    """
    Create a policy for managing users.
    """
    return (
        PolicyBuilder("UserManagerAccess", "Provides access to manage users")
        .allow(["user:*", "role:List", "role:Read"], "arn:app:user:*")
        .build()
    )


def build_department_manager_policy() -> Dict:
    """
    Create a policy for managing departments.
    """
    return (
        PolicyBuilder("DepartmentManagerAccess", "Provides access to manage departments")
        .allow(["department:*", "user:List", "user:Read"], ["arn:app:department:*", "arn:app:user:*"])
        .build()
    )


def build_self_service_policy() -> Dict:
    """
    Create a policy that allows users to manage their own profiles.
    """
    return (
        PolicyBuilder("SelfServiceAccess", "Provides access for users to manage their own profiles")
        .allow(["user:Read", "user:Update"], "arn:app:user:${aws:userid}")
        .build()
    )
