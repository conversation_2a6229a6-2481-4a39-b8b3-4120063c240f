from fastapi import status
from sqlalchemy.orm import Session
from starlette.requests import Request

from configs.database import get_db
from src.auth.encrypt import decode_oauth2_token
from src.common.choices import RoleChoice
from src.common.common import AuthorizationKey, UserKey
from src.common.custom_exception import Unauthorized
from src.common.message import ErrorsMessage
from src.redis.caching.base_caching import BaseCachingRedis
from src.repositories.users_repository import UserRepository


class Authentication:
    
    async def get_user_and_roles(self, request: Request):
        return await self._get_user_and_roles(request)
    
    @classmethod
    async def _get_user_and_roles(cls, request: Request):
        session_db: Session = next(get_db())
        user_repo: UserRepository = UserRepository(session_db)

        authorization = request.headers.get(AuthorizationKey.AUTHORIZATION)
        if not authorization:
            raise Unauthorized(ErrorsMessage.AUTHENTICATION_REQUIRED)

        if authorization.startswith(f"{AuthorizationKey.BEARER} "):
            authorization = authorization.replace(f"{AuthorizationKey.BEARER} ", "")

        payload_decode = await decode_oauth2_token(authorization)
        primary_email = payload_decode.get(UserKey.PRIMARY_EMAIL)
        roles = payload_decode.get(UserKey.ROLES)

        user = await user_repo.get_one_by_primary_email(primary_email)
        if not user:
            raise Unauthorized(ErrorsMessage.AUTHENTICATION_REQUIRED)

        return user, roles

    @classmethod
    async def check_authentication(cls, request: Request):
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            raise Unauthorized(status_code=status.HTTP_401_UNAUTHORIZED, message="Token is invalid")

        token = authorization.replace("Bearer", "")
        token = token.strip()
        signature = token.split(".")[2]
        if not BaseCachingRedis().get_value_by_key_not_hash(signature):
            raise Unauthorized(status_code=status.HTTP_401_UNAUTHORIZED, message="Token is invalid")

        return True

    @classmethod
    async def revoke_token(cls, access_token):
        token = access_token.replace("Bearer", "")
        token = token.strip()
        signature = token.split(".")[2]
        BaseCachingRedis().delete_cache_by_key(signature)
        return True

    @classmethod
    async def check_admin(cls, request: Request):
        _, roles = await cls._get_user_and_roles(request)

        if not RoleChoice.ADMIN.value in roles:
            raise Unauthorized(ErrorsMessage.NOT_PERMISSION, 403)

    @classmethod
    async def check_leader(cls, request: Request):
        _, roles = await cls._get_user_and_roles(request)

        if not RoleChoice.LEADER.value in roles:
            raise Unauthorized(ErrorsMessage.NOT_PERMISSION, 403)

    @classmethod
    async def check_leader_admin_role(cls, request: Request):
        _, roles = await cls._get_user_and_roles(request)

        if (not RoleChoice.LEADER.value in roles) and (not RoleChoice.ADMIN.value in roles):
            raise Unauthorized(ErrorsMessage.NOT_PERMISSION, 403)

    @classmethod
    async def check_authorization(cls, request: Request):
        await cls.check_admin(request)


# Backward compatibility functions - these will delegate to the class methods
async def get_user_and_roles(request: Request):
    return await Authentication._get_user_and_roles(request)


async def check_authentication(request: Request):
    return await Authentication.check_authentication(request)


async def revoke_token(access_token):
    return await Authentication.revoke_token(access_token)


async def check_admin(request: Request):
    await Authentication.check_admin(request)


async def check_leader(request: Request):
    await Authentication.check_leader(request)


async def check_leader_admin_role(request: Request):
    await Authentication.check_leader_admin_role(request)


async def check_authorization(request: Request):
    await Authentication.check_authorization(request)
