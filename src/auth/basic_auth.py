import base64
from typing import <PERSON><PERSON>

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from configs.database import get_db
from src.repositories.users_repository import UserRepository

security = HTTPBasic(auto_error=False)


async def get_basic_auth_credentials(request: Request) -> Tuple[str, str]:
    """
    Extract Basic Authentication credentials from the request.

    Args:
        request: The FastAPI request object

    Returns:
        Tuple of (username, password)

    Raises:
        HTTPException: If authentication credentials are missing or invalid
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Basic "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing Basic Authentication",
            headers={"WWW-Authenticate": "Basic"},
        )

    try:
        # Extract and decode the base64 credentials
        encoded_credentials = auth_header.split(" ")[1]
        decoded = base64.b64decode(encoded_credentials).decode("utf-8")
        username, password = decoded.split(":")
        return username, password
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid Basic Authentication credentials",
            headers={"WWW-Authenticate": "Basic"},
        )


async def basic_auth(request: Request, session: Session = Depends(get_db)) -> dict:
    """
    Dependency for Basic Authentication.

    Args:
        request: The FastAPI request object
        session: Database session

    Returns:
        User object if authentication is successful

    Raises:
        HTTPException: If authentication fails
    """
    username, password = await get_basic_auth_credentials(request)

    if username != "admin" or password != "vn/0?VQv_511":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    return {}
