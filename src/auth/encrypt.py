import jwt
from argon2 import <PERSON><PERSON><PERSON>asher
from mobio.libs.logging import <PERSON><PERSON><PERSON>ogging

from configs import ApplicationConfig
from src.redis.caching.base_caching import BaseCachingRedis

ph = PasswordHasher()


async def hashed_password(password):
    return ph.hash(password)


async def verify_hashed_password(password, hashed_password):
    return ph.verify(hashed_password, password)


async def verify_password(plain_password, hashed_password):
    return ph.verify(hashed_password, plain_password)


def generate_oauth2_token(payload):
    token = jwt.encode(payload=payload, key=ApplicationConfig.SECRET_KEY, algorithm=ApplicationConfig.ALGORITHM)
    split_token = token.split(".")
    signature = split_token[2]
    MobioLogging().info(f"generate_oauth2_token :: signature :: {signature}")
    BaseCachingRedis().set_value_by_key_not_hash(signature, "OK", ApplicationConfig.ACCESS_TOKEN_EXPIRE_SECONDS)
    # payload.update({CommonKey.EXP: expire})
    return token


async def decode_oauth2_token(token):
    return jwt.decode(jwt=token, key=ApplicationConfig.SECRET_KEY, algorithms=[ApplicationConfig.ALGORITHM])
