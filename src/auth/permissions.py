from functools import wraps

from fastapi import Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.middleware.authorization import authorize


def requires_permission(action: str, resource: str, context_builder=None):
    """
    Decorator to require a specific permission for an endpoint.

    Args:
        action: The action being performed (e.g., "user:List")
        resource: The resource ARN being accessed (e.g., "arn:app:user:*")
        context_builder: Optional function that builds additional context from request and params

    Example:
        @app.get("/users/{user_id}")
        @requires_permission("user:Read", "arn:app:user:{user_id}")
        async def get_user(user_id: str):
            ...
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, session: Session = Depends(get_db), **kwargs):
            # Process resource template with path parameters
            processed_resource = resource
            for key, value in kwargs.items():
                if f"{{{key}}}" in processed_resource:
                    processed_resource = processed_resource.replace(f"{{{key}}}", str(value))

            # Build context if provided
            context = {}
            if context_builder:
                context = context_builder(request, *args, **kwargs)

            # Add request payload to context if applicable
            if hasattr(request, "json") and callable(request.json):
                try:
                    context["request_payload"] = await request.json()
                except:
                    pass

            # Authorize the request
            await authorize(request, action, processed_resource, context, session)

            # Call the original function
            return await func(request, *args, **kwargs, session=session)

        return wrapper

    return decorator


# Common permission patterns
def can_list_users():
    return requires_permission("user:List", "arn:app:user:*")


def can_view_user(user_context_builder=None):
    return requires_permission("user:Read", "arn:app:user:{user_id}", user_context_builder)


def can_create_user():
    return requires_permission("user:Create", "arn:app:user:*")


def can_update_user(user_context_builder=None):
    return requires_permission("user:Update", "arn:app:user:{user_id}", user_context_builder)


def can_delete_user(user_context_builder=None):
    return requires_permission("user:Delete", "arn:app:user:{user_id}", user_context_builder)


# Department permissions
def can_list_departments():
    return requires_permission("department:List", "arn:app:department:*")


def can_view_department():
    return requires_permission("department:Read", "arn:app:department:{department_id}")


# Role permissions
def can_manage_roles():
    return requires_permission("role:*", "arn:app:role:*")


# Policy permissions
def can_manage_policies():
    return requires_permission("policy:*", "arn:app:policy:*")
