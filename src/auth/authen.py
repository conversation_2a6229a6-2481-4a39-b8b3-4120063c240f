#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 23/01/2025
"""
from functools import wraps

import jwt
from fastapi import Request
from mobio.libs.logging import MobioLogging

from configs import ApplicationConfig
from src.common.custom_exception import Unauthorized
from src.redis.caching.base_caching import BaseCachingRedis


class Authentication:

    def generate_oauth2_token(self, payload):
        token = jwt.encode(payload=payload, key=ApplicationConfig.SECRET_KEY, algorithm=ApplicationConfig.ALGORITHM)
        split_token = token.split(".")
        signature = split_token[2]
        MobioLogging().info(f"generate_oauth2_token :: signature :: {signature}")
        BaseCachingRedis().set_value_by_key_not_hash(signature, "OK", ApplicationConfig.ACCESS_TOKEN_EXPIRE_SECONDS)
        return token

    def decode_oauth2_token(self, token):
        return jwt.decode(jwt=token, key=ApplicationConfig.SECRET_KEY, algorithms=[ApplicationConfig.ALGORITHM])

    def validate_token_decorator(endpoint):
        @wraps(endpoint)
        async def wrapper(request: Request, *args, **kwargs):
            # Lấy token từ header
            authorization = request.headers.get("Authorization")
            if not authorization or not authorization.startswith("Bearer "):
                raise Unauthorized(status_code=401, message="Token is invalid")

            token = authorization.replace("Bearer", "")
            token = token.strip()
            
            signature = token.split(".")[2]
            if not BaseCachingRedis().get_value_by_key_not_hash(signature):
                raise Unauthorized(status_code=401, message="Token is invalid")

            return await endpoint(request, *args, **kwargs)

        return wrapper
