#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 10/01/2024
"""

from h11 import Request

from src.controllers.log_user_controller import LogUserController
from src.routers import AppSettings

app_settings = AppSettings()


def get_application():
    from fastapi import FastAPI
    from fastapi.exception_handlers import http_exception_handler
    from starlette.middleware.cors import CORSMiddleware

    from src.common.custom_exception import (
        AccessDenied,
        ConflictMessage,
        CustomError,
        CustomException,
        InputParamError,
        ServerErrorMessage,
        Unauthorized,
    )
    from src.middleware.handle_response import (
        http_access_denied_handler,
        http_authorized_handler,
        http_conflict_message_handler,
        http_custom_error_handler,
        http_input_param_handler,
        http_server_error_handler,
    )
    from src.routers.init_routers import api_v1_0, base_api

    app = FastAPI(**app_settings.dict(), docs_url=None, redoc_url=None, openapi_url=None)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=app_settings.allowed_hosts,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Thêm func xử lý exception
    app.add_exception_handler(ConflictMessage, http_conflict_message_handler)
    app.add_exception_handler(CustomError, http_custom_error_handler)
    app.add_exception_handler(InputParamError, http_input_param_handler)
    app.add_exception_handler(ServerErrorMessage, http_server_error_handler)
    app.add_exception_handler(Unauthorized, http_authorized_handler)
    app.add_exception_handler(AccessDenied, http_access_denied_handler)
    app.add_exception_handler(CustomException, http_exception_handler)

    app.include_router(router=base_api)
    app.include_router(api_v1_0)

    return app


app = get_application()


@app.middleware("http")
async def log_user_access(request: Request, call_next):
    response = await call_next(request)
    await LogUserController().log_admin_access(request, response)
    return response


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app_web:app", host="0.0.0.0", port=8001, reload=True, loop="asyncio")
