import sys

if __name__ == "__main__":
    name_service = sys.argv[1]
    if name_service == "update-status-for-competency-framework":
        from src.cronjobs.handler_system_update_status_for_competency_framework_cronjob import (
            HandlerUpdateStatusForCompetencyFramework,
        )

        HandlerUpdateStatusForCompetencyFramework().owner_do()

    if name_service == "sync-data-from-lark":
        from src.cronjobs.handler_migrate_sync_data_from_lark_to_ladder import (
            HandlerSyncDataFromLark,
        )

        HandlerSyncDataFromLark().owner_do()

    if name_service == "auto-generate-evaluate":
        from src.cronjobs.handler_system_auto_generate_evaluate_cronjob import (
            HandlerAutoGenerateEvaluate,
        )

        HandlerAutoGenerateEvaluate().owner_do()

    if name_service == "sync-task-performance":
        from src.cronjobs.handler_sync_task_performance_cronjob import (
            HandlerSyncLarkbasePerformanceData,
        )

        HandlerSyncLarkbasePerformanceData().owner_do()

    if name_service == "push-notification-to-user":
        from src.cronjobs.handler_push_notification_to_user import (
            HandlerPushNotificationToUser,
        )

        HandlerPushNotificationToUser().owner_do()
