FROM BE_BASE_COMPILE_IMAGE as compile-image

WORKDIR /home/<USER>/projects/Ladder
ADD . /home/<USER>/projects/Ladder

RUN find /etc/yum.repos.d/ -type f -name "*.repo" -exec sed -i 's@http://dl.rockylinux.org/$contentdir/$releasever/ResilientStorage/source/tree/@http://************:8081/repository/Rocky-Sources.repo-resilient-storage-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/PowerTools/source/tree/@http://************:8081/repository/Rocky-Sources.repo-powertools-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/AppStream/$basearch/os/@http://************:8081/repository/Rocky-AppStream.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/BaseOS/$basearch/os/@http://************:8081/repository/Rocky-BaseOS.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/Devel/$basearch/os/@http://************:8081/repository/Rocky-Devel.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/extras/$basearch/os/@http://************:8081/repository/Rocky-Extras.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/HighAvailability/$basearch/os/@http://************:8081/repository/Rocky-HighAvailability.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/nfv/$basearch/os/@http://************:8081/repository/Rocky-NFV.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/plus/$basearch/os/@http://************:8081/repository/Rocky-Plus.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/PowerTools/$basearch/os/@http://************:8081/repository/Rocky-PowerTools.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/ResilientStorage/$basearch/os/@http://************:8081/repository/Rocky-ResilientStorage.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/RT/$basearch/os/@http://************:8081/repository/Rocky-RT.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/AppStream/source/tree/@http://************:8081/repository/Rocky-Sources.repo-appstream-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/BaseOS/source/tree/@http://************:8081/repository/Rocky-Sources.repo-baseos-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/extras/source/tree/@http://************:8081/repository/Rocky-Sources.repo-extras-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/HighAvailability/source/tree/@http://************:8081/repository/Rocky-Sources.repo-ha-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/Plus/source/tree/@http://************:8081/repository/Rocky-Sources.repo-plus-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-modular-debug-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-modular.repo-epel-modular-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-modular-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-modular.repo-epel-modular/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-modular-source-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-modular.repo-epel-modular-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-modular-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing-modular.repo-epel-testing-modular/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-modular-debug-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing-modular.repo-epel-testing-modular-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-modular-source-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing-modular.repo-epel-testing-modular-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing.repo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-debug-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing.repo-epel-testing-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-source-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing.repo-epel-testing-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel.repo-epel/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-debug-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel.repo-epel-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-source-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel.repo-epel-source/@g' {} \;
RUN mv /etc/yum.repos.d/epel.repo{,.bak}

RUN echo $'[global] \n\
    index = http://************:8081/repository/pypi-proxy/pypi \n\
    index-url = http://************:8081/repository/pypi-proxy/simple \n\
    trusted-host = ************' > /etc/pip.conf

RUN pip3.11 install -r requirements.txt

# RUN find src/ -type d -exec sh -c 'if [ ! -f {}/__init__.py ]; then touch {}/__init__.py; fi' \;
# RUN python3.11 setup.py build_ext -j 5 --inplace
# RUN find src configs -type f \( -name '*.py' -o -name '*.c' \) -exec rm -f {} \;
# RUN rm -rf ./build

FROM BE_BASE_RUN_IMAGE as run-image
ENV LC_ALL=en_US.UTF-8 \
    LADDER_HOME=/home/<USER>/projects/Ladder \
    LADDER_FOLDER_NAME=Ladder \
    SECRET_KEY=mobio \
    ACCESS_TOKEN_EXPIRE_HOURS=24 \
    BASIC_AUTH_USERNAME=mobio \
    BASIC_AUTH_PASSWORD=Mobio#JQK \
    FAVICON_URL=https://mobio.io/wp-content/themes/mobio/images/favicon.png \
    DATABASE_DEBUG_MODE=false \
    ACCESS_TOKEN_EXPIRE_SECONDS=2592000 \
    PUBLIC_HOST=https://ladder.mobio.io

ENV data_dir=$APPLICATION_DATA_DIR$LADDER_HOME \
    log_dir=$APPLICATION_LOGS_DIR$LADDER_HOME \
    monitor_log_dir=$APPLICATION_LOGS_DIR$LADDER_HOME/monitor_logs/

RUN mkdir -p $data_dir $log_dir $monitor_log_dir

WORKDIR $LADDER_HOME

COPY --from=compile-image $LADDER_HOME $LADDER_HOME
COPY --from=compile-image /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages

RUN find /etc/yum.repos.d/ -type f -name "*.repo" -exec sed -i 's@http://dl.rockylinux.org/$contentdir/$releasever/ResilientStorage/source/tree/@http://************:8081/repository/Rocky-Sources.repo-resilient-storage-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/PowerTools/source/tree/@http://************:8081/repository/Rocky-Sources.repo-powertools-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/AppStream/$basearch/os/@http://************:8081/repository/Rocky-AppStream.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/BaseOS/$basearch/os/@http://************:8081/repository/Rocky-BaseOS.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/Devel/$basearch/os/@http://************:8081/repository/Rocky-Devel.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/extras/$basearch/os/@http://************:8081/repository/Rocky-Extras.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/HighAvailability/$basearch/os/@http://************:8081/repository/Rocky-HighAvailability.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/nfv/$basearch/os/@http://************:8081/repository/Rocky-NFV.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/plus/$basearch/os/@http://************:8081/repository/Rocky-Plus.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/PowerTools/$basearch/os/@http://************:8081/repository/Rocky-PowerTools.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/ResilientStorage/$basearch/os/@http://************:8081/repository/Rocky-ResilientStorage.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/RT/$basearch/os/@http://************:8081/repository/Rocky-RT.repo/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/AppStream/source/tree/@http://************:8081/repository/Rocky-Sources.repo-appstream-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/BaseOS/source/tree/@http://************:8081/repository/Rocky-Sources.repo-baseos-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/extras/source/tree/@http://************:8081/repository/Rocky-Sources.repo-extras-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/HighAvailability/source/tree/@http://************:8081/repository/Rocky-Sources.repo-ha-source/@g; s@http://dl.rockylinux.org/$contentdir/$releasever/Plus/source/tree/@http://************:8081/repository/Rocky-Sources.repo-plus-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-modular-debug-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-modular.repo-epel-modular-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-modular-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-modular.repo-epel-modular/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-modular-source-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-modular.repo-epel-modular-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-modular-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing-modular.repo-epel-testing-modular/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-modular-debug-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing-modular.repo-epel-testing-modular-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-modular-source-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing-modular.repo-epel-testing-modular-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing.repo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-debug-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing.repo-epel-testing-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=testing-source-epel8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel-testing.repo-epel-testing-source/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel.repo-epel/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-debug-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel.repo-epel-debuginfo/@g; s@https://mirrors.fedoraproject.org/metalink?repo=epel-source-8&arch=$basearch&infra=$infra&content=$contentdir@http://************:8081/repository/epel.repo-epel-source/@g' {} \;

RUN chmod +x *.sh
CMD tail -f /dev/null